import { ObjectId } from 'mongodb';
import {
    ModuleType,
    ConsentsAndDeclarations,
    CheckboxConsentsAndDeclarations,
    Condition,
    ConsentFeaturePurpose,
    User,
} from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    getAdvancedVersioningByUserForCreation,
    getAdvancedVersioningByUserForUpdate,
} from '../../../../utils/versioning';
import { InvalidInput } from '../../../errors';
import { ConsentsAndDeclarationsType, GraphQLConsentsAndDeclarationsChildInput } from '../../definitions';

export const getConsentAndModuleFromId = async (id: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const consent = await collections.consentsAndDeclarations.findOne({ _id: id, isDeleted: false });

    if (!consent) {
        throw new InvalidInput({ consentId: 'Invalid Consent ID' });
    }

    const consentModule = await collections.modules.findOne({ _id: consent.moduleId });

    if (!consentModule || consentModule._type !== ModuleType.ConsentsAndDeclarations) {
        throw new InvalidInput({ moduleId: 'Invalid Module ID' });
    }

    return { consent, consentModule };
};

export const getConsentAndModuleFromSuiteId = async (suiteId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const consent = await collections.consentsAndDeclarations.findOne({
        '_versioning.suiteId': suiteId,
        '_versioning.isLatest': true,
        isDeleted: false,
    });

    if (!consent) {
        throw new InvalidInput({ consentId: 'Invalid Consent ID' });
    }

    const consentModule = await collections.modules.findOne({ _id: consent.moduleId });

    if (!consentModule || consentModule._type !== ModuleType.ConsentsAndDeclarations) {
        throw new InvalidInput({ moduleId: 'Invalid Module ID' });
    }

    return { consent, consentModule };
};
export const createChildConsent = async ({
    child,
    moduleId,
    parentId,
    user,
    featurePurpose,
    parentConditions,
}: {
    child: GraphQLConsentsAndDeclarationsChildInput;
    moduleId: ObjectId;
    parentId: ObjectId;
    user: User;
    featurePurpose: ConsentFeaturePurpose;
    parentConditions: Condition[];
}): Promise<ConsentsAndDeclarations> => {
    const baseDocument = {
        _id: new ObjectId(),
        moduleId,
        isDeleted: false,
        conditions: parentConditions, // For group children, use parent conditions instead of child conditions
        _versioning: getAdvancedVersioningByUserForCreation(user._id),
        featurePurpose,
        parentId,
    };

    if (child.type !== ConsentsAndDeclarationsType.Checkbox) {
        throw new InvalidInput({ type: 'All children of a group must be of checkbox type' });
    }
    if (!child.checkboxSettings) {
        throw new InvalidInput({ checkboxSettings: 'Checkbox settings required for group children' });
    }

    return {
        ...baseDocument,
        _type: ConsentsAndDeclarationsType.Checkbox,
        ...child.checkboxSettings,
    } as CheckboxConsentsAndDeclarations;
};

export const updateChildConsent = async ({
    child,
    existingConsent,
    user,
    parentConditions,
}: {
    child: GraphQLConsentsAndDeclarationsChildInput;
    existingConsent: ConsentsAndDeclarations;
    user: User;
    parentConditions?: Condition[];
}): Promise<void> => {
    const { collections } = await getDatabaseContext();

    if (child.type !== ConsentsAndDeclarationsType.Checkbox) {
        throw new InvalidInput({ type: 'All children of a group must be of checkbox type' });
    }
    if (!child.checkboxSettings) {
        throw new InvalidInput({ checkboxSettings: 'Checkbox settings required for group children' });
    }

    const updatedDocument: ConsentsAndDeclarations = {
        ...(existingConsent as CheckboxConsentsAndDeclarations),
        ...child.checkboxSettings,
        conditions: parentConditions,
        _versioning: {
            ...existingConsent._versioning,
            ...getAdvancedVersioningByUserForUpdate(user._id),
        },
    };

    await collections.consentsAndDeclarations.replaceOne(
        { '_versioning.suiteId': existingConsent._versioning.suiteId, '_versioning.isLatest': true },
        updatedDocument
    );
};
