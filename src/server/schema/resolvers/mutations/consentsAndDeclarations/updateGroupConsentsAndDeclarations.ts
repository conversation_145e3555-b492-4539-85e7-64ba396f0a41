import { GroupConsentsAndDeclarations, ConsentsAndDeclarations } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { AgreementPolicyAction } from '../../../../permissions';
import { getAdvancedVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { ConsentsAndDeclarationsType, GraphQLMutationResolvers } from '../../definitions';
import { getConsentAndModuleFromSuiteId, createChildConsent, updateChildConsent } from './shared';

const mutation: GraphQLMutationResolvers['updateGroupConsentsAndDeclarations'] = async (
    root,
    { suiteId, settings },
    { getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const user = await getUser();
    const permissionController = await getPermissionController();

    const { consent: groupConsent, consentModule } = await getConsentAndModuleFromSuiteId(suiteId);

    if (groupConsent._type !== ConsentsAndDeclarationsType.Group) {
        throw new InvalidInput({ suiteId: 'Not a group consent' });
    }

    if (!permissionController.agreements.mayOperateOn(groupConsent, AgreementPolicyAction.Update, consentModule)) {
        throw new InvalidPermission();
    }

    const { groupSettings, children } = settings;

    // Validate that all children are checkbox type
    for (const child of children) {
        if (child.type !== ConsentsAndDeclarationsType.Checkbox) {
            throw new InvalidInput({
                children: 'All children of a group must be of checkbox type',
            });
        }
        if (!child.checkboxSettings) {
            throw new InvalidInput({
                children: 'Checkbox settings are required for all group children',
            });
        }
    }

    // Update the group document
    const updatedGroupDocument: GroupConsentsAndDeclarations = {
        ...groupConsent,
        ...groupSettings,
        _versioning: {
            ...groupConsent._versioning,
            ...getAdvancedVersioningByUserForUpdate(user._id),
        },
    };

    await collections.consentsAndDeclarations.replaceOne(
        { '_versioning.suiteId': suiteId, '_versioning.isLatest': true },
        updatedGroupDocument
    );

    // Get existing child consents
    const existingChildren = await collections.consentsAndDeclarations
        .find({
            parentId: groupConsent._id,
            isDeleted: false,
            '_versioning.isLatest': true,
        })
        .toArray();

    // Process child consents
    const processedChildIds = new Set<string>();
    const updatePromises: Promise<void>[] = [];
    const createPromises: Promise<ConsentsAndDeclarations>[] = [];

    // Separate updates and creates
    for (const child of children) {
        if (child.id) {
            // Update existing child
            const existingChild = existingChildren.find(c => c._id.equals(child.id!));
            if (existingChild) {
                updatePromises.push(
                    updateChildConsent({
                        child,
                        existingConsent: existingChild,
                        user,
                        parentConditions: updatedGroupDocument.conditions,
                    })
                );
                processedChildIds.add(child.id.toString());
            }
        } else {
            // Prepare new child for creation
            createPromises.push(
                createChildConsent({
                    child,
                    moduleId: groupConsent.moduleId,
                    parentId: groupConsent._id,
                    user,
                    featurePurpose: groupConsent.featurePurpose,
                    parentConditions: updatedGroupDocument.conditions,
                })
            );
        }
    }

    // Execute all updates and creates in parallel
    const [, newChildDocuments] = await Promise.all([Promise.all(updatePromises), Promise.all(createPromises)]);

    // Insert new children
    if (newChildDocuments.length > 0) {
        await collections.consentsAndDeclarations.insertMany(newChildDocuments);
    }

    // Mark unprocessed children as deleted
    const childrenToDelete = existingChildren.filter(
        existingChild => !processedChildIds.has(existingChild._id.toString())
    );

    if (childrenToDelete.length > 0) {
        await Promise.all(
            childrenToDelete.map(existingChild =>
                collections.consentsAndDeclarations.updateOne(
                    { '_versioning.suiteId': existingChild._versioning.suiteId, '_versioning.isLatest': true },
                    {
                        $set: {
                            isDeleted: true,
                            ...getAdvancedVersioningByUserForUpdate(user._id),
                        },
                    }
                )
            )
        );
    }

    return updatedGroupDocument;
};

export default requiresLoggedUser(mutation);
