import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    GroupConsentsAndDeclarations,
    Condition,
    ConsentFeaturePurpose,
    ConsentFeatureType,
    ModuleType,
} from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { ModulePolicyAction } from '../../../../permissions';
import { getAdvancedVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../middlewares';
import { ConsentsAndDeclarationsType, GraphQLMutationResolvers } from '../../definitions';
import { checkAllConditionSettings } from './checkConditionSettings';
import generateConditions from './generateConditions';
import { createChildConsent } from './shared';

const mutation: GraphQLMutationResolvers['createGroupConsentsAndDeclarations'] = async (
    root,
    { moduleId, settings, conditionSettings, eventId },
    { getPermissionController, getUser }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const module = await collections.modules.findOne({ _id: moduleId });

    if (!module || module._type !== ModuleType.ConsentsAndDeclarations) {
        throw new InvalidInput({ moduleId: 'Invalid Module ID' });
    }

    if (!permissionController.modules.mayOperateOn(module, ModulePolicyAction.CreateAgreement)) {
        throw new InvalidPermission();
    }

    const { groupSettings, children } = settings;

    // Validate that all children are checkbox type
    for (const child of children) {
        if (child.type !== ConsentsAndDeclarationsType.Checkbox) {
            throw new InvalidInput({
                children: 'All children of a group must be of checkbox type',
            });
        }
        if (!child.checkboxSettings) {
            throw new InvalidInput({
                children: 'Checkbox settings are required for all group children',
            });
        }
    }

    // Create conditions for the group
    let conditions: Condition[] = [];

    if (conditionSettings && conditionSettings.length > 0) {
        const isConditionSettingsValid = checkAllConditionSettings(conditionSettings);
        if (!isConditionSettingsValid) {
            throw new InvalidInput({ conditionSettings: 'Invalid Condition Settings' });
        }
        conditions = conditionSettings.map(setting => generateConditions(setting));
    }

    const featurePurpose: ConsentFeaturePurpose = !isNil(eventId)
        ? { type: ConsentFeatureType.Event, featureId: eventId }
        : { type: ConsentFeatureType.Module, featureId: moduleId };

    const user = await getUser();

    // Create the group document
    const groupDocument: GroupConsentsAndDeclarations = {
        _id: new ObjectId(),
        _type: ConsentsAndDeclarationsType.Group,
        moduleId,
        ...groupSettings,
        isDeleted: false,
        conditions,
        _versioning: getAdvancedVersioningByUserForCreation(user._id),
        featurePurpose,
    };

    // Insert the group first
    await collections.consentsAndDeclarations.insertOne(groupDocument);

    // Create child consents with parent conditions
    const childDocuments = await Promise.all(
        children.map(child =>
            createChildConsent({
                child,
                moduleId,
                parentId: groupDocument._id,
                user,
                featurePurpose,
                parentConditions: conditions, // Pass parent conditions to children
            })
        )
    );

    // Insert all child documents
    if (childDocuments.length > 0) {
        await collections.consentsAndDeclarations.insertMany(childDocuments);
    }

    return groupDocument;
};

export default buildRateLimiterMiddleware({ operation: 'createGroupConsentsAndDeclarations' })(
    requiresLoggedUser(mutation)
);
