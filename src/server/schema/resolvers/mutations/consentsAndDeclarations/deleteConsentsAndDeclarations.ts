import { ConsentsAndDeclarationsType } from '../../../../database/documents/ConsentsAndDeclarations';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { AgreementPolicyAction } from '../../../../permissions';
import { getAdvancedVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { getConsentAndModuleFromSuiteId } from './shared';

const mutation: GraphQLMutationResolvers['deleteConsentsAndDeclarations'] = async (
    root,
    { suiteId },
    { getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const user = await getUser();
    const permissionController = await getPermissionController();

    const { consent, consentModule } = await getConsentAndModuleFromSuiteId(suiteId);

    if (!permissionController.agreements.mayOperateOn(consent, AgreementPolicyAction.Delete, consentModule)) {
        throw new InvalidPermission();
    }

    // If this is a group type, also delete all child consents
    if (consent._type === ConsentsAndDeclarationsType.Group) {
        const childConsents = await collections.consentsAndDeclarations
            .find({
                parentId: consent._id,
                isDeleted: false,
                '_versioning.isLatest': true,
            })
            .toArray();

        // Delete all child consents
        if (childConsents.length > 0) {
            await Promise.all(
                childConsents.map(childConsent =>
                    collections.consentsAndDeclarations.updateOne(
                        { '_versioning.suiteId': childConsent._versioning.suiteId, '_versioning.isLatest': true },
                        { $set: { isDeleted: true, ...getAdvancedVersioningByUserForUpdate(user._id) } }
                    )
                )
            );
        }
    }

    const { modifiedCount } = await collections.consentsAndDeclarations.updateOne(
        { '_versioning.suiteId': suiteId, '_versioning.isLatest': true },
        { $set: { isDeleted: true, ...getAdvancedVersioningByUserForUpdate(user._id) } }
    );

    return modifiedCount > 0;
};

export default requiresLoggedUser(mutation);
