import { CompoundValue } from '../../calculator';
import {
    ApplicationStage,
    AppointmentStage,
    AuthorKind,
    Bank,
    CheckboxConsentsAndDeclarations,
    Customer,
    FinanceProductType,
    FinancingStage,
    GroupConsentsAndDeclarations,
    InsuranceStage,
    LeadStage,
    MarketingConsentsAndDeclarations,
    MobilityStage,
    ReservationStage,
    TextConsentsAndDeclarations,
    TradeInStage,
    User,
    VisitAppointmentStage,
    FollowUpStage,
} from '../../database/documents';

export enum AuthenticationStep {
    Successful = 'authenticationSuccessful',
    PasswordChange = 'authenticationWithPasswordChange',
    TOTP = 'authenticationWithTOTP',
    SmsOTP = 'authenticationWithSmsOTP',
}

export enum ImportInventoryResponseType {
    Success = 'importInventorySuccess',
    Fail = 'importInventoryFail',
}

export enum ImportDealerResponseType {
    Success = 'importDealerSuccess',
    Fail = 'importDealerFail',
}

export type AuthenticationResponse =
    | {
          _kind: AuthenticationStep.Successful;
          user: User;
          token: string;
      }
    | {
          _kind: AuthenticationStep.PasswordChange;
          user: User;
          token: string;
      }
    | {
          _kind: AuthenticationStep.TOTP;
          token: string;
      }
    | {
          _kind: AuthenticationStep.SmsOTP;
          token: string;
      };

export type Author =
    | ({
          _authorKind: AuthorKind.User;
      } & User)
    | ({
          _authorKind: AuthorKind.Bank;
      } & Bank)
    | ({
          _authorKind: AuthorKind.Customer;
      } & Customer)
    | {
          _authorKind: AuthorKind.Salesforce;
      }
    | {
          _authorKind: AuthorKind.PorscheRetain;
      };

export type CalculatorResult =
    | {
          monthlyInstalment: number;
          downPayment: CompoundValue;
          loan: CompoundValue;
          type: FinanceProductType.HirePurchase;
      }
    | {
          monthlyInstalment: number;
          downPayment: CompoundValue;
          loan: CompoundValue;
          type: FinanceProductType.HirePurchaseWithBalloon;
      }
    | {
          monthlyInstalments: number[];
          downPayment: CompoundValue;
          loan: CompoundValue;
          type: FinanceProductType.HirePurchaseWithBalloonGFV;
      }
    | {
          monthlyInstalment: number;
          type: FinanceProductType.Lease;
      }
    | {
          monthlyInstalment: number;
          downPayment: CompoundValue;
          loan: CompoundValue;
          type: FinanceProductType.LeasePurchase;
      }
    | {
          monthlyInstalments: number[];
          downPayment: CompoundValue;
          loan: CompoundValue;
          type: FinanceProductType.DeferredPrincipal;
      }
    | {
          monthlyInstalment: number;
          type: FinanceProductType.UCCLLeasing;
          downPayment: CompoundValue;
          loan: CompoundValue;
          monthlyPaymentFixedInterestRate: number;
      };

export type TextApplicationAgreement = TextConsentsAndDeclarations & { isAgreed: boolean };
export type CheckboxApplicationAgreement = CheckboxConsentsAndDeclarations & { isAgreed: boolean };
export type MarketingApplicationAgreement = MarketingConsentsAndDeclarations & {
    isAgreed: boolean;
    platformsAgreed: {
        email: boolean;
        fax: boolean;
        mail: boolean;
        phone: boolean;
        sms: boolean;
    };
};
export type GroupApplicationAgreement = GroupConsentsAndDeclarations & { isAgreed: boolean };
export type ApplicationAgreement =
    | TextApplicationAgreement
    | CheckboxApplicationAgreement
    | MarketingApplicationAgreement
    | GroupApplicationAgreement;

export type ImportInventoryResponse =
    | {
          _kind: ImportInventoryResponseType.Success;
          updatedCount: number;
      }
    | {
          _kind: ImportInventoryResponseType.Fail;
          message: string;
      };

export type ApplicationStageDetails =
    | ({ _kind: ApplicationStage.Lead } & LeadStage)
    | ({ _kind: ApplicationStage.Mobility } & MobilityStage)
    | ({ _kind: ApplicationStage.Reservation } & ReservationStage)
    | ({ _kind: ApplicationStage.Financing } & FinancingStage)
    | ({ _kind: ApplicationStage.Insurance } & InsuranceStage)
    | ({ _kind: ApplicationStage.Appointment } & AppointmentStage)
    | ({ _kind: ApplicationStage.VisitAppointment } & VisitAppointmentStage)
    | ({ _kind: ApplicationStage.TradeIn } & TradeInStage)
    | ({ _kind: ApplicationStage.FollowUp } & FollowUpStage);

export type ImportDealerResponse =
    | {
          _kind: ImportDealerResponseType.Success;
          createdCount: number;
          createdRoleCount: number;
          createdUserGroupCount: number;
      }
    | {
          _kind: ImportDealerResponseType.Fail;
          message: string;
          errors: string[];
      };
