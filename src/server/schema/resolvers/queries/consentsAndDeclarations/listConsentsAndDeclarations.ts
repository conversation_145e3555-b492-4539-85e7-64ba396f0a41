import { Filter, Sort } from 'mongodb';
import { ConsentsAndDeclarations, ConditionType, Event } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { AgreementPolicyAction, PermissionController } from '../../../../permissions';
import { getSortingValue, paginateAggregation } from '../../../../utils/pagination';
import { requiresLoggedUser } from '../../../middlewares';
import {
    ConsentsAndDeclarationsSortingField,
    GraphQLConsentsAndDeclarationsFilteringRule,
    GraphQLConsentsAndDeclarationsSortingRule,
    GraphQLQueryResolvers,
    Maybe,
} from '../../definitions';

const consentModuleLookupPipeline = [
    {
        // module lookup
        $lookup: {
            from: 'modules',
            localField: 'moduleId',
            foreignField: '_id',
            as: 'module',
        },
    },
    {
        $unwind: {
            path: '$module',
            preserveNullAndEmptyArrays: false,
        },
    },
    {
        $lookup: {
            from: 'companies',
            localField: 'module.companyId',
            foreignField: '_id',
            as: 'module.company',
        },
    },
    {
        $unwind: {
            path: '$module.company',
            preserveNullAndEmptyArrays: false,
        },
    },
];

export const getFilter = (
    permissionController: PermissionController,
    rule?: Maybe<GraphQLConsentsAndDeclarationsFilteringRule>
): Filter<ConsentsAndDeclarations> => {
    const filter: Filter<ConsentsAndDeclarations> = {
        $and: [
            { isDeleted: false, '_versioning.isLatest': true },
            { parentId: { $exists: false } }, // Filter out items with parentId (child consents)
            permissionController.agreements.getFilterQueryForAction(AgreementPolicyAction.View),
        ],
    };

    if (!rule) {
        return filter;
    }

    if (rule.displayName) {
        filter.displayName = rule.displayName;
    }

    if (rule.moduleId) {
        filter.moduleId = rule.moduleId;
    }

    if (rule.companyIds?.length) {
        filter['module.companyId'] = { $in: rule.companyIds };
    }

    if (rule?.featurePurpose) {
        filter['featurePurpose.type'] = rule?.featurePurpose?.type;
        if (rule?.featurePurpose?.featureId) {
            filter['featurePurpose.featureId'] = rule?.featurePurpose?.featureId;
        }
    }

    return filter;
};

export const getSort = (rule?: Maybe<GraphQLConsentsAndDeclarationsSortingRule>): Sort => {
    const sort: Sort = { _id: 1 };

    if (!rule) {
        return sort;
    }

    switch (rule.field) {
        case ConsentsAndDeclarationsSortingField.Company:
            return { 'module.company.displayName': getSortingValue(rule.order), ...sort };

        case ConsentsAndDeclarationsSortingField.DisplayName:
            return { displayName: getSortingValue(rule.order), ...sort };

        case ConsentsAndDeclarationsSortingField.OrderNumber:
            return { orderNumber: getSortingValue(rule.order), ...sort };

        case ConsentsAndDeclarationsSortingField.Active:
            return { isActive: getSortingValue(rule.order), ...sort };

        case ConsentsAndDeclarationsSortingField.Mandatory:
            return { isMandatory: getSortingValue(rule.order), ...sort };

        case ConsentsAndDeclarationsSortingField.Type:
            return { _type: getSortingValue(rule.order), ...sort };

        default:
            throw new Error('Sorting field not supported');
    }
};

export const getLookupPipelines = (
    filter?: Maybe<GraphQLConsentsAndDeclarationsFilteringRule>,
    sort?: Maybe<GraphQLConsentsAndDeclarationsSortingRule>
) => {
    const lookupPipelines = [];

    if (filter?.companyIds?.length || sort?.field === ConsentsAndDeclarationsSortingField.Company) {
        lookupPipelines.push(...consentModuleLookupPipeline);
    }

    return lookupPipelines;
};

const getConsentDisplayListingLeadGenForm = (
    event: Event,
    filter?: Maybe<GraphQLConsentsAndDeclarationsFilteringRule>
) => {
    const filterQuery: Filter<ConsentsAndDeclarations> = {};

    const defaultFilter = [
        { 'conditions.type': ConditionType.IsApplicationModule, 'conditions.moduleId': { $ne: filter.moduleId } },
        { 'conditions.type': ConditionType.IsBank },
        { 'conditions.type': ConditionType.IsApplyingForFinancing },
        { 'conditions.type': ConditionType.IsApplyingForInsurance },
        { 'conditions.type': ConditionType.WithMyinfo },
        { 'conditions.type': ConditionType.IsInsurer },
    ];

    if (!event.isAllowTestDrive) {
        defaultFilter.push({ 'conditions.type': ConditionType.IsTestDrive });
    }

    if (!event.isAllowTradeIn) {
        defaultFilter.push({ 'conditions.type': ConditionType.IsTradeIn });
    }

    filterQuery.$nor = defaultFilter;

    return [{ $match: filterQuery }];
};

const query: GraphQLQueryResolvers['listConsentsAndDeclarations'] = async (
    root,
    { pagination, sort, filter },
    { getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const event = filter.eventId ? await collections.events.findOne({ _id: filter.eventId }) : null;
    const leadGenFormConsent = event ? getConsentDisplayListingLeadGenForm(event, filter) : null;
    const lookupPipelines =
        !filter.isAdditionalConsents && filter.eventId
            ? [...getLookupPipelines(filter, sort), ...leadGenFormConsent]
            : getLookupPipelines(filter, sort);

    return paginateAggregation(
        collections.consentsAndDeclarations,
        [...lookupPipelines, { $match: getFilter(permissionController, filter) }, { $sort: getSort(sort) }],
        pagination
    );
};

export default requiresLoggedUser(query);
