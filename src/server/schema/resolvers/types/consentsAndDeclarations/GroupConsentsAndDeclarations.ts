import { ObjectId } from 'mongodb';
import { GroupConsentsAndDeclarations as GroupConsentsAndDeclarationsDocument } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { GraphQLGroupConsentsAndDeclarationsResolvers } from '../../definitions';

const GroupConsentsAndDeclarations: GraphQLGroupConsentsAndDeclarationsResolvers = {
    // Use the id property directly for GraphQL types
    id: parent => parent.id,
    // Use the type property directly for GraphQL types
    type: parent => parent.type,
    // Use the versioning property directly for GraphQL types
    versioning: parent => parent.versioning,

    async children(parent) {
        const { collections } = await getDatabaseContext();

        // Use the id property which is already an ObjectId
        const parentId = new ObjectId(parent.id);

        return collections.consentsAndDeclarations
            .find({
                parentId,
                isDeleted: false,
                '_versioning.isLatest': true,
            })
            .sort({ orderNumber: 1 })
            .toArray();
    },
};

export default GroupConsentsAndDeclarations;
