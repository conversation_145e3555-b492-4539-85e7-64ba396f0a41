import { ConsentsAndDeclarationsType, GraphQLConsentsAndDeclarationsResolvers } from '../../definitions';

const ConsentsAndDeclarationsGraphQL: GraphQLConsentsAndDeclarationsResolvers = {
    id: root => root._id,
    __resolveType: parent => {
        switch (parent._type) {
            case ConsentsAndDeclarationsType.Text:
                return 'TextConsentsAndDeclarations';

            case ConsentsAndDeclarationsType.Checkbox:
                return 'CheckboxConsentsAndDeclarations';

            case ConsentsAndDeclarationsType.Marketing:
                return 'MarketingConsentsAndDeclarations';

            case ConsentsAndDeclarationsType.Group:
                return 'GroupConsentsAndDeclarations';

            default:
                throw new Error('Consents and declarations type is not supported');
        }
    },
    versioning: root => root._versioning,
    module: async (root, args, { loaders }) => loaders.moduleById.load(root.moduleId),
};

export default ConsentsAndDeclarationsGraphQL;
