import { ObjectId } from 'mongodb';
import { Condition } from './Conditions';
import { TranslatedString } from './LanguagePack';
import { AdvancedVersioning } from './Versioning';

export enum LegalTextPosition {
    Before = 'before',
    After = 'after',
    Modal = 'modal',
}

export enum ConsentsAndDeclarationsPurpose {
    KYC = 'kyc',
    Payment = 'payment',
    Share = 'share',
}

export enum ConsentsAndDeclarationsType {
    Checkbox = 'checkbox',
    Text = 'text',
    Marketing = 'marketing',
    Group = 'group',
}

export enum DataField {
    None = 'none',
    DataProcessing = 'dataProcessing',
}

export type ConsentsAndDeclarationsCore<Type extends ConsentsAndDeclarationsType> = {
    // core properties
    _id: ObjectId;
    _type: Type;
    moduleId: ObjectId;
    displayName: string;
    isDeleted: boolean;
    _versioning: AdvancedVersioning;
    title?: TranslatedString;
    description?: TranslatedString;

    // state properties
    orderNumber: number;
    isActive: boolean;
    dataField: DataField;

    // contextual properties
    conditions: Condition[];
    purpose: ConsentsAndDeclarationsPurpose[];

    featurePurpose: ConsentFeaturePurpose;

    // grouping properties
    parentId?: ObjectId; // Optional, pointing to the C&D group
};

export enum ConsentFeatureType {
    Module = 'module',
    Event = 'event',
}
export type ConsentFeaturePurpose = {
    type: ConsentFeatureType;
    featureId: ObjectId;
};

export type TextConsentsAndDeclarations = ConsentsAndDeclarationsCore<ConsentsAndDeclarationsType.Text>;

export type CheckboxConsentsAndDeclarations = ConsentsAndDeclarationsCore<ConsentsAndDeclarationsType.Checkbox> & {
    isMandatory: boolean;
    hasLegalMarkup?: boolean;
    legalTextPosition?: LegalTextPosition;
    legalMarkup?: TranslatedString;
};

export type MarketingPlatform = {
    mail: boolean;
    email: boolean;
    phone: boolean;
    sms: boolean;
    fax: boolean;
};

export type MarketingConsentsAndDeclarations = ConsentsAndDeclarationsCore<ConsentsAndDeclarationsType.Marketing> & {
    isMandatory: boolean;
    platform: MarketingPlatform;
    defaultChecked: boolean;
};

export type GroupConsentsAndDeclarations = ConsentsAndDeclarationsCore<ConsentsAndDeclarationsType.Group>;

export type ConsentsAndDeclarations =
    | TextConsentsAndDeclarations
    | CheckboxConsentsAndDeclarations
    | MarketingConsentsAndDeclarations
    | GroupConsentsAndDeclarations;
