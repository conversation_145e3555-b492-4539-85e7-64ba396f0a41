import { Permission } from '../../server/database/documents';
import { ResourceType } from '../../server/permissions';
import { adminPermissionId } from './shared';

const permissions: Permission[] = [
    {
        _id: adminPermissionId,
        displayName: 'Administrator',
        systemName: 'apv-test:root',
        description: 'Provide full access administrator',
        policies: [
            {
                resourceType: ResourceType.Application,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.User,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Company,
                actions: '*',
                conditions: {},
            },
            {
                // @ts-ignore
                resourceType: ResourceType.Bank,
                actions: '*',
                conditions: {},
            },
            {
                // @ts-ignore
                resourceType: ResourceType.Vehicle,
                actions: '*',
                conditions: {},
            },
            {
                // @ts-ignore
                resourceType: ResourceType.ConsentAndDeclaration,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Router,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Module,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Language,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.User,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Role,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.UserGroup,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Dealer,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Configurator,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.PromoCode,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Inventory,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Bank,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.FinanceProduct,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Agreement,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Labels,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Mobility,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Website,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.FinderVehicle,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Event,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Insurer,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.InsuranceProduct,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.GiftVoucher,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Customer,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.TradeIn,
                actions: '*',
                conditions: {},
            },
            {
                resourceType: ResourceType.Lead,
                actions: '*',
                conditions: {},
            },
        ],
        origins: { system: true },
    },
];

export default permissions;
