import { gql } from '@apollo/client';
import { flow, set, omit } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    CompanySettings,
    EmailProvider as GraphQLEmailProvider,
    SmsProvider as GraphQLSmsProvider,
    CompanyTheme as GraphQLCompanyTheme,
    PasswordConfiguration as GraphQLPasswordConfiguration,
    MaskDirection as GraphQLMaskDirection,
    CalculationRounding,
} from '../../app/api';
import { CompanyTheme, EmailProvider, PasswordConfiguration, SmsProvider } from '../../server/database/documents';
import getDatabaseContext from '../../server/database/getDatabaseContext';
import companies from '../fixtures/companies';
import permissions from '../fixtures/permissions';
import users, { defaultAdminUserId } from '../fixtures/users';
import { getApolloClient } from '../helpers/apollo';
import { cleanDatabase, createSessionForUser, loadFixtures, setupDatabase } from '../helpers/database';
import { setupWebServer } from '../helpers/server';

const mutation = gql`
    mutation createCompany($settings: CompanySettings!) {
        createCompany(settings: $settings) {
            id
        }
    }
`;

const settings: CompanySettings = {
    email: '<EMAIL>',
    color: '#EF2C72',
    copyright: {
        defaultValue: 'Appvantage',
        overrides: [],
    },
    countryCode: 'SG',
    currency: 'SGD',
    coe: 0,
    ppsr: 0,
    estFee: 0,
    theme: GraphQLCompanyTheme.Porsche,
    emailSettings: {
        provider: GraphQLEmailProvider.System,
    },
    smsSettings: {
        provider: GraphQLSmsProvider.System,
    },
    isActive: true,
    companyName: {
        defaultValue: 'Appvantage',
        overrides: [],
    },
    legalName: {
        defaultValue: 'Appvantage',
        overrides: [],
    },
    displayName: 'Appvantage',
    phone: {
        prefix: 65,
        value: '84124321',
    },
    roundings: {
        amount: { decimals: 0 },
        percentage: { decimals: 0 },
    },
    calculationRounding: CalculationRounding.None,
    timeZone: 'Asia/Singapore',
    languages: [],
    sessionTimeout: 5,
    edmEmailFooter: {
        connectText: { defaultValue: 'Connect', overrides: [] },
        copyRight: { defaultValue: 'Copy-Right', overrides: [] },
        disclaimerText: { defaultValue: 'Disclaimer', overrides: [] },
    },
    passwordConfiguration: GraphQLPasswordConfiguration.Off,
    dataMask: {
        count: 0,
        direction: GraphQLMaskDirection.None,
    },
    isDataPurgeEnabled: false,
    enableContentRefinement: false,
    isInstantApprovalStatsEnabled: false,
    allowLimitDealerFeature: false,
    shouldSendCalendarInvite: false,
    vatRateSettings: null,
};

const webServer = setupWebServer();

beforeAll(async () => webServer.initialize());

beforeEach(async () => {
    await setupDatabase();

    await loadFixtures('companies', companies);
    await loadFixtures('permissions', permissions);
    await loadFixtures('users', users);
});

afterEach(async () => {
    await cleanDatabase();
});

afterAll(() => webServer.cleanUp());

test.only('Create company successfully creates a new company on valid inputs', async () => {
    const { token: originalToken, csrf } = await createSessionForUser(defaultAdminUserId);

    const { client } = getApolloClient(webServer.url, { authorizationToken: originalToken, csrf });
    const { data } = await client.mutate({ mutation, variables: { settings } });
    const companyId = new ObjectId(data.createCompany.id);
    const { collections } = await getDatabaseContext();
    const company = await collections.companies.findOne({ _id: companyId });
    expect(company).not.toBeNull();
    expect(company).toMatchObject(
        flow([
            set('emailSettings.provider', EmailProvider.System),
            set('smsSettings.provider', SmsProvider.System),
            set('theme', CompanyTheme.Porsche),
            set('passwordConfiguration', PasswordConfiguration.Off),
            set('mask', settings.dataMask),
            omit('dataMask'),
        ])(settings)
    );
});
