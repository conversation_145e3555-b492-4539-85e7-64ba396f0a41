import { gql } from '@apollo/client';
import { ObjectId } from 'mongodb';
import { ApplicationScenario } from '../../../app/api';
import { Event, EventApplicationModule } from '../../../server/database/documents';
import getDatabaseContext from '../../../server/database/getDatabaseContext';
import companies from '../../fixtures/companies';
import { eventInput, kycPresets } from '../../fixtures/event';
import {
    consentModule,
    localCustomerManagementModule,
    simpleVehicleManagementModule,
    eventModule,
} from '../../fixtures/modules';
import permissions from '../../fixtures/permissions';
import users, { defaultAdminUserId } from '../../fixtures/users';
import { getApolloClient } from '../../helpers/apollo';
import { cleanDatabase, createSessionForUser, loadFixtures, setupDatabase } from '../../helpers/database';
import { setupWebServer } from '../../helpers/server';

const mutation = gql`
    mutation createEvent($moduleId: ObjectID!, $eventInput: EventInput!, $kycPresets: [EventKycPresetInput!]!) {
        createEvent(moduleId: $moduleId, eventInput: $eventInput, kycPresets: $kycPresets) {
            id
            urlSlug
            moduleId
        }
    }
`;

const webServer = setupWebServer();

describe('Event Create Tests', () => {
    let client: ReturnType<typeof getApolloClient>['client'];
    let originalToken: string;
    let csrf: string;

    beforeAll(async () => {
        await webServer.initialize();

        await setupDatabase();

        await loadFixtures('companies', companies);
        await loadFixtures('permissions', permissions);
        await loadFixtures('users', users);

        const modules = [consentModule, localCustomerManagementModule, simpleVehicleManagementModule, eventModule];
        await loadFixtures('modules', modules);

        const session = await createSessionForUser(defaultAdminUserId);
        originalToken = session.token;
        csrf = session.csrf;
        client = getApolloClient(webServer.url, { authorizationToken: originalToken, csrf }).client;
    });

    afterAll(async () => {
        await cleanDatabase();

        await webServer.cleanUp();
    });

    test('Create event successfully with valid input', async () => {
        const { collections } = await getDatabaseContext();

        const module = await collections.modules.findOne<EventApplicationModule>({ _id: eventModule._id });

        const { data } = await client.mutate({
            mutation,
            variables: {
                moduleId: module._id,
                eventInput,
                kycPresets,
            },
        });

        const event = await collections.events.findOne<Event>({ _id: new ObjectId(data.createEvent.id) });
        expect(event).not.toBeNull();
        expect(event._id.toString()).toBe(data.createEvent.id);
        expect(event.urlSlug).toBe(eventInput.urlSlug);
        expect(event.moduleId).toEqual(module._id);
    });

    test('Create event with missing required fields', async () => {
        const invalidEventInput = { ...eventInput, displayName: null };

        await expect(
            client.mutate({
                mutation,
                variables: {
                    moduleId: eventModule._id,
                    eventInput: invalidEventInput,
                    kycPresets,
                },
            })
        ).rejects.toMatchObject({
            message: 'Response not successful: Received status code 400',
        });
    });

    test('Create event with invalid URL slug', async () => {
        const invalidEventInput = { ...eventInput, urlSlug: 'invalid slug' };

        await expect(
            client.mutate({
                mutation,
                variables: {
                    moduleId: eventModule._id,
                    eventInput: invalidEventInput,
                    kycPresets,
                },
            })
        ).rejects.toMatchObject({
            message: 'bad request',
            graphQLErrors: [
                {
                    extensions: {
                        code: 'BAD_USER_INPUT',
                        urlSlug: 'URL only allow small letter alphanumeric and hyphen',
                    },
                },
            ],
        });
    });

    test('Create event with duplicate URL slug', async () => {
        await expect(
            client.mutate({
                mutation,
                variables: {
                    moduleId: eventModule._id,
                    eventInput,
                    kycPresets,
                },
            })
        ).rejects.toMatchObject({
            message: 'bad request',
            graphQLErrors: [
                {
                    extensions: {
                        code: 'BAD_USER_INPUT',
                        urlSlug: 'There is same URL Slug in same module',
                    },
                },
            ],
        });
    });

    test('Create event without required permission', async () => {
        const { token: originalToken, csrf } = await createSessionForUser(users[1]._id);
        const { client: nonAuthorizedClient } = getApolloClient(webServer.url, {
            authorizationToken: originalToken,
            csrf,
        });

        await expect(
            nonAuthorizedClient.mutate({
                mutation,
                variables: {
                    moduleId: eventModule._id,
                    eventInput,
                    kycPresets,
                },
            })
        ).rejects.toMatchObject({
            message: 'forbidden',
            graphQLErrors: [
                {
                    extensions: {
                        code: 'FORBIDDEN',
                    },
                },
            ],
        });
    });

    test('Create event with missing payment setting', async () => {
        const invalidEventInput = {
            ...eventInput,
            scenarios: [ApplicationScenario.Payment],
        };

        await expect(
            client.mutate({
                mutation,
                variables: {
                    moduleId: eventModule._id,
                    eventInput: invalidEventInput,
                    kycPresets,
                },
            })
        ).rejects.toMatchObject({
            message: 'bad request',
            graphQLErrors: [
                {
                    extensions: {
                        code: 'BAD_USER_INPUT',
                        paymentSetting: 'Payment Module is not selected',
                    },
                },
            ],
        });
    });

    test('Create event with missing public sales person', async () => {
        const invalidEventInput = {
            ...eventInput,
            privateAccess: false,
        };

        await expect(
            client.mutate({
                mutation,
                variables: {
                    moduleId: eventModule._id,
                    eventInput: invalidEventInput,
                    kycPresets,
                },
            })
        ).rejects.toMatchObject({
            message: 'bad request',
            graphQLErrors: [
                {
                    extensions: {
                        code: 'BAD_USER_INPUT',
                        publicSalesPerson: 'There must be a responsible sales person for Public Event',
                    },
                },
            ],
        });
    });
});
