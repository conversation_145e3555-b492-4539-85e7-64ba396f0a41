import { Collection, ObjectId } from 'mongodb';
import { Collections } from '../../server/database/collections';
import getDatabaseContext from '../../server/database/getDatabaseContext';
import { migrate } from '../../server/database/migrate';
import { getSessionToken } from '../../server/schema/session';

export type Fixtures = Partial<{
    [CollectionName in keyof Collections]: Array<
        Collections[CollectionName] extends Collection<infer Schema> ? Schema : never
    >;
}>;

export const setupDatabase = async (): Promise<void> => {
    // get the database
    const context = await getDatabaseContext();

    // ensure everything is dropped
    await context.regular.db.dropDatabase();

    // run migration
    await migrate(context, false);
};

export const cleanDatabase = async (): Promise<void> => {
    // get the database
    const { regular } = await getDatabaseContext();

    // clean the database (regular and encrypted are on the same database)
    await regular.db.dropDatabase();

    // close regular database
    await regular.client.close();

    // reset mongo global store
    global.mongo = { context: null, promise: null };
};

export const loadFixtures = async <T extends keyof Collections>(
    collectionName: T, // Collection name (key from `Collections`)
    fixtures: Fixtures[T] // The fixtures to load (array of schema objects)
): Promise<void> => {
    const { regular } = await getDatabaseContext();

    if (fixtures && fixtures.length > 0) {
        await regular.db.collection(collectionName).insertMany(fixtures);
    }
};

export const createSessionForUser = async (userId: ObjectId) => {
    // generate a session ID
    const sessionId = new ObjectId();

    // create token
    const data = await getSessionToken({ sessionId, userId });

    // create session in database as well
    const { collections } = await getDatabaseContext();
    await collections.userSessions.insertOne({
        _id: sessionId,
        userId,
        userAgent: '',
        expiresAt: data.exp,
        createdAt: data.iat,
        lastActivityAt: data.iat,
    });

    return data;
};
