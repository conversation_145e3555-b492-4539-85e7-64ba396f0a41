import type * as SchemaTypes from '../types';

import type { ConsentsAndDeclarationsWithPermissionsSpecs_CheckboxConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsWithPermissionsSpecs_GroupConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsWithPermissionsSpecs_MarketingConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsWithPermissionsSpecs_TextConsentsAndDeclarations_Fragment } from '../fragments/ConsentsAndDeclarationsWithPermissionsSpecs';
import type { ConsentsAndDeclarationsSpecs_CheckboxConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsSpecs_GroupConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsSpecs_MarketingConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsSpecs_TextConsentsAndDeclarations_Fragment } from '../fragments/ConsentsAndDeclarationsSpecs';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { MarketingPlatformSpecsFragment } from '../fragments/MarketingPlatformSpecs';
import { gql } from '@apollo/client';
import { ConsentsAndDeclarationsWithPermissionsSpecsFragmentDoc } from '../fragments/ConsentsAndDeclarationsWithPermissionsSpecs';
import { ConsentsAndDeclarationsSpecsFragmentDoc } from '../fragments/ConsentsAndDeclarationsSpecs';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { MarketingPlatformSpecsFragmentDoc } from '../fragments/MarketingPlatformSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetConsentsAndDeclarationsQueryVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetConsentsAndDeclarationsQuery = (
  { __typename: 'Query' }
  & { consentsAndDeclarations?: SchemaTypes.Maybe<(
    { __typename: 'CheckboxConsentsAndDeclarations' }
    & ConsentsAndDeclarationsWithPermissionsSpecs_CheckboxConsentsAndDeclarations_Fragment
  ) | (
    { __typename: 'GroupConsentsAndDeclarations' }
    & ConsentsAndDeclarationsWithPermissionsSpecs_GroupConsentsAndDeclarations_Fragment
  ) | (
    { __typename: 'MarketingConsentsAndDeclarations' }
    & ConsentsAndDeclarationsWithPermissionsSpecs_MarketingConsentsAndDeclarations_Fragment
  ) | (
    { __typename: 'TextConsentsAndDeclarations' }
    & ConsentsAndDeclarationsWithPermissionsSpecs_TextConsentsAndDeclarations_Fragment
  )> }
);


export const GetConsentsAndDeclarationsDocument = /*#__PURE__*/ gql`
    query getConsentsAndDeclarations($id: ObjectID!) {
  consentsAndDeclarations: getConsentsAndDeclarations(id: $id) {
    ...ConsentsAndDeclarationsWithPermissionsSpecs
  }
}
    ${ConsentsAndDeclarationsWithPermissionsSpecsFragmentDoc}
${ConsentsAndDeclarationsSpecsFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${MarketingPlatformSpecsFragmentDoc}`;

/**
 * __useGetConsentsAndDeclarationsQuery__
 *
 * To run a query within a React component, call `useGetConsentsAndDeclarationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetConsentsAndDeclarationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetConsentsAndDeclarationsQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetConsentsAndDeclarationsQuery(baseOptions: Apollo.QueryHookOptions<GetConsentsAndDeclarationsQuery, GetConsentsAndDeclarationsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetConsentsAndDeclarationsQuery, GetConsentsAndDeclarationsQueryVariables>(GetConsentsAndDeclarationsDocument, options);
      }
export function useGetConsentsAndDeclarationsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetConsentsAndDeclarationsQuery, GetConsentsAndDeclarationsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetConsentsAndDeclarationsQuery, GetConsentsAndDeclarationsQueryVariables>(GetConsentsAndDeclarationsDocument, options);
        }
export type GetConsentsAndDeclarationsQueryHookResult = ReturnType<typeof useGetConsentsAndDeclarationsQuery>;
export type GetConsentsAndDeclarationsLazyQueryHookResult = ReturnType<typeof useGetConsentsAndDeclarationsLazyQuery>;
export type GetConsentsAndDeclarationsQueryResult = Apollo.QueryResult<GetConsentsAndDeclarationsQuery, GetConsentsAndDeclarationsQueryVariables>;