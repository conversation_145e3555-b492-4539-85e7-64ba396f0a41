import type * as SchemaTypes from '../types';

import type { LocalCustomerManagementModuleSpecsFragment } from '../fragments/LocalCustomerManagementModuleSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import type { KycExtraSettingsSpecsFragment } from '../fragments/KYCExtraSettingsSpecs';
import type { KycPresetsSpecFragment } from '../fragments/KYCPresetsSpec';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { LocalCustomerManagementModuleSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import { KycExtraSettingsSpecsFragmentDoc } from '../fragments/KYCExtraSettingsSpecs';
import { KycPresetsSpecFragmentDoc } from '../fragments/KYCPresetsSpec';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type UpdateLocalCustomerManagementKycFieldsMutationVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
  kycFields: Array<SchemaTypes.LocalCustomerManagementModuleKycFieldSettings> | SchemaTypes.LocalCustomerManagementModuleKycFieldSettings;
  extraSettings: SchemaTypes.LocalCustomerManagementKycFieldsExtraConfigSettings;
}>;


export type UpdateLocalCustomerManagementKycFieldsMutation = (
  { __typename: 'Mutation' }
  & { module?: SchemaTypes.Maybe<{ __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
    { __typename: 'LocalCustomerManagementModule' }
    & LocalCustomerManagementModuleSpecsFragment
  ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }> }
);


export const UpdateLocalCustomerManagementKycFieldsDocument = /*#__PURE__*/ gql`
    mutation updateLocalCustomerManagementKYCFields($moduleId: ObjectID!, $kycFields: [LocalCustomerManagementModuleKycFieldSettings!]!, $extraSettings: LocalCustomerManagementKYCFieldsExtraConfigSettings!) {
  module: updateLocalCustomerManagementKYCFields(
    moduleId: $moduleId
    kycFields: $kycFields
    extraSettings: $extraSettings
  ) {
    ...LocalCustomerManagementModuleSpecs
  }
}
    ${LocalCustomerManagementModuleSpecsFragmentDoc}
${LocalCustomerManagementModuleKycFieldSpecsFragmentDoc}
${KycExtraSettingsSpecsFragmentDoc}
${KycPresetsSpecFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;
export type UpdateLocalCustomerManagementKycFieldsMutationFn = Apollo.MutationFunction<UpdateLocalCustomerManagementKycFieldsMutation, UpdateLocalCustomerManagementKycFieldsMutationVariables>;

/**
 * __useUpdateLocalCustomerManagementKycFieldsMutation__
 *
 * To run a mutation, you first call `useUpdateLocalCustomerManagementKycFieldsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateLocalCustomerManagementKycFieldsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateLocalCustomerManagementKycFieldsMutation, { data, loading, error }] = useUpdateLocalCustomerManagementKycFieldsMutation({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *      kycFields: // value for 'kycFields'
 *      extraSettings: // value for 'extraSettings'
 *   },
 * });
 */
export function useUpdateLocalCustomerManagementKycFieldsMutation(baseOptions?: Apollo.MutationHookOptions<UpdateLocalCustomerManagementKycFieldsMutation, UpdateLocalCustomerManagementKycFieldsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateLocalCustomerManagementKycFieldsMutation, UpdateLocalCustomerManagementKycFieldsMutationVariables>(UpdateLocalCustomerManagementKycFieldsDocument, options);
      }
export type UpdateLocalCustomerManagementKycFieldsMutationHookResult = ReturnType<typeof useUpdateLocalCustomerManagementKycFieldsMutation>;
export type UpdateLocalCustomerManagementKycFieldsMutationResult = Apollo.MutationResult<UpdateLocalCustomerManagementKycFieldsMutation>;
export type UpdateLocalCustomerManagementKycFieldsMutationOptions = Apollo.BaseMutationOptions<UpdateLocalCustomerManagementKycFieldsMutation, UpdateLocalCustomerManagementKycFieldsMutationVariables>;