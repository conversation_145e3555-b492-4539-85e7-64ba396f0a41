import type * as SchemaTypes from '../types';

import type { ConsentsAndDeclarationsSpecs_CheckboxConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsSpecs_GroupConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsSpecs_MarketingConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsSpecs_TextConsentsAndDeclarations_Fragment } from '../fragments/ConsentsAndDeclarationsSpecs';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { MarketingPlatformSpecsFragment } from '../fragments/MarketingPlatformSpecs';
import { gql } from '@apollo/client';
import { ConsentsAndDeclarationsSpecsFragmentDoc } from '../fragments/ConsentsAndDeclarationsSpecs';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { MarketingPlatformSpecsFragmentDoc } from '../fragments/MarketingPlatformSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type CreateCheckboxConsentsAndDeclarationsMutationVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
  settings: SchemaTypes.CheckboxConsentsAndDeclarationsSettings;
  conditionSettings?: SchemaTypes.InputMaybe<Array<SchemaTypes.ConditionSettings> | SchemaTypes.ConditionSettings>;
  eventId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type CreateCheckboxConsentsAndDeclarationsMutation = (
  { __typename: 'Mutation' }
  & { consentsAndDeclarations: (
    { __typename: 'CheckboxConsentsAndDeclarations' }
    & ConsentsAndDeclarationsSpecs_CheckboxConsentsAndDeclarations_Fragment
  ) | (
    { __typename: 'GroupConsentsAndDeclarations' }
    & ConsentsAndDeclarationsSpecs_GroupConsentsAndDeclarations_Fragment
  ) | (
    { __typename: 'MarketingConsentsAndDeclarations' }
    & ConsentsAndDeclarationsSpecs_MarketingConsentsAndDeclarations_Fragment
  ) | (
    { __typename: 'TextConsentsAndDeclarations' }
    & ConsentsAndDeclarationsSpecs_TextConsentsAndDeclarations_Fragment
  ) }
);


export const CreateCheckboxConsentsAndDeclarationsDocument = /*#__PURE__*/ gql`
    mutation createCheckboxConsentsAndDeclarations($moduleId: ObjectID!, $settings: CheckboxConsentsAndDeclarationsSettings!, $conditionSettings: [ConditionSettings!], $eventId: ObjectID) {
  consentsAndDeclarations: createCheckboxConsentsAndDeclarations(
    moduleId: $moduleId
    settings: $settings
    conditionSettings: $conditionSettings
    eventId: $eventId
  ) {
    ...ConsentsAndDeclarationsSpecs
  }
}
    ${ConsentsAndDeclarationsSpecsFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${MarketingPlatformSpecsFragmentDoc}`;
export type CreateCheckboxConsentsAndDeclarationsMutationFn = Apollo.MutationFunction<CreateCheckboxConsentsAndDeclarationsMutation, CreateCheckboxConsentsAndDeclarationsMutationVariables>;

/**
 * __useCreateCheckboxConsentsAndDeclarationsMutation__
 *
 * To run a mutation, you first call `useCreateCheckboxConsentsAndDeclarationsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateCheckboxConsentsAndDeclarationsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createCheckboxConsentsAndDeclarationsMutation, { data, loading, error }] = useCreateCheckboxConsentsAndDeclarationsMutation({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *      settings: // value for 'settings'
 *      conditionSettings: // value for 'conditionSettings'
 *      eventId: // value for 'eventId'
 *   },
 * });
 */
export function useCreateCheckboxConsentsAndDeclarationsMutation(baseOptions?: Apollo.MutationHookOptions<CreateCheckboxConsentsAndDeclarationsMutation, CreateCheckboxConsentsAndDeclarationsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateCheckboxConsentsAndDeclarationsMutation, CreateCheckboxConsentsAndDeclarationsMutationVariables>(CreateCheckboxConsentsAndDeclarationsDocument, options);
      }
export type CreateCheckboxConsentsAndDeclarationsMutationHookResult = ReturnType<typeof useCreateCheckboxConsentsAndDeclarationsMutation>;
export type CreateCheckboxConsentsAndDeclarationsMutationResult = Apollo.MutationResult<CreateCheckboxConsentsAndDeclarationsMutation>;
export type CreateCheckboxConsentsAndDeclarationsMutationOptions = Apollo.BaseMutationOptions<CreateCheckboxConsentsAndDeclarationsMutation, CreateCheckboxConsentsAndDeclarationsMutationVariables>;