/* eslint-disable */

export type {
  VerifyMobileOtpMutationVariables,
  VerifyMobileOtpMutation,
  VerifyMobileOtpMutationFn,
  VerifyMobileOtpMutationHookResult,
  VerifyMobileOtpMutationResult,
  VerifyMobileOtpMutationOptions,
} from "./verifyMobileOTP";

export {
  VerifyMobileOtpDocument,
  useVerifyMobileOtpMutation,
} from "./verifyMobileOTP";

export type {
  ValidateSmsOtpForUpdateEmailMutationVariables,
  ValidateSmsOtpForUpdateEmailMutation,
  ValidateSmsOtpForUpdateEmailMutationFn,
  ValidateSmsOtpForUpdateEmailMutationHookResult,
  ValidateSmsOtpForUpdateEmailMutationResult,
  ValidateSmsOtpForUpdateEmailMutationOptions,
} from "./validateSmsOTPForUpdateEmail";

export {
  ValidateSmsOtpForUpdateEmailDocument,
  useValidateSmsOtpForUpdateEmailMutation,
} from "./validateSmsOTPForUpdateEmail";

export type {
  ValidateSmsOtpForResetPasswordMutationVariables,
  ValidateSmsOtpForResetPasswordMutation,
  ValidateSmsOtpForResetPasswordMutationFn,
  ValidateSmsOtpForResetPasswordMutationHookResult,
  ValidateSmsOtpForResetPasswordMutationResult,
  ValidateSmsOtpForResetPasswordMutationOptions,
} from "./validateSmsOTPForResetPassword";

export {
  ValidateSmsOtpForResetPasswordDocument,
  useValidateSmsOtpForResetPasswordMutation,
} from "./validateSmsOTPForResetPassword";

export type {
  ValidateSalesOfferRemoteJourneyPasscodeMutationVariables,
  ValidateSalesOfferRemoteJourneyPasscodeMutation,
  ValidateSalesOfferRemoteJourneyPasscodeMutationFn,
  ValidateSalesOfferRemoteJourneyPasscodeMutationHookResult,
  ValidateSalesOfferRemoteJourneyPasscodeMutationResult,
  ValidateSalesOfferRemoteJourneyPasscodeMutationOptions,
} from "./validateSalesOfferRemoteJourneyPasscode";

export {
  ValidateSalesOfferRemoteJourneyPasscodeDocument,
  useValidateSalesOfferRemoteJourneyPasscodeMutation,
} from "./validateSalesOfferRemoteJourneyPasscode";

export type {
  ValidateRemoteJourneyPasscodeMutationVariables,
  ValidateRemoteJourneyPasscodeMutation,
  ValidateRemoteJourneyPasscodeMutationFn,
  ValidateRemoteJourneyPasscodeMutationHookResult,
  ValidateRemoteJourneyPasscodeMutationResult,
  ValidateRemoteJourneyPasscodeMutationOptions,
} from "./validateRemoteJourneyPasscode";

export {
  ValidateRemoteJourneyPasscodeDocument,
  useValidateRemoteJourneyPasscodeMutation,
} from "./validateRemoteJourneyPasscode";

export type {
  UpsertKycPresetInEventMutationVariables,
  UpsertKycPresetInEventMutation,
  UpsertKycPresetInEventMutationFn,
  UpsertKycPresetInEventMutationHookResult,
  UpsertKycPresetInEventMutationResult,
  UpsertKycPresetInEventMutationOptions,
} from "./upsertKycPresetInEvent";

export {
  UpsertKycPresetInEventDocument,
  useUpsertKycPresetInEventMutation,
} from "./upsertKycPresetInEvent";

export type {
  UpsertEventUserIdsMutationVariables,
  UpsertEventUserIdsMutation,
  UpsertEventUserIdsMutationFn,
  UpsertEventUserIdsMutationHookResult,
  UpsertEventUserIdsMutationResult,
  UpsertEventUserIdsMutationOptions,
} from "./upsertEventUserIds";

export {
  UpsertEventUserIdsDocument,
  useUpsertEventUserIdsMutation,
} from "./upsertEventUserIds";

export type {
  UploadWebsiteSocialMediaAssetMutationVariables,
  UploadWebsiteSocialMediaAssetMutation,
  UploadWebsiteSocialMediaAssetMutationFn,
  UploadWebsiteSocialMediaAssetMutationHookResult,
  UploadWebsiteSocialMediaAssetMutationResult,
  UploadWebsiteSocialMediaAssetMutationOptions,
} from "./uploadWebsiteSocialMediaAsset";

export {
  UploadWebsiteSocialMediaAssetDocument,
  useUploadWebsiteSocialMediaAssetMutation,
} from "./uploadWebsiteSocialMediaAsset";

export type {
  UploadWebPageImageMutationVariables,
  UploadWebPageImageMutation,
  UploadWebPageImageMutationFn,
  UploadWebPageImageMutationHookResult,
  UploadWebPageImageMutationResult,
  UploadWebPageImageMutationOptions,
} from "./uploadWebPageImage";

export {
  UploadWebPageImageDocument,
  useUploadWebPageImageMutation,
} from "./uploadWebPageImage";

export type {
  UploadVisitAppointmentModuleAssetMutationVariables,
  UploadVisitAppointmentModuleAssetMutation,
  UploadVisitAppointmentModuleAssetMutationFn,
  UploadVisitAppointmentModuleAssetMutationHookResult,
  UploadVisitAppointmentModuleAssetMutationResult,
  UploadVisitAppointmentModuleAssetMutationOptions,
} from "./uploadVisitAppointmentModuleAsset";

export {
  UploadVisitAppointmentModuleAssetDocument,
  useUploadVisitAppointmentModuleAssetMutation,
} from "./uploadVisitAppointmentModuleAsset";

export type {
  UploadVehicleSalesOfferSpecificationDocumentMutationVariables,
  UploadVehicleSalesOfferSpecificationDocumentMutation,
  UploadVehicleSalesOfferSpecificationDocumentMutationFn,
  UploadVehicleSalesOfferSpecificationDocumentMutationHookResult,
  UploadVehicleSalesOfferSpecificationDocumentMutationResult,
  UploadVehicleSalesOfferSpecificationDocumentMutationOptions,
} from "./uploadVehicleSalesOfferSpecificationDocument";

export {
  UploadVehicleSalesOfferSpecificationDocumentDocument,
  useUploadVehicleSalesOfferSpecificationDocumentMutation,
} from "./uploadVehicleSalesOfferSpecificationDocument";

export type {
  UploadVariantConfiguratorTrimSettingAssetMutationVariables,
  UploadVariantConfiguratorTrimSettingAssetMutation,
  UploadVariantConfiguratorTrimSettingAssetMutationFn,
  UploadVariantConfiguratorTrimSettingAssetMutationHookResult,
  UploadVariantConfiguratorTrimSettingAssetMutationResult,
  UploadVariantConfiguratorTrimSettingAssetMutationOptions,
} from "./uploadVariantConfiguratorTrimSettingAsset";

export {
  UploadVariantConfiguratorTrimSettingAssetDocument,
  useUploadVariantConfiguratorTrimSettingAssetMutation,
} from "./uploadVariantConfiguratorTrimSettingAsset";

export type {
  UploadVariantConfiguratorPackageSectionImageAssetMutationVariables,
  UploadVariantConfiguratorPackageSectionImageAssetMutation,
  UploadVariantConfiguratorPackageSectionImageAssetMutationFn,
  UploadVariantConfiguratorPackageSectionImageAssetMutationHookResult,
  UploadVariantConfiguratorPackageSectionImageAssetMutationResult,
  UploadVariantConfiguratorPackageSectionImageAssetMutationOptions,
} from "./uploadVariantConfiguratorPackageSectionImageAsset";

export {
  UploadVariantConfiguratorPackageSectionImageAssetDocument,
  useUploadVariantConfiguratorPackageSectionImageAssetMutation,
} from "./uploadVariantConfiguratorPackageSectionImageAsset";

export type {
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutationVariables,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutation,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutationFn,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutationHookResult,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutationResult,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutationOptions,
} from "./uploadVariantConfiguratorPackageAdditionalDetailsAsset";

export {
  UploadVariantConfiguratorPackageAdditionalDetailsAssetDocument,
  useUploadVariantConfiguratorPackageAdditionalDetailsAssetMutation,
} from "./uploadVariantConfiguratorPackageAdditionalDetailsAsset";

export type {
  UploadVariantConfiguratorOptionSettingAssetMutationVariables,
  UploadVariantConfiguratorOptionSettingAssetMutation,
  UploadVariantConfiguratorOptionSettingAssetMutationFn,
  UploadVariantConfiguratorOptionSettingAssetMutationHookResult,
  UploadVariantConfiguratorOptionSettingAssetMutationResult,
  UploadVariantConfiguratorOptionSettingAssetMutationOptions,
} from "./uploadVariantConfiguratorOptionSettingAsset";

export {
  UploadVariantConfiguratorOptionSettingAssetDocument,
  useUploadVariantConfiguratorOptionSettingAssetMutation,
} from "./uploadVariantConfiguratorOptionSettingAsset";

export type {
  UploadVariantConfiguratorOptionAssetMutationVariables,
  UploadVariantConfiguratorOptionAssetMutation,
  UploadVariantConfiguratorOptionAssetMutationFn,
  UploadVariantConfiguratorOptionAssetMutationHookResult,
  UploadVariantConfiguratorOptionAssetMutationResult,
  UploadVariantConfiguratorOptionAssetMutationOptions,
} from "./uploadVariantConfiguratorOptionAsset";

export {
  UploadVariantConfiguratorOptionAssetDocument,
  useUploadVariantConfiguratorOptionAssetMutation,
} from "./uploadVariantConfiguratorOptionAsset";

export type {
  UploadVariantConfiguratorMatrixAssetMutationVariables,
  UploadVariantConfiguratorMatrixAssetMutation,
  UploadVariantConfiguratorMatrixAssetMutationFn,
  UploadVariantConfiguratorMatrixAssetMutationHookResult,
  UploadVariantConfiguratorMatrixAssetMutationResult,
  UploadVariantConfiguratorMatrixAssetMutationOptions,
} from "./uploadVariantConfiguratorMatrixAsset";

export {
  UploadVariantConfiguratorMatrixAssetDocument,
  useUploadVariantConfiguratorMatrixAssetMutation,
} from "./uploadVariantConfiguratorMatrixAsset";

export type {
  UploadVariantConfiguratorColorSettingAssetMutationVariables,
  UploadVariantConfiguratorColorSettingAssetMutation,
  UploadVariantConfiguratorColorSettingAssetMutationFn,
  UploadVariantConfiguratorColorSettingAssetMutationHookResult,
  UploadVariantConfiguratorColorSettingAssetMutationResult,
  UploadVariantConfiguratorColorSettingAssetMutationOptions,
} from "./uploadVariantConfiguratorColorSettingAsset";

export {
  UploadVariantConfiguratorColorSettingAssetDocument,
  useUploadVariantConfiguratorColorSettingAssetMutation,
} from "./uploadVariantConfiguratorColorSettingAsset";

export type {
  UploadVariantAssetMutationVariables,
  UploadVariantAssetMutation,
  UploadVariantAssetMutationFn,
  UploadVariantAssetMutationHookResult,
  UploadVariantAssetMutationResult,
  UploadVariantAssetMutationOptions,
} from "./uploadVariantAsset";

export {
  UploadVariantAssetDocument,
  useUploadVariantAssetMutation,
} from "./uploadVariantAsset";

export type {
  UploadUserAssetMutationVariables,
  UploadUserAssetMutation,
  UploadUserAssetMutationFn,
  UploadUserAssetMutationHookResult,
  UploadUserAssetMutationResult,
  UploadUserAssetMutationOptions,
} from "./uploadUserAsset";

export {
  UploadUserAssetDocument,
  useUploadUserAssetMutation,
} from "./uploadUserAsset";

export type {
  UploadStockAssetMutationVariables,
  UploadStockAssetMutation,
  UploadStockAssetMutationFn,
  UploadStockAssetMutationHookResult,
  UploadStockAssetMutationResult,
  UploadStockAssetMutationOptions,
} from "./uploadStockAsset";

export {
  UploadStockAssetDocument,
  useUploadStockAssetMutation,
} from "./uploadStockAsset";

export type {
  UploadStandardApplicationModuleAssetMutationVariables,
  UploadStandardApplicationModuleAssetMutation,
  UploadStandardApplicationModuleAssetMutationFn,
  UploadStandardApplicationModuleAssetMutationHookResult,
  UploadStandardApplicationModuleAssetMutationResult,
  UploadStandardApplicationModuleAssetMutationOptions,
} from "./uploadStandardApplicationModuleAsset";

export {
  UploadStandardApplicationModuleAssetDocument,
  useUploadStandardApplicationModuleAssetMutation,
} from "./uploadStandardApplicationModuleAsset";

export type {
  UploadSalesOfferDocumentMutationVariables,
  UploadSalesOfferDocumentMutation,
  UploadSalesOfferDocumentMutationFn,
  UploadSalesOfferDocumentMutationHookResult,
  UploadSalesOfferDocumentMutationResult,
  UploadSalesOfferDocumentMutationOptions,
} from "./uploadSalesOfferDocument";

export {
  UploadSalesOfferDocumentDocument,
  useUploadSalesOfferDocumentMutation,
} from "./uploadSalesOfferDocument";

export type {
  UploadModelConfiguratorAssetMutationVariables,
  UploadModelConfiguratorAssetMutation,
  UploadModelConfiguratorAssetMutationFn,
  UploadModelConfiguratorAssetMutationHookResult,
  UploadModelConfiguratorAssetMutationResult,
  UploadModelConfiguratorAssetMutationOptions,
} from "./uploadModelConfiguratorAsset";

export {
  UploadModelConfiguratorAssetDocument,
  useUploadModelConfiguratorAssetMutation,
} from "./uploadModelConfiguratorAsset";

export type {
  UploadMobilityModuleEmailAssetMutationVariables,
  UploadMobilityModuleEmailAssetMutation,
  UploadMobilityModuleEmailAssetMutationFn,
  UploadMobilityModuleEmailAssetMutationHookResult,
  UploadMobilityModuleEmailAssetMutationResult,
  UploadMobilityModuleEmailAssetMutationOptions,
} from "./uploadMobilityModuleEmailAsset";

export {
  UploadMobilityModuleEmailAssetDocument,
  useUploadMobilityModuleEmailAssetMutation,
} from "./uploadMobilityModuleEmailAsset";

export type {
  UploadMaintenanceModuleAssetMutationVariables,
  UploadMaintenanceModuleAssetMutation,
  UploadMaintenanceModuleAssetMutationFn,
  UploadMaintenanceModuleAssetMutationHookResult,
  UploadMaintenanceModuleAssetMutationResult,
  UploadMaintenanceModuleAssetMutationOptions,
} from "./uploadMaintenanceModuleAsset";

export {
  UploadMaintenanceModuleAssetDocument,
  useUploadMaintenanceModuleAssetMutation,
} from "./uploadMaintenanceModuleAsset";

export type {
  UploadLeadDocumentsMutationVariables,
  UploadLeadDocumentsMutation,
  UploadLeadDocumentsMutationFn,
  UploadLeadDocumentsMutationHookResult,
  UploadLeadDocumentsMutationResult,
  UploadLeadDocumentsMutationOptions,
} from "./uploadLeadDocuments";

export {
  UploadLeadDocumentsDocument,
  useUploadLeadDocumentsMutation,
} from "./uploadLeadDocuments";

export type {
  UploadLeadDocumentMutationVariables,
  UploadLeadDocumentMutation,
  UploadLeadDocumentMutationFn,
  UploadLeadDocumentMutationHookResult,
  UploadLeadDocumentMutationResult,
  UploadLeadDocumentMutationOptions,
} from "./uploadLeadDocument";

export {
  UploadLeadDocumentDocument,
  useUploadLeadDocumentMutation,
} from "./uploadLeadDocument";

export type {
  UploadGiftVoucherModuleAssetMutationVariables,
  UploadGiftVoucherModuleAssetMutation,
  UploadGiftVoucherModuleAssetMutationFn,
  UploadGiftVoucherModuleAssetMutationHookResult,
  UploadGiftVoucherModuleAssetMutationResult,
  UploadGiftVoucherModuleAssetMutationOptions,
} from "./uploadGiftVoucherModuleAsset";

export {
  UploadGiftVoucherModuleAssetDocument,
  useUploadGiftVoucherModuleAssetMutation,
} from "./uploadGiftVoucherModuleAsset";

export type {
  UploadGiftVoucherDocumentMutationVariables,
  UploadGiftVoucherDocumentMutation,
  UploadGiftVoucherDocumentMutationFn,
  UploadGiftVoucherDocumentMutationHookResult,
  UploadGiftVoucherDocumentMutationResult,
  UploadGiftVoucherDocumentMutationOptions,
} from "./uploadGiftVoucherDocument";

export {
  UploadGiftVoucherDocumentDocument,
  useUploadGiftVoucherDocumentMutation,
} from "./uploadGiftVoucherDocument";

export type {
  UploadFinderApplicationModuleAssetMutationVariables,
  UploadFinderApplicationModuleAssetMutation,
  UploadFinderApplicationModuleAssetMutationFn,
  UploadFinderApplicationModuleAssetMutationHookResult,
  UploadFinderApplicationModuleAssetMutationResult,
  UploadFinderApplicationModuleAssetMutationOptions,
} from "./uploadFinderApplicationModuleAsset";

export {
  UploadFinderApplicationModuleAssetDocument,
  useUploadFinderApplicationModuleAssetMutation,
} from "./uploadFinderApplicationModuleAsset";

export type {
  UploadEventLevelAssetMutationVariables,
  UploadEventLevelAssetMutation,
  UploadEventLevelAssetMutationFn,
  UploadEventLevelAssetMutationHookResult,
  UploadEventLevelAssetMutationResult,
  UploadEventLevelAssetMutationOptions,
} from "./uploadEventLevelAsset";

export {
  UploadEventLevelAssetDocument,
  useUploadEventLevelAssetMutation,
} from "./uploadEventLevelAsset";

export type {
  UploadEventApplicationModuleAssetMutationVariables,
  UploadEventApplicationModuleAssetMutation,
  UploadEventApplicationModuleAssetMutationFn,
  UploadEventApplicationModuleAssetMutationHookResult,
  UploadEventApplicationModuleAssetMutationResult,
  UploadEventApplicationModuleAssetMutationOptions,
} from "./uploadEventApplicationModuleAsset";

export {
  UploadEventApplicationModuleAssetDocument,
  useUploadEventApplicationModuleAssetMutation,
} from "./uploadEventApplicationModuleAsset";

export type {
  UploadEdmSocialMediaAssetMutationVariables,
  UploadEdmSocialMediaAssetMutation,
  UploadEdmSocialMediaAssetMutationFn,
  UploadEdmSocialMediaAssetMutationHookResult,
  UploadEdmSocialMediaAssetMutationResult,
  UploadEdmSocialMediaAssetMutationOptions,
} from "./uploadEdmSocialMediaAsset";

export {
  UploadEdmSocialMediaAssetDocument,
  useUploadEdmSocialMediaAssetMutation,
} from "./uploadEdmSocialMediaAsset";

export type {
  UploadDealerSocialMediaAssetMutationVariables,
  UploadDealerSocialMediaAssetMutation,
  UploadDealerSocialMediaAssetMutationFn,
  UploadDealerSocialMediaAssetMutationHookResult,
  UploadDealerSocialMediaAssetMutationResult,
  UploadDealerSocialMediaAssetMutationOptions,
} from "./uploadDealerSocialMediaAsset";

export {
  UploadDealerSocialMediaAssetDocument,
  useUploadDealerSocialMediaAssetMutation,
} from "./uploadDealerSocialMediaAsset";

export type {
  UploadConfiguratorModuleAssetMutationVariables,
  UploadConfiguratorModuleAssetMutation,
  UploadConfiguratorModuleAssetMutationFn,
  UploadConfiguratorModuleAssetMutationHookResult,
  UploadConfiguratorModuleAssetMutationResult,
  UploadConfiguratorModuleAssetMutationOptions,
} from "./uploadConfiguratorModuleAsset";

export {
  UploadConfiguratorModuleAssetDocument,
  useUploadConfiguratorModuleAssetMutation,
} from "./uploadConfiguratorModuleAsset";

export type {
  UploadConfiguratorDescriptionImageMutationVariables,
  UploadConfiguratorDescriptionImageMutation,
  UploadConfiguratorDescriptionImageMutationFn,
  UploadConfiguratorDescriptionImageMutationHookResult,
  UploadConfiguratorDescriptionImageMutationResult,
  UploadConfiguratorDescriptionImageMutationOptions,
} from "./uploadConfiguratorDescriptionImage";

export {
  UploadConfiguratorDescriptionImageDocument,
  useUploadConfiguratorDescriptionImageMutation,
} from "./uploadConfiguratorDescriptionImage";

export type {
  UploadCompanyAssetMutationVariables,
  UploadCompanyAssetMutation,
  UploadCompanyAssetMutationFn,
  UploadCompanyAssetMutationHookResult,
  UploadCompanyAssetMutationResult,
  UploadCompanyAssetMutationOptions,
} from "./uploadCompanyAsset";

export {
  UploadCompanyAssetDocument,
  useUploadCompanyAssetMutation,
} from "./uploadCompanyAsset";

export type {
  UploadBannerImageMutationVariables,
  UploadBannerImageMutation,
  UploadBannerImageMutationFn,
  UploadBannerImageMutationHookResult,
  UploadBannerImageMutationResult,
  UploadBannerImageMutationOptions,
} from "./uploadBannerImage";

export {
  UploadBannerImageDocument,
  useUploadBannerImageMutation,
} from "./uploadBannerImage";

export type {
  UploadBankAssetMutationVariables,
  UploadBankAssetMutation,
  UploadBankAssetMutationFn,
  UploadBankAssetMutationHookResult,
  UploadBankAssetMutationResult,
  UploadBankAssetMutationOptions,
} from "./uploadBankAsset";

export {
  UploadBankAssetDocument,
  useUploadBankAssetMutation,
} from "./uploadBankAsset";

export type {
  UploadAppointmentModuleAssetMutationVariables,
  UploadAppointmentModuleAssetMutation,
  UploadAppointmentModuleAssetMutationFn,
  UploadAppointmentModuleAssetMutationHookResult,
  UploadAppointmentModuleAssetMutationResult,
  UploadAppointmentModuleAssetMutationOptions,
} from "./uploadAppointmentModuleAsset";

export {
  UploadAppointmentModuleAssetDocument,
  useUploadAppointmentModuleAssetMutation,
} from "./uploadAppointmentModuleAsset";

export type {
  UploadApplicationDocumentsMutationVariables,
  UploadApplicationDocumentsMutation,
  UploadApplicationDocumentsMutationFn,
  UploadApplicationDocumentsMutationHookResult,
  UploadApplicationDocumentsMutationResult,
  UploadApplicationDocumentsMutationOptions,
} from "./uploadApplicationDocuments";

export {
  UploadApplicationDocumentsDocument,
  useUploadApplicationDocumentsMutation,
} from "./uploadApplicationDocuments";

export type {
  UploadApplicationDocumentMutationVariables,
  UploadApplicationDocumentMutation,
  UploadApplicationDocumentMutationFn,
  UploadApplicationDocumentMutationHookResult,
  UploadApplicationDocumentMutationResult,
  UploadApplicationDocumentMutationOptions,
} from "./uploadApplicationDocument";

export {
  UploadApplicationDocumentDocument,
  useUploadApplicationDocumentMutation,
} from "./uploadApplicationDocument";

export type {
  UpdateWhatsappLiveChatSettingsMutationVariables,
  UpdateWhatsappLiveChatSettingsMutation,
  UpdateWhatsappLiveChatSettingsMutationFn,
  UpdateWhatsappLiveChatSettingsMutationHookResult,
  UpdateWhatsappLiveChatSettingsMutationResult,
  UpdateWhatsappLiveChatSettingsMutationOptions,
} from "./updateWhatsappLiveChatSettings";

export {
  UpdateWhatsappLiveChatSettingsDocument,
  useUpdateWhatsappLiveChatSettingsMutation,
} from "./updateWhatsappLiveChatSettings";

export type {
  UpdateWhatsappLiveChatModuleMutationVariables,
  UpdateWhatsappLiveChatModuleMutation,
  UpdateWhatsappLiveChatModuleMutationFn,
  UpdateWhatsappLiveChatModuleMutationHookResult,
  UpdateWhatsappLiveChatModuleMutationResult,
  UpdateWhatsappLiveChatModuleMutationOptions,
} from "./updateWhatsappLiveChatModule";

export {
  UpdateWhatsappLiveChatModuleDocument,
  useUpdateWhatsappLiveChatModuleMutation,
} from "./updateWhatsappLiveChatModule";

export type {
  UpdateWebsiteSocialMediaMutationVariables,
  UpdateWebsiteSocialMediaMutation,
  UpdateWebsiteSocialMediaMutationFn,
  UpdateWebsiteSocialMediaMutationHookResult,
  UpdateWebsiteSocialMediaMutationResult,
  UpdateWebsiteSocialMediaMutationOptions,
} from "./updateWebsiteSocialMedia";

export {
  UpdateWebsiteSocialMediaDocument,
  useUpdateWebsiteSocialMediaMutation,
} from "./updateWebsiteSocialMedia";

export type {
  UpdateWebsiteModuleMutationVariables,
  UpdateWebsiteModuleMutation,
  UpdateWebsiteModuleMutationFn,
  UpdateWebsiteModuleMutationHookResult,
  UpdateWebsiteModuleMutationResult,
  UpdateWebsiteModuleMutationOptions,
} from "./updateWebsiteModule";

export {
  UpdateWebsiteModuleDocument,
  useUpdateWebsiteModuleMutation,
} from "./updateWebsiteModule";

export type {
  UpdateWebPageEndpointMutationVariables,
  UpdateWebPageEndpointMutation,
  UpdateWebPageEndpointMutationFn,
  UpdateWebPageEndpointMutationHookResult,
  UpdateWebPageEndpointMutationResult,
  UpdateWebPageEndpointMutationOptions,
} from "./updateWebPageEndpoint";

export {
  UpdateWebPageEndpointDocument,
  useUpdateWebPageEndpointMutation,
} from "./updateWebPageEndpoint";

export type {
  UpdateWebPageMutationVariables,
  UpdateWebPageMutation,
  UpdateWebPageMutationFn,
  UpdateWebPageMutationHookResult,
  UpdateWebPageMutationResult,
  UpdateWebPageMutationOptions,
} from "./updateWebPage";

export {
  UpdateWebPageDocument,
  useUpdateWebPageMutation,
} from "./updateWebPage";

export type {
  UpdateWebCalcSettingMutationVariables,
  UpdateWebCalcSettingMutation,
  UpdateWebCalcSettingMutationFn,
  UpdateWebCalcSettingMutationHookResult,
  UpdateWebCalcSettingMutationResult,
  UpdateWebCalcSettingMutationOptions,
} from "./updateWebCalcSetting";

export {
  UpdateWebCalcSettingDocument,
  useUpdateWebCalcSettingMutation,
} from "./updateWebCalcSetting";

export type {
  UpdateVisitAppointmentModuleEmailContentMutationVariables,
  UpdateVisitAppointmentModuleEmailContentMutation,
  UpdateVisitAppointmentModuleEmailContentMutationFn,
  UpdateVisitAppointmentModuleEmailContentMutationHookResult,
  UpdateVisitAppointmentModuleEmailContentMutationResult,
  UpdateVisitAppointmentModuleEmailContentMutationOptions,
} from "./updateVisitAppointmentModuleEmailContent";

export {
  UpdateVisitAppointmentModuleEmailContentDocument,
  useUpdateVisitAppointmentModuleEmailContentMutation,
} from "./updateVisitAppointmentModuleEmailContent";

export type {
  UpdateVisitAppointmentModuleMutationVariables,
  UpdateVisitAppointmentModuleMutation,
  UpdateVisitAppointmentModuleMutationFn,
  UpdateVisitAppointmentModuleMutationHookResult,
  UpdateVisitAppointmentModuleMutationResult,
  UpdateVisitAppointmentModuleMutationOptions,
} from "./updateVisitAppointmentModule";

export {
  UpdateVisitAppointmentModuleDocument,
  useUpdateVisitAppointmentModuleMutation,
} from "./updateVisitAppointmentModule";

export type {
  UpdateVehicleSalesOfferMutationVariables,
  UpdateVehicleSalesOfferMutation,
  UpdateVehicleSalesOfferMutationFn,
  UpdateVehicleSalesOfferMutationHookResult,
  UpdateVehicleSalesOfferMutationResult,
  UpdateVehicleSalesOfferMutationOptions,
} from "./updateVehicleSalesOffer";

export {
  UpdateVehicleSalesOfferDocument,
  useUpdateVehicleSalesOfferMutation,
} from "./updateVehicleSalesOffer";

export type {
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutationVariables,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutation,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutationFn,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutationHookResult,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutationResult,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutationOptions,
} from "./updateVehicleDataWithPorscheCodeIntegrationModule";

export {
  UpdateVehicleDataWithPorscheCodeIntegrationModuleDocument,
  useUpdateVehicleDataWithPorscheCodeIntegrationModuleMutation,
} from "./updateVehicleDataWithPorscheCodeIntegrationModule";

export type {
  UpdateVariantLabelsMutationVariables,
  UpdateVariantLabelsMutation,
  UpdateVariantLabelsMutationFn,
  UpdateVariantLabelsMutationHookResult,
  UpdateVariantLabelsMutationResult,
  UpdateVariantLabelsMutationOptions,
} from "./updateVariantLabels";

export {
  UpdateVariantLabelsDocument,
  useUpdateVariantLabelsMutation,
} from "./updateVariantLabels";

export type {
  UpdateVariantImageOrderingMutationVariables,
  UpdateVariantImageOrderingMutation,
  UpdateVariantImageOrderingMutationFn,
  UpdateVariantImageOrderingMutationHookResult,
  UpdateVariantImageOrderingMutationResult,
  UpdateVariantImageOrderingMutationOptions,
} from "./updateVariantImageOrdering";

export {
  UpdateVariantImageOrderingDocument,
  useUpdateVariantImageOrderingMutation,
} from "./updateVariantImageOrdering";

export type {
  UpdateVariantConfiguratorMutationVariables,
  UpdateVariantConfiguratorMutation,
  UpdateVariantConfiguratorMutationFn,
  UpdateVariantConfiguratorMutationHookResult,
  UpdateVariantConfiguratorMutationResult,
  UpdateVariantConfiguratorMutationOptions,
} from "./updateVariantConfigurator";

export {
  UpdateVariantConfiguratorDocument,
  useUpdateVariantConfiguratorMutation,
} from "./updateVariantConfigurator";

export type {
  UpdateVariantMutationVariables,
  UpdateVariantMutation,
  UpdateVariantMutationFn,
  UpdateVariantMutationHookResult,
  UpdateVariantMutationResult,
  UpdateVariantMutationOptions,
} from "./updateVariant";

export {
  UpdateVariantDocument,
  useUpdateVariantMutation,
} from "./updateVariant";

export type {
  UpdateUserlikeChatbotSettingsMutationVariables,
  UpdateUserlikeChatbotSettingsMutation,
  UpdateUserlikeChatbotSettingsMutationFn,
  UpdateUserlikeChatbotSettingsMutationHookResult,
  UpdateUserlikeChatbotSettingsMutationResult,
  UpdateUserlikeChatbotSettingsMutationOptions,
} from "./updateUserlikeChatbotSettings";

export {
  UpdateUserlikeChatbotSettingsDocument,
  useUpdateUserlikeChatbotSettingsMutation,
} from "./updateUserlikeChatbotSettings";

export type {
  UpdateUserlikeChatbotModuleMutationVariables,
  UpdateUserlikeChatbotModuleMutation,
  UpdateUserlikeChatbotModuleMutationFn,
  UpdateUserlikeChatbotModuleMutationHookResult,
  UpdateUserlikeChatbotModuleMutationResult,
  UpdateUserlikeChatbotModuleMutationOptions,
} from "./updateUserlikeChatbotModule";

export {
  UpdateUserlikeChatbotModuleDocument,
  useUpdateUserlikeChatbotModuleMutation,
} from "./updateUserlikeChatbotModule";

export type {
  UpdateUserGroupMutationVariables,
  UpdateUserGroupMutation,
  UpdateUserGroupMutationFn,
  UpdateUserGroupMutationHookResult,
  UpdateUserGroupMutationResult,
  UpdateUserGroupMutationOptions,
} from "./updateUserGroup";

export {
  UpdateUserGroupDocument,
  useUpdateUserGroupMutation,
} from "./updateUserGroup";

export type {
  UpdateUserMutationVariables,
  UpdateUserMutation,
  UpdateUserMutationFn,
  UpdateUserMutationHookResult,
  UpdateUserMutationResult,
  UpdateUserMutationOptions,
} from "./updateUser";

export { UpdateUserDocument, useUpdateUserMutation } from "./updateUser";

export type {
  UpdateUobBankIntegrationSettingMutationVariables,
  UpdateUobBankIntegrationSettingMutation,
  UpdateUobBankIntegrationSettingMutationFn,
  UpdateUobBankIntegrationSettingMutationHookResult,
  UpdateUobBankIntegrationSettingMutationResult,
  UpdateUobBankIntegrationSettingMutationOptions,
} from "./updateUobBankIntegrationSetting";

export {
  UpdateUobBankIntegrationSettingDocument,
  useUpdateUobBankIntegrationSettingMutation,
} from "./updateUobBankIntegrationSetting";

export type {
  UpdateTtbPaymentSettingsMutationVariables,
  UpdateTtbPaymentSettingsMutation,
  UpdateTtbPaymentSettingsMutationFn,
  UpdateTtbPaymentSettingsMutationHookResult,
  UpdateTtbPaymentSettingsMutationResult,
  UpdateTtbPaymentSettingsMutationOptions,
} from "./updateTtbPaymentSettings";

export {
  UpdateTtbPaymentSettingsDocument,
  useUpdateTtbPaymentSettingsMutation,
} from "./updateTtbPaymentSettings";

export type {
  UpdateTtbPaymentModuleMutationVariables,
  UpdateTtbPaymentModuleMutation,
  UpdateTtbPaymentModuleMutationFn,
  UpdateTtbPaymentModuleMutationHookResult,
  UpdateTtbPaymentModuleMutationResult,
  UpdateTtbPaymentModuleMutationOptions,
} from "./updateTtbPaymentModule";

export {
  UpdateTtbPaymentModuleDocument,
  useUpdateTtbPaymentModuleMutation,
} from "./updateTtbPaymentModule";

export type {
  UpdateTrimBlockMutationVariables,
  UpdateTrimBlockMutation,
  UpdateTrimBlockMutationFn,
  UpdateTrimBlockMutationHookResult,
  UpdateTrimBlockMutationResult,
  UpdateTrimBlockMutationOptions,
} from "./updateTrimBlock";

export {
  UpdateTrimBlockDocument,
  useUpdateTrimBlockMutation,
} from "./updateTrimBlock";

export type {
  UpdateTradeInSalesOfferMutationVariables,
  UpdateTradeInSalesOfferMutation,
  UpdateTradeInSalesOfferMutationFn,
  UpdateTradeInSalesOfferMutationHookResult,
  UpdateTradeInSalesOfferMutationResult,
  UpdateTradeInSalesOfferMutationOptions,
} from "./updateTradeInSalesOffer";

export {
  UpdateTradeInSalesOfferDocument,
  useUpdateTradeInSalesOfferMutation,
} from "./updateTradeInSalesOffer";

export type {
  UpdateTradeInModuleMutationVariables,
  UpdateTradeInModuleMutation,
  UpdateTradeInModuleMutationFn,
  UpdateTradeInModuleMutationHookResult,
  UpdateTradeInModuleMutationResult,
  UpdateTradeInModuleMutationOptions,
} from "./updateTradeInModule";

export {
  UpdateTradeInModuleDocument,
  useUpdateTradeInModuleMutation,
} from "./updateTradeInModule";

export type {
  UpdateTextConsentsAndDeclarationsMutationVariables,
  UpdateTextConsentsAndDeclarationsMutation,
  UpdateTextConsentsAndDeclarationsMutationFn,
  UpdateTextConsentsAndDeclarationsMutationHookResult,
  UpdateTextConsentsAndDeclarationsMutationResult,
  UpdateTextConsentsAndDeclarationsMutationOptions,
} from "./updateTextConsentsAndDeclarations";

export {
  UpdateTextConsentsAndDeclarationsDocument,
  useUpdateTextConsentsAndDeclarationsMutation,
} from "./updateTextConsentsAndDeclarations";

export type {
  UpdateTestDriveDataMutationVariables,
  UpdateTestDriveDataMutation,
  UpdateTestDriveDataMutationFn,
  UpdateTestDriveDataMutationHookResult,
  UpdateTestDriveDataMutationResult,
  UpdateTestDriveDataMutationOptions,
} from "./updateTestDriveData";

export {
  UpdateTestDriveDataDocument,
  useUpdateTestDriveDataMutation,
} from "./updateTestDriveData";

export type {
  UpdateStockInventoryMutationVariables,
  UpdateStockInventoryMutation,
  UpdateStockInventoryMutationFn,
  UpdateStockInventoryMutationHookResult,
  UpdateStockInventoryMutationResult,
  UpdateStockInventoryMutationOptions,
} from "./updateStockInventory";

export {
  UpdateStockInventoryDocument,
  useUpdateStockInventoryMutation,
} from "./updateStockInventory";

export type {
  UpdateStandardApplicationVehicleAssignmentsMutationVariables,
  UpdateStandardApplicationVehicleAssignmentsMutation,
  UpdateStandardApplicationVehicleAssignmentsMutationFn,
  UpdateStandardApplicationVehicleAssignmentsMutationHookResult,
  UpdateStandardApplicationVehicleAssignmentsMutationResult,
  UpdateStandardApplicationVehicleAssignmentsMutationOptions,
} from "./updateStandardApplicationVehicleAssignments";

export {
  UpdateStandardApplicationVehicleAssignmentsDocument,
  useUpdateStandardApplicationVehicleAssignmentsMutation,
} from "./updateStandardApplicationVehicleAssignments";

export type {
  UpdateStandardApplicationPublicAccessEntrypointMutationVariables,
  UpdateStandardApplicationPublicAccessEntrypointMutation,
  UpdateStandardApplicationPublicAccessEntrypointMutationFn,
  UpdateStandardApplicationPublicAccessEntrypointMutationHookResult,
  UpdateStandardApplicationPublicAccessEntrypointMutationResult,
  UpdateStandardApplicationPublicAccessEntrypointMutationOptions,
} from "./updateStandardApplicationPublicAccessEntrypoint";

export {
  UpdateStandardApplicationPublicAccessEntrypointDocument,
  useUpdateStandardApplicationPublicAccessEntrypointMutation,
} from "./updateStandardApplicationPublicAccessEntrypoint";

export type {
  UpdateStandardApplicationModuleMainDetailsMutationVariables,
  UpdateStandardApplicationModuleMainDetailsMutation,
  UpdateStandardApplicationModuleMainDetailsMutationFn,
  UpdateStandardApplicationModuleMainDetailsMutationHookResult,
  UpdateStandardApplicationModuleMainDetailsMutationResult,
  UpdateStandardApplicationModuleMainDetailsMutationOptions,
} from "./updateStandardApplicationModuleMainDetails";

export {
  UpdateStandardApplicationModuleMainDetailsDocument,
  useUpdateStandardApplicationModuleMainDetailsMutation,
} from "./updateStandardApplicationModuleMainDetails";

export type {
  UpdateStandardApplicationModuleEmailContentMutationVariables,
  UpdateStandardApplicationModuleEmailContentMutation,
  UpdateStandardApplicationModuleEmailContentMutationFn,
  UpdateStandardApplicationModuleEmailContentMutationHookResult,
  UpdateStandardApplicationModuleEmailContentMutationResult,
  UpdateStandardApplicationModuleEmailContentMutationOptions,
} from "./updateStandardApplicationModuleEmailContent";

export {
  UpdateStandardApplicationModuleEmailContentDocument,
  useUpdateStandardApplicationModuleEmailContentMutation,
} from "./updateStandardApplicationModuleEmailContent";

export type {
  UpdateStandardApplicationJourneyMutationVariables,
  UpdateStandardApplicationJourneyMutation,
  UpdateStandardApplicationJourneyMutationFn,
  UpdateStandardApplicationJourneyMutationHookResult,
  UpdateStandardApplicationJourneyMutationResult,
  UpdateStandardApplicationJourneyMutationOptions,
} from "./updateStandardApplicationJourney";

export {
  UpdateStandardApplicationJourneyDocument,
  useUpdateStandardApplicationJourneyMutation,
} from "./updateStandardApplicationJourney";

export type {
  UpdateStandardApplicationFinanceProductAssignmentsMutationVariables,
  UpdateStandardApplicationFinanceProductAssignmentsMutation,
  UpdateStandardApplicationFinanceProductAssignmentsMutationFn,
  UpdateStandardApplicationFinanceProductAssignmentsMutationHookResult,
  UpdateStandardApplicationFinanceProductAssignmentsMutationResult,
  UpdateStandardApplicationFinanceProductAssignmentsMutationOptions,
} from "./updateStandardApplicationFinanceProductAssignments";

export {
  UpdateStandardApplicationFinanceProductAssignmentsDocument,
  useUpdateStandardApplicationFinanceProductAssignmentsMutation,
} from "./updateStandardApplicationFinanceProductAssignments";

export type {
  UpdateStandardApplicationEntrypointMutationVariables,
  UpdateStandardApplicationEntrypointMutation,
  UpdateStandardApplicationEntrypointMutationFn,
  UpdateStandardApplicationEntrypointMutationHookResult,
  UpdateStandardApplicationEntrypointMutationResult,
  UpdateStandardApplicationEntrypointMutationOptions,
} from "./updateStandardApplicationEntrypoint";

export {
  UpdateStandardApplicationEntrypointDocument,
  useUpdateStandardApplicationEntrypointMutation,
} from "./updateStandardApplicationEntrypoint";

export type {
  UpdateStandardApplicationDraftMutationVariables,
  UpdateStandardApplicationDraftMutation,
  UpdateStandardApplicationDraftMutationFn,
  UpdateStandardApplicationDraftMutationHookResult,
  UpdateStandardApplicationDraftMutationResult,
  UpdateStandardApplicationDraftMutationOptions,
} from "./updateStandardApplicationDraft";

export {
  UpdateStandardApplicationDraftDocument,
  useUpdateStandardApplicationDraftMutation,
} from "./updateStandardApplicationDraft";

export type {
  UpdateStandardApplicationConfigurationMutationVariables,
  UpdateStandardApplicationConfigurationMutation,
  UpdateStandardApplicationConfigurationMutationFn,
  UpdateStandardApplicationConfigurationMutationHookResult,
  UpdateStandardApplicationConfigurationMutationResult,
  UpdateStandardApplicationConfigurationMutationOptions,
} from "./updateStandardApplicationConfiguration";

export {
  UpdateStandardApplicationConfigurationDocument,
  useUpdateStandardApplicationConfigurationMutation,
} from "./updateStandardApplicationConfiguration";

export type {
  UpdateSimpleVehicleManagementModuleMutationVariables,
  UpdateSimpleVehicleManagementModuleMutation,
  UpdateSimpleVehicleManagementModuleMutationFn,
  UpdateSimpleVehicleManagementModuleMutationHookResult,
  UpdateSimpleVehicleManagementModuleMutationResult,
  UpdateSimpleVehicleManagementModuleMutationOptions,
} from "./updateSimpleVehicleManagementModule";

export {
  UpdateSimpleVehicleManagementModuleDocument,
  useUpdateSimpleVehicleManagementModuleMutation,
} from "./updateSimpleVehicleManagementModule";

export type {
  UpdateSalesOfferModuleEmailContentMutationVariables,
  UpdateSalesOfferModuleEmailContentMutation,
  UpdateSalesOfferModuleEmailContentMutationFn,
  UpdateSalesOfferModuleEmailContentMutationHookResult,
  UpdateSalesOfferModuleEmailContentMutationResult,
  UpdateSalesOfferModuleEmailContentMutationOptions,
} from "./updateSalesOfferModuleEmailContent";

export {
  UpdateSalesOfferModuleEmailContentDocument,
  useUpdateSalesOfferModuleEmailContentMutation,
} from "./updateSalesOfferModuleEmailContent";

export type {
  UpdateSalesOfferModuleMutationVariables,
  UpdateSalesOfferModuleMutation,
  UpdateSalesOfferModuleMutationFn,
  UpdateSalesOfferModuleMutationHookResult,
  UpdateSalesOfferModuleMutationResult,
  UpdateSalesOfferModuleMutationOptions,
} from "./updateSalesOfferModule";

export {
  UpdateSalesOfferModuleDocument,
  useUpdateSalesOfferModuleMutation,
} from "./updateSalesOfferModule";

export type {
  UpdateSalesControlBoardModuleByDealerMutationVariables,
  UpdateSalesControlBoardModuleByDealerMutation,
  UpdateSalesControlBoardModuleByDealerMutationFn,
  UpdateSalesControlBoardModuleByDealerMutationHookResult,
  UpdateSalesControlBoardModuleByDealerMutationResult,
  UpdateSalesControlBoardModuleByDealerMutationOptions,
} from "./updateSalesControlBoardModuleByDealer";

export {
  UpdateSalesControlBoardModuleByDealerDocument,
  useUpdateSalesControlBoardModuleByDealerMutation,
} from "./updateSalesControlBoardModuleByDealer";

export type {
  UpdateSalesControlBoardModuleMutationVariables,
  UpdateSalesControlBoardModuleMutation,
  UpdateSalesControlBoardModuleMutationFn,
  UpdateSalesControlBoardModuleMutationHookResult,
  UpdateSalesControlBoardModuleMutationResult,
  UpdateSalesControlBoardModuleMutationOptions,
} from "./updateSalesControlBoardModule";

export {
  UpdateSalesControlBoardModuleDocument,
  useUpdateSalesControlBoardModuleMutation,
} from "./updateSalesControlBoardModule";

export type {
  UpdateRouterPathScriptsMutationVariables,
  UpdateRouterPathScriptsMutation,
  UpdateRouterPathScriptsMutationFn,
  UpdateRouterPathScriptsMutationHookResult,
  UpdateRouterPathScriptsMutationResult,
  UpdateRouterPathScriptsMutationOptions,
} from "./updateRouterPathScripts";

export {
  UpdateRouterPathScriptsDocument,
  useUpdateRouterPathScriptsMutation,
} from "./updateRouterPathScripts";

export type {
  UpdateRouterMenuMutationVariables,
  UpdateRouterMenuMutation,
  UpdateRouterMenuMutationFn,
  UpdateRouterMenuMutationHookResult,
  UpdateRouterMenuMutationResult,
  UpdateRouterMenuMutationOptions,
} from "./updateRouterMenu";

export {
  UpdateRouterMenuDocument,
  useUpdateRouterMenuMutation,
} from "./updateRouterMenu";

export type {
  UpdateRouterMutationVariables,
  UpdateRouterMutation,
  UpdateRouterMutationFn,
  UpdateRouterMutationHookResult,
  UpdateRouterMutationResult,
  UpdateRouterMutationOptions,
} from "./updateRouter";

export { UpdateRouterDocument, useUpdateRouterMutation } from "./updateRouter";

export type {
  UpdateRoleMutationVariables,
  UpdateRoleMutation,
  UpdateRoleMutationFn,
  UpdateRoleMutationHookResult,
  UpdateRoleMutationResult,
  UpdateRoleMutationOptions,
} from "./updateRole";

export { UpdateRoleDocument, useUpdateRoleMutation } from "./updateRole";

export type {
  UpdatePromoCodeModuleMutationVariables,
  UpdatePromoCodeModuleMutation,
  UpdatePromoCodeModuleMutationFn,
  UpdatePromoCodeModuleMutationHookResult,
  UpdatePromoCodeModuleMutationResult,
  UpdatePromoCodeModuleMutationOptions,
} from "./updatePromoCodeModule";

export {
  UpdatePromoCodeModuleDocument,
  useUpdatePromoCodeModuleMutation,
} from "./updatePromoCodeModule";

export type {
  UpdatePromoCodeMutationVariables,
  UpdatePromoCodeMutation,
  UpdatePromoCodeMutationFn,
  UpdatePromoCodeMutationHookResult,
  UpdatePromoCodeMutationResult,
  UpdatePromoCodeMutationOptions,
} from "./updatePromoCode";

export {
  UpdatePromoCodeDocument,
  useUpdatePromoCodeMutation,
} from "./updatePromoCode";

export type {
  UpdatePorscheRetainModuleMutationVariables,
  UpdatePorscheRetainModuleMutation,
  UpdatePorscheRetainModuleMutationFn,
  UpdatePorscheRetainModuleMutationHookResult,
  UpdatePorscheRetainModuleMutationResult,
  UpdatePorscheRetainModuleMutationOptions,
} from "./updatePorscheRetainModule";

export {
  UpdatePorscheRetainModuleDocument,
  useUpdatePorscheRetainModuleMutation,
} from "./updatePorscheRetainModule";

export type {
  UpdatePorschePaymentSettingsMutationVariables,
  UpdatePorschePaymentSettingsMutation,
  UpdatePorschePaymentSettingsMutationFn,
  UpdatePorschePaymentSettingsMutationHookResult,
  UpdatePorschePaymentSettingsMutationResult,
  UpdatePorschePaymentSettingsMutationOptions,
} from "./updatePorschePaymentSettings";

export {
  UpdatePorschePaymentSettingsDocument,
  useUpdatePorschePaymentSettingsMutation,
} from "./updatePorschePaymentSettings";

export type {
  UpdatePorschePaymentModuleMutationVariables,
  UpdatePorschePaymentModuleMutation,
  UpdatePorschePaymentModuleMutationFn,
  UpdatePorschePaymentModuleMutationHookResult,
  UpdatePorschePaymentModuleMutationResult,
  UpdatePorschePaymentModuleMutationOptions,
} from "./updatePorschePaymentModule";

export {
  UpdatePorschePaymentModuleDocument,
  useUpdatePorschePaymentModuleMutation,
} from "./updatePorschePaymentModule";

export type {
  UpdatePorscheMasterDataModuleMutationVariables,
  UpdatePorscheMasterDataModuleMutation,
  UpdatePorscheMasterDataModuleMutationFn,
  UpdatePorscheMasterDataModuleMutationHookResult,
  UpdatePorscheMasterDataModuleMutationResult,
  UpdatePorscheMasterDataModuleMutationOptions,
} from "./updatePorscheMasterDataModule";

export {
  UpdatePorscheMasterDataModuleDocument,
  useUpdatePorscheMasterDataModuleMutation,
} from "./updatePorscheMasterDataModule";

export type {
  UpdatePorscheIdModuleMutationVariables,
  UpdatePorscheIdModuleMutation,
  UpdatePorscheIdModuleMutationFn,
  UpdatePorscheIdModuleMutationHookResult,
  UpdatePorscheIdModuleMutationResult,
  UpdatePorscheIdModuleMutationOptions,
} from "./updatePorscheIdModule";

export {
  UpdatePorscheIdModuleDocument,
  useUpdatePorscheIdModuleMutation,
} from "./updatePorscheIdModule";

export type {
  UpdatePayGatePaymentSettingsMutationVariables,
  UpdatePayGatePaymentSettingsMutation,
  UpdatePayGatePaymentSettingsMutationFn,
  UpdatePayGatePaymentSettingsMutationHookResult,
  UpdatePayGatePaymentSettingsMutationResult,
  UpdatePayGatePaymentSettingsMutationOptions,
} from "./updatePayGatePaymentSettings";

export {
  UpdatePayGatePaymentSettingsDocument,
  useUpdatePayGatePaymentSettingsMutation,
} from "./updatePayGatePaymentSettings";

export type {
  UpdatePayGatePaymentModuleMutationVariables,
  UpdatePayGatePaymentModuleMutation,
  UpdatePayGatePaymentModuleMutationFn,
  UpdatePayGatePaymentModuleMutationHookResult,
  UpdatePayGatePaymentModuleMutationResult,
  UpdatePayGatePaymentModuleMutationOptions,
} from "./updatePayGatePaymentModule";

export {
  UpdatePayGatePaymentModuleDocument,
  useUpdatePayGatePaymentModuleMutation,
} from "./updatePayGatePaymentModule";

export type {
  UpdatePackageSettingMutationVariables,
  UpdatePackageSettingMutation,
  UpdatePackageSettingMutationFn,
  UpdatePackageSettingMutationHookResult,
  UpdatePackageSettingMutationResult,
  UpdatePackageSettingMutationOptions,
} from "./updatePackageSetting";

export {
  UpdatePackageSettingDocument,
  useUpdatePackageSettingMutation,
} from "./updatePackageSetting";

export type {
  UpdatePackageBlockMutationVariables,
  UpdatePackageBlockMutation,
  UpdatePackageBlockMutationFn,
  UpdatePackageBlockMutationHookResult,
  UpdatePackageBlockMutationResult,
  UpdatePackageBlockMutationOptions,
} from "./updatePackageBlock";

export {
  UpdatePackageBlockDocument,
  useUpdatePackageBlockMutation,
} from "./updatePackageBlock";

export type {
  UpdateOptionsBlockMutationVariables,
  UpdateOptionsBlockMutation,
  UpdateOptionsBlockMutationFn,
  UpdateOptionsBlockMutationHookResult,
  UpdateOptionsBlockMutationResult,
  UpdateOptionsBlockMutationOptions,
} from "./updateOptionsBlock";

export {
  UpdateOptionsBlockDocument,
  useUpdateOptionsBlockMutation,
} from "./updateOptionsBlock";

export type {
  UpdateOidcModuleMutationVariables,
  UpdateOidcModuleMutation,
  UpdateOidcModuleMutationFn,
  UpdateOidcModuleMutationHookResult,
  UpdateOidcModuleMutationResult,
  UpdateOidcModuleMutationOptions,
} from "./updateOIDCModule";

export {
  UpdateOidcModuleDocument,
  useUpdateOidcModuleMutation,
} from "./updateOIDCModule";

export type {
  UpdateOidcClientMutationVariables,
  UpdateOidcClientMutation,
  UpdateOidcClientMutationFn,
  UpdateOidcClientMutationHookResult,
  UpdateOidcClientMutationResult,
  UpdateOidcClientMutationOptions,
} from "./updateOIDCClient";

export {
  UpdateOidcClientDocument,
  useUpdateOidcClientMutation,
} from "./updateOIDCClient";

export type {
  UpdateNamirialSigningModuleMutationVariables,
  UpdateNamirialSigningModuleMutation,
  UpdateNamirialSigningModuleMutationFn,
  UpdateNamirialSigningModuleMutationHookResult,
  UpdateNamirialSigningModuleMutationResult,
  UpdateNamirialSigningModuleMutationOptions,
} from "./updateNamirialSigningModule";

export {
  UpdateNamirialSigningModuleDocument,
  useUpdateNamirialSigningModuleMutation,
} from "./updateNamirialSigningModule";

export type {
  UpdateNamirialSettingsMutationVariables,
  UpdateNamirialSettingsMutation,
  UpdateNamirialSettingsMutationFn,
  UpdateNamirialSettingsMutationHookResult,
  UpdateNamirialSettingsMutationResult,
  UpdateNamirialSettingsMutationOptions,
} from "./updateNamirialSettings";

export {
  UpdateNamirialSettingsDocument,
  useUpdateNamirialSettingsMutation,
} from "./updateNamirialSettings";

export type {
  UpdateMyInfoSettingMutationVariables,
  UpdateMyInfoSettingMutation,
  UpdateMyInfoSettingMutationFn,
  UpdateMyInfoSettingMutationHookResult,
  UpdateMyInfoSettingMutationResult,
  UpdateMyInfoSettingMutationOptions,
} from "./updateMyInfoSetting";

export {
  UpdateMyInfoSettingDocument,
  useUpdateMyInfoSettingMutation,
} from "./updateMyInfoSetting";

export type {
  UpdateMyInfoModuleMutationVariables,
  UpdateMyInfoModuleMutation,
  UpdateMyInfoModuleMutationFn,
  UpdateMyInfoModuleMutationHookResult,
  UpdateMyInfoModuleMutationResult,
  UpdateMyInfoModuleMutationOptions,
} from "./updateMyInfoModule";

export {
  UpdateMyInfoModuleDocument,
  useUpdateMyInfoModuleMutation,
} from "./updateMyInfoModule";

export type {
  UpdateModelConfiguratorMutationVariables,
  UpdateModelConfiguratorMutation,
  UpdateModelConfiguratorMutationFn,
  UpdateModelConfiguratorMutationHookResult,
  UpdateModelConfiguratorMutationResult,
  UpdateModelConfiguratorMutationOptions,
} from "./updateModelConfigurator";

export {
  UpdateModelConfiguratorDocument,
  useUpdateModelConfiguratorMutation,
} from "./updateModelConfigurator";

export type {
  UpdateModelMutationVariables,
  UpdateModelMutation,
  UpdateModelMutationFn,
  UpdateModelMutationHookResult,
  UpdateModelMutationResult,
  UpdateModelMutationOptions,
} from "./updateModel";

export { UpdateModelDocument, useUpdateModelMutation } from "./updateModel";

export type {
  UpdateMobilityModuleMutationVariables,
  UpdateMobilityModuleMutation,
  UpdateMobilityModuleMutationFn,
  UpdateMobilityModuleMutationHookResult,
  UpdateMobilityModuleMutationResult,
  UpdateMobilityModuleMutationOptions,
} from "./updateMobilityModule";

export {
  UpdateMobilityModuleDocument,
  useUpdateMobilityModuleMutation,
} from "./updateMobilityModule";

export type {
  UpdateMobilityEmailContentMutationVariables,
  UpdateMobilityEmailContentMutation,
  UpdateMobilityEmailContentMutationFn,
  UpdateMobilityEmailContentMutationHookResult,
  UpdateMobilityEmailContentMutationResult,
  UpdateMobilityEmailContentMutationOptions,
} from "./updateMobilityEmailContent";

export {
  UpdateMobilityEmailContentDocument,
  useUpdateMobilityEmailContentMutation,
} from "./updateMobilityEmailContent";

export type {
  UpdateMobilityDepositAmountMutationVariables,
  UpdateMobilityDepositAmountMutation,
  UpdateMobilityDepositAmountMutationFn,
  UpdateMobilityDepositAmountMutationHookResult,
  UpdateMobilityDepositAmountMutationResult,
  UpdateMobilityDepositAmountMutationOptions,
} from "./updateMobilityDepositAmount";

export {
  UpdateMobilityDepositAmountDocument,
  useUpdateMobilityDepositAmountMutation,
} from "./updateMobilityDepositAmount";

export type {
  UpdateMobilityApplicationEntrypointMutationVariables,
  UpdateMobilityApplicationEntrypointMutation,
  UpdateMobilityApplicationEntrypointMutationFn,
  UpdateMobilityApplicationEntrypointMutationHookResult,
  UpdateMobilityApplicationEntrypointMutationResult,
  UpdateMobilityApplicationEntrypointMutationOptions,
} from "./updateMobilityApplicationEntrypoint";

export {
  UpdateMobilityApplicationEntrypointDocument,
  useUpdateMobilityApplicationEntrypointMutation,
} from "./updateMobilityApplicationEntrypoint";

export type {
  UpdateMobilityApplicationDraftMutationVariables,
  UpdateMobilityApplicationDraftMutation,
  UpdateMobilityApplicationDraftMutationFn,
  UpdateMobilityApplicationDraftMutationHookResult,
  UpdateMobilityApplicationDraftMutationResult,
  UpdateMobilityApplicationDraftMutationOptions,
} from "./updateMobilityApplicationDraft";

export {
  UpdateMobilityApplicationDraftDocument,
  useUpdateMobilityApplicationDraftMutation,
} from "./updateMobilityApplicationDraft";

export type {
  UpdateMobilityApplicationMutationVariables,
  UpdateMobilityApplicationMutation,
  UpdateMobilityApplicationMutationFn,
  UpdateMobilityApplicationMutationHookResult,
  UpdateMobilityApplicationMutationResult,
  UpdateMobilityApplicationMutationOptions,
} from "./updateMobilityApplication";

export {
  UpdateMobilityApplicationDocument,
  useUpdateMobilityApplicationMutation,
} from "./updateMobilityApplication";

export type {
  UpdateMobilityAddonMutationVariables,
  UpdateMobilityAddonMutation,
  UpdateMobilityAddonMutationFn,
  UpdateMobilityAddonMutationHookResult,
  UpdateMobilityAddonMutationResult,
  UpdateMobilityAddonMutationOptions,
} from "./updateMobilityAddon";

export {
  UpdateMobilityAddonDocument,
  useUpdateMobilityAddonMutation,
} from "./updateMobilityAddon";

export type {
  UpdateMobilityAdditionalInfoMutationVariables,
  UpdateMobilityAdditionalInfoMutation,
  UpdateMobilityAdditionalInfoMutationFn,
  UpdateMobilityAdditionalInfoMutationHookResult,
  UpdateMobilityAdditionalInfoMutationResult,
  UpdateMobilityAdditionalInfoMutationOptions,
} from "./updateMobilityAdditionalInfo";

export {
  UpdateMobilityAdditionalInfoDocument,
  useUpdateMobilityAdditionalInfoMutation,
} from "./updateMobilityAdditionalInfo";

export type {
  UpdateMaybankIntegrationSettingMutationVariables,
  UpdateMaybankIntegrationSettingMutation,
  UpdateMaybankIntegrationSettingMutationFn,
  UpdateMaybankIntegrationSettingMutationHookResult,
  UpdateMaybankIntegrationSettingMutationResult,
  UpdateMaybankIntegrationSettingMutationOptions,
} from "./updateMaybankIntegrationSetting";

export {
  UpdateMaybankIntegrationSettingDocument,
  useUpdateMaybankIntegrationSettingMutation,
} from "./updateMaybankIntegrationSetting";

export type {
  UpdateMarketingModuleMutationVariables,
  UpdateMarketingModuleMutation,
  UpdateMarketingModuleMutationFn,
  UpdateMarketingModuleMutationHookResult,
  UpdateMarketingModuleMutationResult,
  UpdateMarketingModuleMutationOptions,
} from "./updateMarketingModule";

export {
  UpdateMarketingModuleDocument,
  useUpdateMarketingModuleMutation,
} from "./updateMarketingModule";

export type {
  UpdateMarketingConsentsAndDeclarationsMutationVariables,
  UpdateMarketingConsentsAndDeclarationsMutation,
  UpdateMarketingConsentsAndDeclarationsMutationFn,
  UpdateMarketingConsentsAndDeclarationsMutationHookResult,
  UpdateMarketingConsentsAndDeclarationsMutationResult,
  UpdateMarketingConsentsAndDeclarationsMutationOptions,
} from "./updateMarketingConsentsAndDeclarations";

export {
  UpdateMarketingConsentsAndDeclarationsDocument,
  useUpdateMarketingConsentsAndDeclarationsMutation,
} from "./updateMarketingConsentsAndDeclarations";

export type {
  UpdateMakeMutationVariables,
  UpdateMakeMutation,
  UpdateMakeMutationFn,
  UpdateMakeMutationHookResult,
  UpdateMakeMutationResult,
  UpdateMakeMutationOptions,
} from "./updateMake";

export { UpdateMakeDocument, useUpdateMakeMutation } from "./updateMake";

export type {
  UpdateMaintenanceModuleDetailsMutationVariables,
  UpdateMaintenanceModuleDetailsMutation,
  UpdateMaintenanceModuleDetailsMutationFn,
  UpdateMaintenanceModuleDetailsMutationHookResult,
  UpdateMaintenanceModuleDetailsMutationResult,
  UpdateMaintenanceModuleDetailsMutationOptions,
} from "./updateMaintenanceModuleDetails";

export {
  UpdateMaintenanceModuleDetailsDocument,
  useUpdateMaintenanceModuleDetailsMutation,
} from "./updateMaintenanceModuleDetails";

export type {
  UpdateMaintenanceModuleMutationVariables,
  UpdateMaintenanceModuleMutation,
  UpdateMaintenanceModuleMutationFn,
  UpdateMaintenanceModuleMutationHookResult,
  UpdateMaintenanceModuleMutationResult,
  UpdateMaintenanceModuleMutationOptions,
} from "./updateMaintenanceModule";

export {
  UpdateMaintenanceModuleDocument,
  useUpdateMaintenanceModuleMutation,
} from "./updateMaintenanceModule";

export type {
  UpdateMainDetailsSalesOfferMutationVariables,
  UpdateMainDetailsSalesOfferMutation,
  UpdateMainDetailsSalesOfferMutationFn,
  UpdateMainDetailsSalesOfferMutationHookResult,
  UpdateMainDetailsSalesOfferMutationResult,
  UpdateMainDetailsSalesOfferMutationOptions,
} from "./updateMainDetailsSalesOffer";

export {
  UpdateMainDetailsSalesOfferDocument,
  useUpdateMainDetailsSalesOfferMutation,
} from "./updateMainDetailsSalesOffer";

export type {
  UpdateLocalCustomerManagementKycFieldsMutationVariables,
  UpdateLocalCustomerManagementKycFieldsMutation,
  UpdateLocalCustomerManagementKycFieldsMutationFn,
  UpdateLocalCustomerManagementKycFieldsMutationHookResult,
  UpdateLocalCustomerManagementKycFieldsMutationResult,
  UpdateLocalCustomerManagementKycFieldsMutationOptions,
} from "./updateLocalCustomerManagementKYCFields";

export {
  UpdateLocalCustomerManagementKycFieldsDocument,
  useUpdateLocalCustomerManagementKycFieldsMutation,
} from "./updateLocalCustomerManagementKYCFields";

export type {
  UpdateLocalCustomerManagementMutationVariables,
  UpdateLocalCustomerManagementMutation,
  UpdateLocalCustomerManagementMutationFn,
  UpdateLocalCustomerManagementMutationHookResult,
  UpdateLocalCustomerManagementMutationResult,
  UpdateLocalCustomerManagementMutationOptions,
} from "./updateLocalCustomerManagement";

export {
  UpdateLocalCustomerManagementDocument,
  useUpdateLocalCustomerManagementMutation,
} from "./updateLocalCustomerManagement";

export type {
  UpdateLeadListEndpointMutationVariables,
  UpdateLeadListEndpointMutation,
  UpdateLeadListEndpointMutationFn,
  UpdateLeadListEndpointMutationHookResult,
  UpdateLeadListEndpointMutationResult,
  UpdateLeadListEndpointMutationOptions,
} from "./updateLeadListEndpoint";

export {
  UpdateLeadListEndpointDocument,
  useUpdateLeadListEndpointMutation,
} from "./updateLeadListEndpoint";

export type {
  UpdateLeadFollowUpMutationVariables,
  UpdateLeadFollowUpMutation,
  UpdateLeadFollowUpMutationFn,
  UpdateLeadFollowUpMutationHookResult,
  UpdateLeadFollowUpMutationResult,
  UpdateLeadFollowUpMutationOptions,
} from "./updateLeadFollowUp";

export {
  UpdateLeadFollowUpDocument,
  useUpdateLeadFollowUpMutation,
} from "./updateLeadFollowUp";

export type {
  UpdateLeadMutationVariables,
  UpdateLeadMutation,
  UpdateLeadMutationFn,
  UpdateLeadMutationHookResult,
  UpdateLeadMutationResult,
  UpdateLeadMutationOptions,
} from "./updateLead";

export { UpdateLeadDocument, useUpdateLeadMutation } from "./updateLead";

export type {
  UpdateLaunchpadApplicationTradeInMutationVariables,
  UpdateLaunchpadApplicationTradeInMutation,
  UpdateLaunchpadApplicationTradeInMutationFn,
  UpdateLaunchpadApplicationTradeInMutationHookResult,
  UpdateLaunchpadApplicationTradeInMutationResult,
  UpdateLaunchpadApplicationTradeInMutationOptions,
} from "./updateLaunchpadApplicationTradeIn";

export {
  UpdateLaunchpadApplicationTradeInDocument,
  useUpdateLaunchpadApplicationTradeInMutation,
} from "./updateLaunchpadApplicationTradeIn";

export type {
  UpdateLaunchPadModuleVehicleAssignmentsMutationVariables,
  UpdateLaunchPadModuleVehicleAssignmentsMutation,
  UpdateLaunchPadModuleVehicleAssignmentsMutationFn,
  UpdateLaunchPadModuleVehicleAssignmentsMutationHookResult,
  UpdateLaunchPadModuleVehicleAssignmentsMutationResult,
  UpdateLaunchPadModuleVehicleAssignmentsMutationOptions,
} from "./updateLaunchPadModuleVehicleAssignments";

export {
  UpdateLaunchPadModuleVehicleAssignmentsDocument,
  useUpdateLaunchPadModuleVehicleAssignmentsMutation,
} from "./updateLaunchPadModuleVehicleAssignments";

export type {
  UpdateLaunchPadModuleMutationVariables,
  UpdateLaunchPadModuleMutation,
  UpdateLaunchPadModuleMutationFn,
  UpdateLaunchPadModuleMutationHookResult,
  UpdateLaunchPadModuleMutationResult,
  UpdateLaunchPadModuleMutationOptions,
} from "./updateLaunchPadModule";

export {
  UpdateLaunchPadModuleDocument,
  useUpdateLaunchPadModuleMutation,
} from "./updateLaunchPadModule";

export type {
  UpdateLaunchPadApplicationEntrypointMutationVariables,
  UpdateLaunchPadApplicationEntrypointMutation,
  UpdateLaunchPadApplicationEntrypointMutationFn,
  UpdateLaunchPadApplicationEntrypointMutationHookResult,
  UpdateLaunchPadApplicationEntrypointMutationResult,
  UpdateLaunchPadApplicationEntrypointMutationOptions,
} from "./updateLaunchPadApplicationEntrypoint";

export {
  UpdateLaunchPadApplicationEntrypointDocument,
  useUpdateLaunchPadApplicationEntrypointMutation,
} from "./updateLaunchPadApplicationEntrypoint";

export type {
  UpdateLanguagePackMutationVariables,
  UpdateLanguagePackMutation,
  UpdateLanguagePackMutationFn,
  UpdateLanguagePackMutationHookResult,
  UpdateLanguagePackMutationResult,
  UpdateLanguagePackMutationOptions,
} from "./updateLanguagePack";

export {
  UpdateLanguagePackDocument,
  useUpdateLanguagePackMutation,
} from "./updateLanguagePack";

export type {
  UpdateLabelsModuleMutationVariables,
  UpdateLabelsModuleMutation,
  UpdateLabelsModuleMutationFn,
  UpdateLabelsModuleMutationHookResult,
  UpdateLabelsModuleMutationResult,
  UpdateLabelsModuleMutationOptions,
} from "./updateLabelsModule";

export {
  UpdateLabelsModuleDocument,
  useUpdateLabelsModuleMutation,
} from "./updateLabelsModule";

export type {
  UpdateLabelsMutationVariables,
  UpdateLabelsMutation,
  UpdateLabelsMutationFn,
  UpdateLabelsMutationHookResult,
  UpdateLabelsMutationResult,
  UpdateLabelsMutationOptions,
} from "./updateLabels";

export { UpdateLabelsDocument, useUpdateLabelsMutation } from "./updateLabels";

export type {
  UpdateKycPresetMutationVariables,
  UpdateKycPresetMutation,
  UpdateKycPresetMutationFn,
  UpdateKycPresetMutationHookResult,
  UpdateKycPresetMutationResult,
  UpdateKycPresetMutationOptions,
} from "./updateKYCPreset";

export {
  UpdateKycPresetDocument,
  useUpdateKycPresetMutation,
} from "./updateKYCPreset";

export type {
  UpdateInventoryMutationVariables,
  UpdateInventoryMutation,
  UpdateInventoryMutationFn,
  UpdateInventoryMutationHookResult,
  UpdateInventoryMutationResult,
  UpdateInventoryMutationOptions,
} from "./updateInventory";

export {
  UpdateInventoryDocument,
  useUpdateInventoryMutation,
} from "./updateInventory";

export type {
  UpdateInsurerMutationVariables,
  UpdateInsurerMutation,
  UpdateInsurerMutationFn,
  UpdateInsurerMutationHookResult,
  UpdateInsurerMutationResult,
  UpdateInsurerMutationOptions,
} from "./updateInsurer";

export {
  UpdateInsurerDocument,
  useUpdateInsurerMutation,
} from "./updateInsurer";

export type {
  UpdateInsuranceSalesOfferMutationVariables,
  UpdateInsuranceSalesOfferMutation,
  UpdateInsuranceSalesOfferMutationFn,
  UpdateInsuranceSalesOfferMutationHookResult,
  UpdateInsuranceSalesOfferMutationResult,
  UpdateInsuranceSalesOfferMutationOptions,
} from "./updateInsuranceSalesOffer";

export {
  UpdateInsuranceSalesOfferDocument,
  useUpdateInsuranceSalesOfferMutation,
} from "./updateInsuranceSalesOffer";

export type {
  UpdateInsuranceProductMutationVariables,
  UpdateInsuranceProductMutation,
  UpdateInsuranceProductMutationFn,
  UpdateInsuranceProductMutationHookResult,
  UpdateInsuranceProductMutationResult,
  UpdateInsuranceProductMutationOptions,
} from "./updateInsuranceProduct";

export {
  UpdateInsuranceProductDocument,
  useUpdateInsuranceProductMutation,
} from "./updateInsuranceProduct";

export type {
  UpdateInsuranceModuleMutationVariables,
  UpdateInsuranceModuleMutation,
  UpdateInsuranceModuleMutationFn,
  UpdateInsuranceModuleMutationHookResult,
  UpdateInsuranceModuleMutationResult,
  UpdateInsuranceModuleMutationOptions,
} from "./updateInsuranceModule";

export {
  UpdateInsuranceModuleDocument,
  useUpdateInsuranceModuleMutation,
} from "./updateInsuranceModule";

export type {
  UpdateHlfBankV2IntegrationSettingMutationVariables,
  UpdateHlfBankV2IntegrationSettingMutation,
  UpdateHlfBankV2IntegrationSettingMutationFn,
  UpdateHlfBankV2IntegrationSettingMutationHookResult,
  UpdateHlfBankV2IntegrationSettingMutationResult,
  UpdateHlfBankV2IntegrationSettingMutationOptions,
} from "./updateHlfBankV2IntegrationSetting";

export {
  UpdateHlfBankV2IntegrationSettingDocument,
  useUpdateHlfBankV2IntegrationSettingMutation,
} from "./updateHlfBankV2IntegrationSetting";

export type {
  UpdateHlfBankIntegrationSettingMutationVariables,
  UpdateHlfBankIntegrationSettingMutation,
  UpdateHlfBankIntegrationSettingMutationFn,
  UpdateHlfBankIntegrationSettingMutationHookResult,
  UpdateHlfBankIntegrationSettingMutationResult,
  UpdateHlfBankIntegrationSettingMutationOptions,
} from "./updateHlfBankIntegrationSetting";

export {
  UpdateHlfBankIntegrationSettingDocument,
  useUpdateHlfBankIntegrationSettingMutation,
} from "./updateHlfBankIntegrationSetting";

export type {
  UpdateGroupConsentsAndDeclarationsMutationVariables,
  UpdateGroupConsentsAndDeclarationsMutation,
  UpdateGroupConsentsAndDeclarationsMutationFn,
  UpdateGroupConsentsAndDeclarationsMutationHookResult,
  UpdateGroupConsentsAndDeclarationsMutationResult,
  UpdateGroupConsentsAndDeclarationsMutationOptions,
} from "./updateGroupConsentsAndDeclarations";

export {
  UpdateGroupConsentsAndDeclarationsDocument,
  useUpdateGroupConsentsAndDeclarationsMutation,
} from "./updateGroupConsentsAndDeclarations";

export type {
  UpdateGiftVoucherModuleEmailContentMutationVariables,
  UpdateGiftVoucherModuleEmailContentMutation,
  UpdateGiftVoucherModuleEmailContentMutationFn,
  UpdateGiftVoucherModuleEmailContentMutationHookResult,
  UpdateGiftVoucherModuleEmailContentMutationResult,
  UpdateGiftVoucherModuleEmailContentMutationOptions,
} from "./updateGiftVoucherModuleEmailContent";

export {
  UpdateGiftVoucherModuleEmailContentDocument,
  useUpdateGiftVoucherModuleEmailContentMutation,
} from "./updateGiftVoucherModuleEmailContent";

export type {
  UpdateGiftVoucherModuleMutationVariables,
  UpdateGiftVoucherModuleMutation,
  UpdateGiftVoucherModuleMutationFn,
  UpdateGiftVoucherModuleMutationHookResult,
  UpdateGiftVoucherModuleMutationResult,
  UpdateGiftVoucherModuleMutationOptions,
} from "./updateGiftVoucherModule";

export {
  UpdateGiftVoucherModuleDocument,
  useUpdateGiftVoucherModuleMutation,
} from "./updateGiftVoucherModule";

export type {
  UpdateGiftVoucherMutationVariables,
  UpdateGiftVoucherMutation,
  UpdateGiftVoucherMutationFn,
  UpdateGiftVoucherMutationHookResult,
  UpdateGiftVoucherMutationResult,
  UpdateGiftVoucherMutationOptions,
} from "./updateGiftVoucher";

export {
  UpdateGiftVoucherDocument,
  useUpdateGiftVoucherMutation,
} from "./updateGiftVoucher";

export type {
  UpdateFiservPaymentSettingsMutationVariables,
  UpdateFiservPaymentSettingsMutation,
  UpdateFiservPaymentSettingsMutationFn,
  UpdateFiservPaymentSettingsMutationHookResult,
  UpdateFiservPaymentSettingsMutationResult,
  UpdateFiservPaymentSettingsMutationOptions,
} from "./updateFiservPaymentSettings";

export {
  UpdateFiservPaymentSettingsDocument,
  useUpdateFiservPaymentSettingsMutation,
} from "./updateFiservPaymentSettings";

export type {
  UpdateFiservPaymentModuleMutationVariables,
  UpdateFiservPaymentModuleMutation,
  UpdateFiservPaymentModuleMutationFn,
  UpdateFiservPaymentModuleMutationHookResult,
  UpdateFiservPaymentModuleMutationResult,
  UpdateFiservPaymentModuleMutationOptions,
} from "./updateFiservPaymentModule";

export {
  UpdateFiservPaymentModuleDocument,
  useUpdateFiservPaymentModuleMutation,
} from "./updateFiservPaymentModule";

export type {
  UpdateFinderVehicleManagementModuleMutationVariables,
  UpdateFinderVehicleManagementModuleMutation,
  UpdateFinderVehicleManagementModuleMutationFn,
  UpdateFinderVehicleManagementModuleMutationHookResult,
  UpdateFinderVehicleManagementModuleMutationResult,
  UpdateFinderVehicleManagementModuleMutationOptions,
} from "./updateFinderVehicleManagementModule";

export {
  UpdateFinderVehicleManagementModuleDocument,
  useUpdateFinderVehicleManagementModuleMutation,
} from "./updateFinderVehicleManagementModule";

export type {
  UpdateFinderVehicleMutationVariables,
  UpdateFinderVehicleMutation,
  UpdateFinderVehicleMutationFn,
  UpdateFinderVehicleMutationHookResult,
  UpdateFinderVehicleMutationResult,
  UpdateFinderVehicleMutationOptions,
} from "./updateFinderVehicle";

export {
  UpdateFinderVehicleDocument,
  useUpdateFinderVehicleMutation,
} from "./updateFinderVehicle";

export type {
  UpdateFinderApplicationPublicModuleEmailContentsMutationVariables,
  UpdateFinderApplicationPublicModuleEmailContentsMutation,
  UpdateFinderApplicationPublicModuleEmailContentsMutationFn,
  UpdateFinderApplicationPublicModuleEmailContentsMutationHookResult,
  UpdateFinderApplicationPublicModuleEmailContentsMutationResult,
  UpdateFinderApplicationPublicModuleEmailContentsMutationOptions,
} from "./updateFinderApplicationPublicModuleEmailContents";

export {
  UpdateFinderApplicationPublicModuleEmailContentsDocument,
  useUpdateFinderApplicationPublicModuleEmailContentsMutation,
} from "./updateFinderApplicationPublicModuleEmailContents";

export type {
  UpdateFinderApplicationPublicAccessEntrypointMutationVariables,
  UpdateFinderApplicationPublicAccessEntrypointMutation,
  UpdateFinderApplicationPublicAccessEntrypointMutationFn,
  UpdateFinderApplicationPublicAccessEntrypointMutationHookResult,
  UpdateFinderApplicationPublicAccessEntrypointMutationResult,
  UpdateFinderApplicationPublicAccessEntrypointMutationOptions,
} from "./updateFinderApplicationPublicAccessEntrypoint";

export {
  UpdateFinderApplicationPublicAccessEntrypointDocument,
  useUpdateFinderApplicationPublicAccessEntrypointMutation,
} from "./updateFinderApplicationPublicAccessEntrypoint";

export type {
  UpdateFinderApplicationPrivateModuleEmailContentsMutationVariables,
  UpdateFinderApplicationPrivateModuleEmailContentsMutation,
  UpdateFinderApplicationPrivateModuleEmailContentsMutationFn,
  UpdateFinderApplicationPrivateModuleEmailContentsMutationHookResult,
  UpdateFinderApplicationPrivateModuleEmailContentsMutationResult,
  UpdateFinderApplicationPrivateModuleEmailContentsMutationOptions,
} from "./updateFinderApplicationPrivateModuleEmailContents";

export {
  UpdateFinderApplicationPrivateModuleEmailContentsDocument,
  useUpdateFinderApplicationPrivateModuleEmailContentsMutation,
} from "./updateFinderApplicationPrivateModuleEmailContents";

export type {
  UpdateFinderApplicationPrivateModuleMutationVariables,
  UpdateFinderApplicationPrivateModuleMutation,
  UpdateFinderApplicationPrivateModuleMutationFn,
  UpdateFinderApplicationPrivateModuleMutationHookResult,
  UpdateFinderApplicationPrivateModuleMutationResult,
  UpdateFinderApplicationPrivateModuleMutationOptions,
} from "./updateFinderApplicationPrivateModule";

export {
  UpdateFinderApplicationPrivateModuleDocument,
  useUpdateFinderApplicationPrivateModuleMutation,
} from "./updateFinderApplicationPrivateModule";

export type {
  UpdateFinderApplicationPublicModuleMutationVariables,
  UpdateFinderApplicationPublicModuleMutation,
  UpdateFinderApplicationPublicModuleMutationFn,
  UpdateFinderApplicationPublicModuleMutationHookResult,
  UpdateFinderApplicationPublicModuleMutationResult,
  UpdateFinderApplicationPublicModuleMutationOptions,
} from "./updateFinderApplicationModule";

export {
  UpdateFinderApplicationPublicModuleDocument,
  useUpdateFinderApplicationPublicModuleMutation,
} from "./updateFinderApplicationModule";

export type {
  UpdateFinderApplicationJourneyMutationVariables,
  UpdateFinderApplicationJourneyMutation,
  UpdateFinderApplicationJourneyMutationFn,
  UpdateFinderApplicationJourneyMutationHookResult,
  UpdateFinderApplicationJourneyMutationResult,
  UpdateFinderApplicationJourneyMutationOptions,
} from "./updateFinderApplicationJourney";

export {
  UpdateFinderApplicationJourneyDocument,
  useUpdateFinderApplicationJourneyMutation,
} from "./updateFinderApplicationJourney";

export type {
  UpdateFinderApplicationEntrypointMutationVariables,
  UpdateFinderApplicationEntrypointMutation,
  UpdateFinderApplicationEntrypointMutationFn,
  UpdateFinderApplicationEntrypointMutationHookResult,
  UpdateFinderApplicationEntrypointMutationResult,
  UpdateFinderApplicationEntrypointMutationOptions,
} from "./updateFinderApplicationEntrypoint";

export {
  UpdateFinderApplicationEntrypointDocument,
  useUpdateFinderApplicationEntrypointMutation,
} from "./updateFinderApplicationEntrypoint";

export type {
  UpdateFinderApplicationDraftMutationVariables,
  UpdateFinderApplicationDraftMutation,
  UpdateFinderApplicationDraftMutationFn,
  UpdateFinderApplicationDraftMutationHookResult,
  UpdateFinderApplicationDraftMutationResult,
  UpdateFinderApplicationDraftMutationOptions,
} from "./updateFinderApplicationDraft";

export {
  UpdateFinderApplicationDraftDocument,
  useUpdateFinderApplicationDraftMutation,
} from "./updateFinderApplicationDraft";

export type {
  UpdateFinderApplicationMutationVariables,
  UpdateFinderApplicationMutation,
  UpdateFinderApplicationMutationFn,
  UpdateFinderApplicationMutationHookResult,
  UpdateFinderApplicationMutationResult,
  UpdateFinderApplicationMutationOptions,
} from "./updateFinderApplication";

export {
  UpdateFinderApplicationDocument,
  useUpdateFinderApplicationMutation,
} from "./updateFinderApplication";

export type {
  UpdateFinanceSalesOfferMutationVariables,
  UpdateFinanceSalesOfferMutation,
  UpdateFinanceSalesOfferMutationFn,
  UpdateFinanceSalesOfferMutationHookResult,
  UpdateFinanceSalesOfferMutationResult,
  UpdateFinanceSalesOfferMutationOptions,
} from "./updateFinanceSalesOffer";

export {
  UpdateFinanceSalesOfferDocument,
  useUpdateFinanceSalesOfferMutation,
} from "./updateFinanceSalesOffer";

export type {
  UpdateFinanceProductMutationVariables,
  UpdateFinanceProductMutation,
  UpdateFinanceProductMutationFn,
  UpdateFinanceProductMutationHookResult,
  UpdateFinanceProductMutationResult,
  UpdateFinanceProductMutationOptions,
} from "./updateFinanceProduct";

export {
  UpdateFinanceProductDocument,
  useUpdateFinanceProductMutation,
} from "./updateFinanceProduct";

export type {
  UpdateEventApplicationVehicleAssignmentsMutationVariables,
  UpdateEventApplicationVehicleAssignmentsMutation,
  UpdateEventApplicationVehicleAssignmentsMutationFn,
  UpdateEventApplicationVehicleAssignmentsMutationHookResult,
  UpdateEventApplicationVehicleAssignmentsMutationResult,
  UpdateEventApplicationVehicleAssignmentsMutationOptions,
} from "./updateEventApplicationVehicleAssignments";

export {
  UpdateEventApplicationVehicleAssignmentsDocument,
  useUpdateEventApplicationVehicleAssignmentsMutation,
} from "./updateEventApplicationVehicleAssignments";

export type {
  UpdateEventApplicationModuleMainDetailsMutationVariables,
  UpdateEventApplicationModuleMainDetailsMutation,
  UpdateEventApplicationModuleMainDetailsMutationFn,
  UpdateEventApplicationModuleMainDetailsMutationHookResult,
  UpdateEventApplicationModuleMainDetailsMutationResult,
  UpdateEventApplicationModuleMainDetailsMutationOptions,
} from "./updateEventApplicationModuleMainDetails";

export {
  UpdateEventApplicationModuleMainDetailsDocument,
  useUpdateEventApplicationModuleMainDetailsMutation,
} from "./updateEventApplicationModuleMainDetails";

export type {
  UpdateEventApplicationModuleEmailContentsMutationVariables,
  UpdateEventApplicationModuleEmailContentsMutation,
  UpdateEventApplicationModuleEmailContentsMutationFn,
  UpdateEventApplicationModuleEmailContentsMutationHookResult,
  UpdateEventApplicationModuleEmailContentsMutationResult,
  UpdateEventApplicationModuleEmailContentsMutationOptions,
} from "./updateEventApplicationModuleEmailContents";

export {
  UpdateEventApplicationModuleEmailContentsDocument,
  useUpdateEventApplicationModuleEmailContentsMutation,
} from "./updateEventApplicationModuleEmailContents";

export type {
  UpdateEventApplicationFinanceProductAssignmentsMutationVariables,
  UpdateEventApplicationFinanceProductAssignmentsMutation,
  UpdateEventApplicationFinanceProductAssignmentsMutationFn,
  UpdateEventApplicationFinanceProductAssignmentsMutationHookResult,
  UpdateEventApplicationFinanceProductAssignmentsMutationResult,
  UpdateEventApplicationFinanceProductAssignmentsMutationOptions,
} from "./updateEventApplicationFinanceProductAssignments";

export {
  UpdateEventApplicationFinanceProductAssignmentsDocument,
  useUpdateEventApplicationFinanceProductAssignmentsMutation,
} from "./updateEventApplicationFinanceProductAssignments";

export type {
  UpdateEventApplicationEntrypointMutationVariables,
  UpdateEventApplicationEntrypointMutation,
  UpdateEventApplicationEntrypointMutationFn,
  UpdateEventApplicationEntrypointMutationHookResult,
  UpdateEventApplicationEntrypointMutationResult,
  UpdateEventApplicationEntrypointMutationOptions,
} from "./updateEventApplicationEntrypoint";

export {
  UpdateEventApplicationEntrypointDocument,
  useUpdateEventApplicationEntrypointMutation,
} from "./updateEventApplicationEntrypoint";

export type {
  UpdateEventApplicationMutationVariables,
  UpdateEventApplicationMutation,
  UpdateEventApplicationMutationFn,
  UpdateEventApplicationMutationHookResult,
  UpdateEventApplicationMutationResult,
  UpdateEventApplicationMutationOptions,
} from "./updateEventApplication";

export {
  UpdateEventApplicationDocument,
  useUpdateEventApplicationMutation,
} from "./updateEventApplication";

export type {
  UpdateEventMutationVariables,
  UpdateEventMutation,
  UpdateEventMutationFn,
  UpdateEventMutationHookResult,
  UpdateEventMutationResult,
  UpdateEventMutationOptions,
} from "./updateEvent";

export { UpdateEventDocument, useUpdateEventMutation } from "./updateEvent";

export type {
  UpdateEnbdBankIntegrationSettingMutationVariables,
  UpdateEnbdBankIntegrationSettingMutation,
  UpdateEnbdBankIntegrationSettingMutationFn,
  UpdateEnbdBankIntegrationSettingMutationHookResult,
  UpdateEnbdBankIntegrationSettingMutationResult,
  UpdateEnbdBankIntegrationSettingMutationOptions,
} from "./updateEnbdBankIntegrationSetting";

export {
  UpdateEnbdBankIntegrationSettingDocument,
  useUpdateEnbdBankIntegrationSettingMutation,
} from "./updateEnbdBankIntegrationSetting";

export type {
  UpdateEmailInsurerIntegrationSettingMutationVariables,
  UpdateEmailInsurerIntegrationSettingMutation,
  UpdateEmailInsurerIntegrationSettingMutationFn,
  UpdateEmailInsurerIntegrationSettingMutationHookResult,
  UpdateEmailInsurerIntegrationSettingMutationResult,
  UpdateEmailInsurerIntegrationSettingMutationOptions,
} from "./updateEmailInsurerIntegrationSetting";

export {
  UpdateEmailInsurerIntegrationSettingDocument,
  useUpdateEmailInsurerIntegrationSettingMutation,
} from "./updateEmailInsurerIntegrationSetting";

export type {
  UpdateEmailFromTokenMutationVariables,
  UpdateEmailFromTokenMutation,
  UpdateEmailFromTokenMutationFn,
  UpdateEmailFromTokenMutationHookResult,
  UpdateEmailFromTokenMutationResult,
  UpdateEmailFromTokenMutationOptions,
} from "./updateEmailFromToken";

export {
  UpdateEmailFromTokenDocument,
  useUpdateEmailFromTokenMutation,
} from "./updateEmailFromToken";

export type {
  UpdateEmailBankIntegrationSettingMutationVariables,
  UpdateEmailBankIntegrationSettingMutation,
  UpdateEmailBankIntegrationSettingMutationFn,
  UpdateEmailBankIntegrationSettingMutationHookResult,
  UpdateEmailBankIntegrationSettingMutationResult,
  UpdateEmailBankIntegrationSettingMutationOptions,
} from "./updateEmailBankIntegrationSetting";

export {
  UpdateEmailBankIntegrationSettingDocument,
  useUpdateEmailBankIntegrationSettingMutation,
} from "./updateEmailBankIntegrationSetting";

export type {
  UpdateEdmEmailSocialMediaMutationVariables,
  UpdateEdmEmailSocialMediaMutation,
  UpdateEdmEmailSocialMediaMutationFn,
  UpdateEdmEmailSocialMediaMutationHookResult,
  UpdateEdmEmailSocialMediaMutationResult,
  UpdateEdmEmailSocialMediaMutationOptions,
} from "./updateEdmEmailSocialMedia";

export {
  UpdateEdmEmailSocialMediaDocument,
  useUpdateEdmEmailSocialMediaMutation,
} from "./updateEdmEmailSocialMedia";

export type {
  UpdateEazyInsurerIntegrationSettingMutationVariables,
  UpdateEazyInsurerIntegrationSettingMutation,
  UpdateEazyInsurerIntegrationSettingMutationFn,
  UpdateEazyInsurerIntegrationSettingMutationHookResult,
  UpdateEazyInsurerIntegrationSettingMutationResult,
  UpdateEazyInsurerIntegrationSettingMutationOptions,
} from "./updateEazyInsurerIntegrationSetting";

export {
  UpdateEazyInsurerIntegrationSettingDocument,
  useUpdateEazyInsurerIntegrationSettingMutation,
} from "./updateEazyInsurerIntegrationSetting";

export type {
  UpdateDummyWelcomePageEndpointMutationVariables,
  UpdateDummyWelcomePageEndpointMutation,
  UpdateDummyWelcomePageEndpointMutationFn,
  UpdateDummyWelcomePageEndpointMutationHookResult,
  UpdateDummyWelcomePageEndpointMutationResult,
  UpdateDummyWelcomePageEndpointMutationOptions,
} from "./updateDummyWelcomePageEndpoint";

export {
  UpdateDummyWelcomePageEndpointDocument,
  useUpdateDummyWelcomePageEndpointMutation,
} from "./updateDummyWelcomePageEndpoint";

export type {
  UpdateDummyPrivatePageEndpointMutationVariables,
  UpdateDummyPrivatePageEndpointMutation,
  UpdateDummyPrivatePageEndpointMutationFn,
  UpdateDummyPrivatePageEndpointMutationHookResult,
  UpdateDummyPrivatePageEndpointMutationResult,
  UpdateDummyPrivatePageEndpointMutationOptions,
} from "./updateDummyPrivatePageEndpoint";

export {
  UpdateDummyPrivatePageEndpointDocument,
  useUpdateDummyPrivatePageEndpointMutation,
} from "./updateDummyPrivatePageEndpoint";

export type {
  UpdateDocusignSettingMutationVariables,
  UpdateDocusignSettingMutation,
  UpdateDocusignSettingMutationFn,
  UpdateDocusignSettingMutationHookResult,
  UpdateDocusignSettingMutationResult,
  UpdateDocusignSettingMutationOptions,
} from "./updateDocusignSetting";

export {
  UpdateDocusignSettingDocument,
  useUpdateDocusignSettingMutation,
} from "./updateDocusignSetting";

export type {
  UpdateDocusignModuleMutationVariables,
  UpdateDocusignModuleMutation,
  UpdateDocusignModuleMutationFn,
  UpdateDocusignModuleMutationHookResult,
  UpdateDocusignModuleMutationResult,
  UpdateDocusignModuleMutationOptions,
} from "./updateDocusignModule";

export {
  UpdateDocusignModuleDocument,
  useUpdateDocusignModuleMutation,
} from "./updateDocusignModule";

export type {
  UpdateDepositSalesOfferMutationVariables,
  UpdateDepositSalesOfferMutation,
  UpdateDepositSalesOfferMutationFn,
  UpdateDepositSalesOfferMutationHookResult,
  UpdateDepositSalesOfferMutationResult,
  UpdateDepositSalesOfferMutationOptions,
} from "./updateDepositSalesOffer";

export {
  UpdateDepositSalesOfferDocument,
  useUpdateDepositSalesOfferMutation,
} from "./updateDepositSalesOffer";

export type {
  UpdateDealerSocialMediaMutationVariables,
  UpdateDealerSocialMediaMutation,
  UpdateDealerSocialMediaMutationFn,
  UpdateDealerSocialMediaMutationHookResult,
  UpdateDealerSocialMediaMutationResult,
  UpdateDealerSocialMediaMutationOptions,
} from "./updateDealerSocialMedia";

export {
  UpdateDealerSocialMediaDocument,
  useUpdateDealerSocialMediaMutation,
} from "./updateDealerSocialMedia";

export type {
  UpdateDealerMutationVariables,
  UpdateDealerMutation,
  UpdateDealerMutationFn,
  UpdateDealerMutationHookResult,
  UpdateDealerMutationResult,
  UpdateDealerMutationOptions,
} from "./updateDealer";

export { UpdateDealerDocument, useUpdateDealerMutation } from "./updateDealer";

export type {
  UpdateDbsBankIntegrationSettingMutationVariables,
  UpdateDbsBankIntegrationSettingMutation,
  UpdateDbsBankIntegrationSettingMutationFn,
  UpdateDbsBankIntegrationSettingMutationHookResult,
  UpdateDbsBankIntegrationSettingMutationResult,
  UpdateDbsBankIntegrationSettingMutationOptions,
} from "./updateDbsBankIntegrationSetting";

export {
  UpdateDbsBankIntegrationSettingDocument,
  useUpdateDbsBankIntegrationSettingMutation,
} from "./updateDbsBankIntegrationSetting";

export type {
  UpdateCustomerListEndpointMutationVariables,
  UpdateCustomerListEndpointMutation,
  UpdateCustomerListEndpointMutationFn,
  UpdateCustomerListEndpointMutationHookResult,
  UpdateCustomerListEndpointMutationResult,
  UpdateCustomerListEndpointMutationOptions,
} from "./updateCustomerListEndpoint";

export {
  UpdateCustomerListEndpointDocument,
  useUpdateCustomerListEndpointMutation,
} from "./updateCustomerListEndpoint";

export type {
  UpdateCustomerMutationVariables,
  UpdateCustomerMutation,
  UpdateCustomerMutationFn,
  UpdateCustomerMutationHookResult,
  UpdateCustomerMutationResult,
  UpdateCustomerMutationOptions,
} from "./updateCustomer";

export {
  UpdateCustomerDocument,
  useUpdateCustomerMutation,
} from "./updateCustomer";

export type {
  UpdateCtsModuleSettingMutationVariables,
  UpdateCtsModuleSettingMutation,
  UpdateCtsModuleSettingMutationFn,
  UpdateCtsModuleSettingMutationHookResult,
  UpdateCtsModuleSettingMutationResult,
  UpdateCtsModuleSettingMutationOptions,
} from "./updateCtsModuleSetting";

export {
  UpdateCtsModuleSettingDocument,
  useUpdateCtsModuleSettingMutation,
} from "./updateCtsModuleSetting";

export type {
  UpdateCtsModuleMutationVariables,
  UpdateCtsModuleMutation,
  UpdateCtsModuleMutationFn,
  UpdateCtsModuleMutationHookResult,
  UpdateCtsModuleMutationResult,
  UpdateCtsModuleMutationOptions,
} from "./updateCtsModule";

export {
  UpdateCtsModuleDocument,
  useUpdateCtsModuleMutation,
} from "./updateCtsModule";

export type {
  UpdateCtsInsuranceProductsMutationVariables,
  UpdateCtsInsuranceProductsMutation,
  UpdateCtsInsuranceProductsMutationFn,
  UpdateCtsInsuranceProductsMutationHookResult,
  UpdateCtsInsuranceProductsMutationResult,
  UpdateCtsInsuranceProductsMutationOptions,
} from "./updateCtsInsuranceProducts";

export {
  UpdateCtsInsuranceProductsDocument,
  useUpdateCtsInsuranceProductsMutation,
} from "./updateCtsInsuranceProducts";

export type {
  UpdateCtsFinanceProductsMutationVariables,
  UpdateCtsFinanceProductsMutation,
  UpdateCtsFinanceProductsMutationFn,
  UpdateCtsFinanceProductsMutationHookResult,
  UpdateCtsFinanceProductsMutationResult,
  UpdateCtsFinanceProductsMutationOptions,
} from "./updateCtsFinanceProducts";

export {
  UpdateCtsFinanceProductsDocument,
  useUpdateCtsFinanceProductsMutation,
} from "./updateCtsFinanceProducts";

export type {
  UpdateConsentsAndDeclarationsModuleMutationVariables,
  UpdateConsentsAndDeclarationsModuleMutation,
  UpdateConsentsAndDeclarationsModuleMutationFn,
  UpdateConsentsAndDeclarationsModuleMutationHookResult,
  UpdateConsentsAndDeclarationsModuleMutationResult,
  UpdateConsentsAndDeclarationsModuleMutationOptions,
} from "./updateConsentsAndDeclarationsModule";

export {
  UpdateConsentsAndDeclarationsModuleDocument,
  useUpdateConsentsAndDeclarationsModuleMutation,
} from "./updateConsentsAndDeclarationsModule";

export type {
  UpdateConsentOrderListMutationVariables,
  UpdateConsentOrderListMutation,
  UpdateConsentOrderListMutationFn,
  UpdateConsentOrderListMutationHookResult,
  UpdateConsentOrderListMutationResult,
  UpdateConsentOrderListMutationOptions,
} from "./updateConsentOrderList";

export {
  UpdateConsentOrderListDocument,
  useUpdateConsentOrderListMutation,
} from "./updateConsentOrderList";

export type {
  UpdateConfiguratorModuleEmailContentsMutationVariables,
  UpdateConfiguratorModuleEmailContentsMutation,
  UpdateConfiguratorModuleEmailContentsMutationFn,
  UpdateConfiguratorModuleEmailContentsMutationHookResult,
  UpdateConfiguratorModuleEmailContentsMutationResult,
  UpdateConfiguratorModuleEmailContentsMutationOptions,
} from "./updateConfiguratorModuleEmailContents";

export {
  UpdateConfiguratorModuleEmailContentsDocument,
  useUpdateConfiguratorModuleEmailContentsMutation,
} from "./updateConfiguratorModuleEmailContents";

export type {
  UpdateConfiguratorModuleMutationVariables,
  UpdateConfiguratorModuleMutation,
  UpdateConfiguratorModuleMutationFn,
  UpdateConfiguratorModuleMutationHookResult,
  UpdateConfiguratorModuleMutationResult,
  UpdateConfiguratorModuleMutationOptions,
} from "./updateConfiguratorModule";

export {
  UpdateConfiguratorModuleDocument,
  useUpdateConfiguratorModuleMutation,
} from "./updateConfiguratorModule";

export type {
  UpdateConfiguratorApplicationJourneyMutationVariables,
  UpdateConfiguratorApplicationJourneyMutation,
  UpdateConfiguratorApplicationJourneyMutationFn,
  UpdateConfiguratorApplicationJourneyMutationHookResult,
  UpdateConfiguratorApplicationJourneyMutationResult,
  UpdateConfiguratorApplicationJourneyMutationOptions,
} from "./updateConfiguratorApplicationJourney";

export {
  UpdateConfiguratorApplicationJourneyDocument,
  useUpdateConfiguratorApplicationJourneyMutation,
} from "./updateConfiguratorApplicationJourney";

export type {
  UpdateConfiguratorApplicationEntrypointMutationVariables,
  UpdateConfiguratorApplicationEntrypointMutation,
  UpdateConfiguratorApplicationEntrypointMutationFn,
  UpdateConfiguratorApplicationEntrypointMutationHookResult,
  UpdateConfiguratorApplicationEntrypointMutationResult,
  UpdateConfiguratorApplicationEntrypointMutationOptions,
} from "./updateConfiguratorApplicationEntrypoint";

export {
  UpdateConfiguratorApplicationEntrypointDocument,
  useUpdateConfiguratorApplicationEntrypointMutation,
} from "./updateConfiguratorApplicationEntrypoint";

export type {
  UpdateConfiguratorApplicationConfigurationMutationVariables,
  UpdateConfiguratorApplicationConfigurationMutation,
  UpdateConfiguratorApplicationConfigurationMutationFn,
  UpdateConfiguratorApplicationConfigurationMutationHookResult,
  UpdateConfiguratorApplicationConfigurationMutationResult,
  UpdateConfiguratorApplicationConfigurationMutationOptions,
} from "./updateConfiguratorApplicationConfiguration";

export {
  UpdateConfiguratorApplicationConfigurationDocument,
  useUpdateConfiguratorApplicationConfigurationMutation,
} from "./updateConfiguratorApplicationConfiguration";

export type {
  UpdateConfiguratorApplicationMutationVariables,
  UpdateConfiguratorApplicationMutation,
  UpdateConfiguratorApplicationMutationFn,
  UpdateConfiguratorApplicationMutationHookResult,
  UpdateConfiguratorApplicationMutationResult,
  UpdateConfiguratorApplicationMutationOptions,
} from "./updateConfiguratorApplication";

export {
  UpdateConfiguratorApplicationDocument,
  useUpdateConfiguratorApplicationMutation,
} from "./updateConfiguratorApplication";

export type {
  UpdateCompanyMutationVariables,
  UpdateCompanyMutation,
  UpdateCompanyMutationFn,
  UpdateCompanyMutationHookResult,
  UpdateCompanyMutationResult,
  UpdateCompanyMutationOptions,
} from "./updateCompany";

export {
  UpdateCompanyDocument,
  useUpdateCompanyMutation,
} from "./updateCompany";

export type {
  UpdateColorBlockMutationVariables,
  UpdateColorBlockMutation,
  UpdateColorBlockMutationFn,
  UpdateColorBlockMutationHookResult,
  UpdateColorBlockMutationResult,
  UpdateColorBlockMutationOptions,
} from "./updateColorBlock";

export {
  UpdateColorBlockDocument,
  useUpdateColorBlockMutation,
} from "./updateColorBlock";

export type {
  UpdateCheckboxConsentsAndDeclarationsMutationVariables,
  UpdateCheckboxConsentsAndDeclarationsMutation,
  UpdateCheckboxConsentsAndDeclarationsMutationFn,
  UpdateCheckboxConsentsAndDeclarationsMutationHookResult,
  UpdateCheckboxConsentsAndDeclarationsMutationResult,
  UpdateCheckboxConsentsAndDeclarationsMutationOptions,
} from "./updateCheckboxConsentsAndDeclarations";

export {
  UpdateCheckboxConsentsAndDeclarationsDocument,
  useUpdateCheckboxConsentsAndDeclarationsMutation,
} from "./updateCheckboxConsentsAndDeclarations";

export type {
  UpdateCapModuleMutationVariables,
  UpdateCapModuleMutation,
  UpdateCapModuleMutationFn,
  UpdateCapModuleMutationHookResult,
  UpdateCapModuleMutationResult,
  UpdateCapModuleMutationOptions,
} from "./updateCapModule";

export {
  UpdateCapModuleDocument,
  useUpdateCapModuleMutation,
} from "./updateCapModule";

export type {
  UpdateBasicSigningModuleMutationVariables,
  UpdateBasicSigningModuleMutation,
  UpdateBasicSigningModuleMutationFn,
  UpdateBasicSigningModuleMutationHookResult,
  UpdateBasicSigningModuleMutationResult,
  UpdateBasicSigningModuleMutationOptions,
} from "./updateBasicSigningModule";

export {
  UpdateBasicSigningModuleDocument,
  useUpdateBasicSigningModuleMutation,
} from "./updateBasicSigningModule";

export type {
  UpdateBannerMutationVariables,
  UpdateBannerMutation,
  UpdateBannerMutationFn,
  UpdateBannerMutationHookResult,
  UpdateBannerMutationResult,
  UpdateBannerMutationOptions,
} from "./updateBanner";

export { UpdateBannerDocument, useUpdateBannerMutation } from "./updateBanner";

export type {
  UpdateBankModuleMutationVariables,
  UpdateBankModuleMutation,
  UpdateBankModuleMutationFn,
  UpdateBankModuleMutationHookResult,
  UpdateBankModuleMutationResult,
  UpdateBankModuleMutationOptions,
} from "./updateBankModule";

export {
  UpdateBankModuleDocument,
  useUpdateBankModuleMutation,
} from "./updateBankModule";

export type {
  UpdateBankAvailableFinanceProductTypesMutationVariables,
  UpdateBankAvailableFinanceProductTypesMutation,
  UpdateBankAvailableFinanceProductTypesMutationFn,
  UpdateBankAvailableFinanceProductTypesMutationHookResult,
  UpdateBankAvailableFinanceProductTypesMutationResult,
  UpdateBankAvailableFinanceProductTypesMutationOptions,
} from "./updateBankAvailableFinanceProductTypes";

export {
  UpdateBankAvailableFinanceProductTypesDocument,
  useUpdateBankAvailableFinanceProductTypesMutation,
} from "./updateBankAvailableFinanceProductTypes";

export type {
  UpdateBankMutationVariables,
  UpdateBankMutation,
  UpdateBankMutationFn,
  UpdateBankMutationHookResult,
  UpdateBankMutationResult,
  UpdateBankMutationOptions,
} from "./updateBank";

export { UpdateBankDocument, useUpdateBankMutation } from "./updateBank";

export type {
  UpdateAutoplayModuleSettingMutationVariables,
  UpdateAutoplayModuleSettingMutation,
  UpdateAutoplayModuleSettingMutationFn,
  UpdateAutoplayModuleSettingMutationHookResult,
  UpdateAutoplayModuleSettingMutationResult,
  UpdateAutoplayModuleSettingMutationOptions,
} from "./updateAutoplayModuleSetting";

export {
  UpdateAutoplayModuleSettingDocument,
  useUpdateAutoplayModuleSettingMutation,
} from "./updateAutoplayModuleSetting";

export type {
  UpdateAutoplayModuleMutationVariables,
  UpdateAutoplayModuleMutation,
  UpdateAutoplayModuleMutationFn,
  UpdateAutoplayModuleMutationHookResult,
  UpdateAutoplayModuleMutationResult,
  UpdateAutoplayModuleMutationOptions,
} from "./updateAutoplayModule";

export {
  UpdateAutoplayModuleDocument,
  useUpdateAutoplayModuleMutation,
} from "./updateAutoplayModule";

export type {
  UpdateAssigneeOnLeadMutationVariables,
  UpdateAssigneeOnLeadMutation,
  UpdateAssigneeOnLeadMutationFn,
  UpdateAssigneeOnLeadMutationHookResult,
  UpdateAssigneeOnLeadMutationResult,
  UpdateAssigneeOnLeadMutationOptions,
} from "./updateAssigneeOnLead";

export {
  UpdateAssigneeOnLeadDocument,
  useUpdateAssigneeOnLeadMutation,
} from "./updateAssigneeOnLead";

export type {
  UpdateAssigneeOnApplicationMutationVariables,
  UpdateAssigneeOnApplicationMutation,
  UpdateAssigneeOnApplicationMutationFn,
  UpdateAssigneeOnApplicationMutationHookResult,
  UpdateAssigneeOnApplicationMutationResult,
  UpdateAssigneeOnApplicationMutationOptions,
} from "./updateAssigneeOnApplication";

export {
  UpdateAssigneeOnApplicationDocument,
  useUpdateAssigneeOnApplicationMutation,
} from "./updateAssigneeOnApplication";

export type {
  UpdateAppointmentModuleEmailContentMutationVariables,
  UpdateAppointmentModuleEmailContentMutation,
  UpdateAppointmentModuleEmailContentMutationFn,
  UpdateAppointmentModuleEmailContentMutationHookResult,
  UpdateAppointmentModuleEmailContentMutationResult,
  UpdateAppointmentModuleEmailContentMutationOptions,
} from "./updateAppointmentModuleEmailContent";

export {
  UpdateAppointmentModuleEmailContentDocument,
  useUpdateAppointmentModuleEmailContentMutation,
} from "./updateAppointmentModuleEmailContent";

export type {
  UpdateAppointmentModuleMutationVariables,
  UpdateAppointmentModuleMutation,
  UpdateAppointmentModuleMutationFn,
  UpdateAppointmentModuleMutationHookResult,
  UpdateAppointmentModuleMutationResult,
  UpdateAppointmentModuleMutationOptions,
} from "./updateAppointmentModule";

export {
  UpdateAppointmentModuleDocument,
  useUpdateAppointmentModuleMutation,
} from "./updateAppointmentModule";

export type {
  UpdateAppointmentDataMutationVariables,
  UpdateAppointmentDataMutation,
  UpdateAppointmentDataMutationFn,
  UpdateAppointmentDataMutationHookResult,
  UpdateAppointmentDataMutationResult,
  UpdateAppointmentDataMutationOptions,
} from "./updateAppointmentData";

export {
  UpdateAppointmentDataDocument,
  useUpdateAppointmentDataMutation,
} from "./updateAppointmentData";

export type {
  UpdateApplicationListEndpointMutationVariables,
  UpdateApplicationListEndpointMutation,
  UpdateApplicationListEndpointMutationFn,
  UpdateApplicationListEndpointMutationHookResult,
  UpdateApplicationListEndpointMutationResult,
  UpdateApplicationListEndpointMutationOptions,
} from "./updateApplicationListEndpoint";

export {
  UpdateApplicationListEndpointDocument,
  useUpdateApplicationListEndpointMutation,
} from "./updateApplicationListEndpoint";

export type {
  UpdateApplicationFieldsMutationVariables,
  UpdateApplicationFieldsMutation,
  UpdateApplicationFieldsMutationFn,
  UpdateApplicationFieldsMutationHookResult,
  UpdateApplicationFieldsMutationResult,
  UpdateApplicationFieldsMutationOptions,
} from "./updateApplicationFields";

export {
  UpdateApplicationFieldsDocument,
  useUpdateApplicationFieldsMutation,
} from "./updateApplicationFields";

export type {
  UpdateApplicationDealershipAssignmentsByDealerMutationVariables,
  UpdateApplicationDealershipAssignmentsByDealerMutation,
  UpdateApplicationDealershipAssignmentsByDealerMutationFn,
  UpdateApplicationDealershipAssignmentsByDealerMutationHookResult,
  UpdateApplicationDealershipAssignmentsByDealerMutationResult,
  UpdateApplicationDealershipAssignmentsByDealerMutationOptions,
} from "./updateApplicationDealershipAssignmentsByDealer";

export {
  UpdateApplicationDealershipAssignmentsByDealerDocument,
  useUpdateApplicationDealershipAssignmentsByDealerMutation,
} from "./updateApplicationDealershipAssignmentsByDealer";

export type {
  UpdateApplicationMutationVariables,
  UpdateApplicationMutation,
  UpdateApplicationMutationFn,
  UpdateApplicationMutationHookResult,
  UpdateApplicationMutationResult,
  UpdateApplicationMutationOptions,
} from "./updateApplication";

export {
  UpdateApplicationDocument,
  useUpdateApplicationMutation,
} from "./updateApplication";

export type {
  UpdateAdyenPaymentSettingsMutationVariables,
  UpdateAdyenPaymentSettingsMutation,
  UpdateAdyenPaymentSettingsMutationFn,
  UpdateAdyenPaymentSettingsMutationHookResult,
  UpdateAdyenPaymentSettingsMutationResult,
  UpdateAdyenPaymentSettingsMutationOptions,
} from "./updateAdyenPaymentSettings";

export {
  UpdateAdyenPaymentSettingsDocument,
  useUpdateAdyenPaymentSettingsMutation,
} from "./updateAdyenPaymentSettings";

export type {
  UpdateAdyenPaymentModuleMutationVariables,
  UpdateAdyenPaymentModuleMutation,
  UpdateAdyenPaymentModuleMutationFn,
  UpdateAdyenPaymentModuleMutationHookResult,
  UpdateAdyenPaymentModuleMutationResult,
  UpdateAdyenPaymentModuleMutationOptions,
} from "./updateAdyenPaymentModule";

export {
  UpdateAdyenPaymentModuleDocument,
  useUpdateAdyenPaymentModuleMutation,
} from "./updateAdyenPaymentModule";

export type {
  UpdateAdditionalDetailMutationVariables,
  UpdateAdditionalDetailMutation,
  UpdateAdditionalDetailMutationFn,
  UpdateAdditionalDetailMutationHookResult,
  UpdateAdditionalDetailMutationResult,
  UpdateAdditionalDetailMutationOptions,
} from "./updateAdditionalDetail";

export {
  UpdateAdditionalDetailDocument,
  useUpdateAdditionalDetailMutation,
} from "./updateAdditionalDetail";

export type {
  UpdateAccountMutationVariables,
  UpdateAccountMutation,
  UpdateAccountMutationFn,
  UpdateAccountMutationHookResult,
  UpdateAccountMutationResult,
  UpdateAccountMutationOptions,
} from "./updateAccount";

export {
  UpdateAccountDocument,
  useUpdateAccountMutation,
} from "./updateAccount";

export type {
  UnqualifyLeadMutationVariables,
  UnqualifyLeadMutation,
  UnqualifyLeadMutationFn,
  UnqualifyLeadMutationHookResult,
  UnqualifyLeadMutationResult,
  UnqualifyLeadMutationOptions,
} from "./unqualifyLead";

export {
  UnqualifyLeadDocument,
  useUnqualifyLeadMutation,
} from "./unqualifyLead";

export type {
  TakeOutStockInventoryMutationVariables,
  TakeOutStockInventoryMutation,
  TakeOutStockInventoryMutationFn,
  TakeOutStockInventoryMutationHookResult,
  TakeOutStockInventoryMutationResult,
  TakeOutStockInventoryMutationOptions,
} from "./takeOutStockInventory";

export {
  TakeOutStockInventoryDocument,
  useTakeOutStockInventoryMutation,
} from "./takeOutStockInventory";

export type {
  SynchronizePorscheMasterDataMutationVariables,
  SynchronizePorscheMasterDataMutation,
  SynchronizePorscheMasterDataMutationFn,
  SynchronizePorscheMasterDataMutationHookResult,
  SynchronizePorscheMasterDataMutationResult,
  SynchronizePorscheMasterDataMutationOptions,
} from "./synchronizePorscheMasterData";

export {
  SynchronizePorscheMasterDataDocument,
  useSynchronizePorscheMasterDataMutation,
} from "./synchronizePorscheMasterData";

export type {
  SynchronizeFinderVehicleMutationVariables,
  SynchronizeFinderVehicleMutation,
  SynchronizeFinderVehicleMutationFn,
  SynchronizeFinderVehicleMutationHookResult,
  SynchronizeFinderVehicleMutationResult,
  SynchronizeFinderVehicleMutationOptions,
} from "./synchronizeFinderVehicle";

export {
  SynchronizeFinderVehicleDocument,
  useSynchronizeFinderVehicleMutation,
} from "./synchronizeFinderVehicle";

export type {
  SubmitTtbPaymentMutationVariables,
  SubmitTtbPaymentMutation,
  SubmitTtbPaymentMutationFn,
  SubmitTtbPaymentMutationHookResult,
  SubmitTtbPaymentMutationResult,
  SubmitTtbPaymentMutationOptions,
} from "./submitTtbPayment";

export {
  SubmitTtbPaymentDocument,
  useSubmitTtbPaymentMutation,
} from "./submitTtbPayment";

export type {
  SubmitTestDriveKycMutationVariables,
  SubmitTestDriveKycMutation,
  SubmitTestDriveKycMutationFn,
  SubmitTestDriveKycMutationHookResult,
  SubmitTestDriveKycMutationResult,
  SubmitTestDriveKycMutationOptions,
} from "./submitTestDriveKYC";

export {
  SubmitTestDriveKycDocument,
  useSubmitTestDriveKycMutation,
} from "./submitTestDriveKYC";

export type {
  SubmitTestDriveAgreementsMutationVariables,
  SubmitTestDriveAgreementsMutation,
  SubmitTestDriveAgreementsMutationFn,
  SubmitTestDriveAgreementsMutationHookResult,
  SubmitTestDriveAgreementsMutationResult,
  SubmitTestDriveAgreementsMutationOptions,
} from "./submitTestDriveAgreements";

export {
  SubmitTestDriveAgreementsDocument,
  useSubmitTestDriveAgreementsMutation,
} from "./submitTestDriveAgreements";

export type {
  SubmitSigningOtpMutationVariables,
  SubmitSigningOtpMutation,
  SubmitSigningOtpMutationFn,
  SubmitSigningOtpMutationHookResult,
  SubmitSigningOtpMutationResult,
  SubmitSigningOtpMutationOptions,
} from "./submitSigningOTP";

export {
  SubmitSigningOtpDocument,
  useSubmitSigningOtpMutation,
} from "./submitSigningOTP";

export type {
  SubmitSalesOfferPorschePaymentMutationVariables,
  SubmitSalesOfferPorschePaymentMutation,
  SubmitSalesOfferPorschePaymentMutationFn,
  SubmitSalesOfferPorschePaymentMutationHookResult,
  SubmitSalesOfferPorschePaymentMutationResult,
  SubmitSalesOfferPorschePaymentMutationOptions,
} from "./submitSalesOfferPorschePayment";

export {
  SubmitSalesOfferPorschePaymentDocument,
  useSubmitSalesOfferPorschePaymentMutation,
} from "./submitSalesOfferPorschePayment";

export type {
  SubmitPorschePaymentMutationVariables,
  SubmitPorschePaymentMutation,
  SubmitPorschePaymentMutationFn,
  SubmitPorschePaymentMutationHookResult,
  SubmitPorschePaymentMutationResult,
  SubmitPorschePaymentMutationOptions,
} from "./submitPorschePayment";

export {
  SubmitPorschePaymentDocument,
  useSubmitPorschePaymentMutation,
} from "./submitPorschePayment";

export type {
  SubmitPayGatePaymentMutationVariables,
  SubmitPayGatePaymentMutation,
  SubmitPayGatePaymentMutationFn,
  SubmitPayGatePaymentMutationHookResult,
  SubmitPayGatePaymentMutationResult,
  SubmitPayGatePaymentMutationOptions,
} from "./submitPayGatePayment";

export {
  SubmitPayGatePaymentDocument,
  useSubmitPayGatePaymentMutation,
} from "./submitPayGatePayment";

export type {
  SubmitIntentAndAssignMutationVariables,
  SubmitIntentAndAssignMutation,
  SubmitIntentAndAssignMutationFn,
  SubmitIntentAndAssignMutationHookResult,
  SubmitIntentAndAssignMutationResult,
  SubmitIntentAndAssignMutationOptions,
} from "./submitIntentAndAssign";

export {
  SubmitIntentAndAssignDocument,
  useSubmitIntentAndAssignMutation,
} from "./submitIntentAndAssign";

export type {
  SubmitGuarantorKycMutationVariables,
  SubmitGuarantorKycMutation,
  SubmitGuarantorKycMutationFn,
  SubmitGuarantorKycMutationHookResult,
  SubmitGuarantorKycMutationResult,
  SubmitGuarantorKycMutationOptions,
} from "./submitGuarantorKYC";

export {
  SubmitGuarantorKycDocument,
  useSubmitGuarantorKycMutation,
} from "./submitGuarantorKYC";

export type {
  SubmitGuarantorAgreementsMutationVariables,
  SubmitGuarantorAgreementsMutation,
  SubmitGuarantorAgreementsMutationFn,
  SubmitGuarantorAgreementsMutationHookResult,
  SubmitGuarantorAgreementsMutationResult,
  SubmitGuarantorAgreementsMutationOptions,
} from "./submitGuarantorAgreements";

export {
  SubmitGuarantorAgreementsDocument,
  useSubmitGuarantorAgreementsMutation,
} from "./submitGuarantorAgreements";

export type {
  SubmitGiftVoucherTtbPaymentMutationVariables,
  SubmitGiftVoucherTtbPaymentMutation,
  SubmitGiftVoucherTtbPaymentMutationFn,
  SubmitGiftVoucherTtbPaymentMutationHookResult,
  SubmitGiftVoucherTtbPaymentMutationResult,
  SubmitGiftVoucherTtbPaymentMutationOptions,
} from "./submitGiftVoucherTtbPayment";

export {
  SubmitGiftVoucherTtbPaymentDocument,
  useSubmitGiftVoucherTtbPaymentMutation,
} from "./submitGiftVoucherTtbPayment";

export type {
  SubmitGiftVoucherPorschePaymentMutationVariables,
  SubmitGiftVoucherPorschePaymentMutation,
  SubmitGiftVoucherPorschePaymentMutationFn,
  SubmitGiftVoucherPorschePaymentMutationHookResult,
  SubmitGiftVoucherPorschePaymentMutationResult,
  SubmitGiftVoucherPorschePaymentMutationOptions,
} from "./submitGiftVoucherPorschePayment";

export {
  SubmitGiftVoucherPorschePaymentDocument,
  useSubmitGiftVoucherPorschePaymentMutation,
} from "./submitGiftVoucherPorschePayment";

export type {
  SubmitGiftVoucherPayGatePaymentMutationVariables,
  SubmitGiftVoucherPayGatePaymentMutation,
  SubmitGiftVoucherPayGatePaymentMutationFn,
  SubmitGiftVoucherPayGatePaymentMutationHookResult,
  SubmitGiftVoucherPayGatePaymentMutationResult,
  SubmitGiftVoucherPayGatePaymentMutationOptions,
} from "./submitGiftVoucherPayGatePayment";

export {
  SubmitGiftVoucherPayGatePaymentDocument,
  useSubmitGiftVoucherPayGatePaymentMutation,
} from "./submitGiftVoucherPayGatePayment";

export type {
  SubmitGiftVoucherFiservPaymentMutationVariables,
  SubmitGiftVoucherFiservPaymentMutation,
  SubmitGiftVoucherFiservPaymentMutationFn,
  SubmitGiftVoucherFiservPaymentMutationHookResult,
  SubmitGiftVoucherFiservPaymentMutationResult,
  SubmitGiftVoucherFiservPaymentMutationOptions,
} from "./submitGiftVoucherFiservPayment";

export {
  SubmitGiftVoucherFiservPaymentDocument,
  useSubmitGiftVoucherFiservPaymentMutation,
} from "./submitGiftVoucherFiservPayment";

export type {
  SubmitGiftVoucherApplicantKycMutationVariables,
  SubmitGiftVoucherApplicantKycMutation,
  SubmitGiftVoucherApplicantKycMutationFn,
  SubmitGiftVoucherApplicantKycMutationHookResult,
  SubmitGiftVoucherApplicantKycMutationResult,
  SubmitGiftVoucherApplicantKycMutationOptions,
} from "./submitGiftVoucherApplicantKYC";

export {
  SubmitGiftVoucherApplicantKycDocument,
  useSubmitGiftVoucherApplicantKycMutation,
} from "./submitGiftVoucherApplicantKYC";

export type {
  SubmitGiftVoucherApplicantAgreementsMutationVariables,
  SubmitGiftVoucherApplicantAgreementsMutation,
  SubmitGiftVoucherApplicantAgreementsMutationFn,
  SubmitGiftVoucherApplicantAgreementsMutationHookResult,
  SubmitGiftVoucherApplicantAgreementsMutationResult,
  SubmitGiftVoucherApplicantAgreementsMutationOptions,
} from "./submitGiftVoucherApplicantAgreements";

export {
  SubmitGiftVoucherApplicantAgreementsDocument,
  useSubmitGiftVoucherApplicantAgreementsMutation,
} from "./submitGiftVoucherApplicantAgreements";

export type {
  SubmitGiftVoucherAdyenPaymentMutationVariables,
  SubmitGiftVoucherAdyenPaymentMutation,
  SubmitGiftVoucherAdyenPaymentMutationFn,
  SubmitGiftVoucherAdyenPaymentMutationHookResult,
  SubmitGiftVoucherAdyenPaymentMutationResult,
  SubmitGiftVoucherAdyenPaymentMutationOptions,
} from "./submitGiftVoucherAdyenPayment";

export {
  SubmitGiftVoucherAdyenPaymentDocument,
  useSubmitGiftVoucherAdyenPaymentMutation,
} from "./submitGiftVoucherAdyenPayment";

export type {
  SubmitFiservPaymentMutationVariables,
  SubmitFiservPaymentMutation,
  SubmitFiservPaymentMutationFn,
  SubmitFiservPaymentMutationHookResult,
  SubmitFiservPaymentMutationResult,
  SubmitFiservPaymentMutationOptions,
} from "./submitFiservPayment";

export {
  SubmitFiservPaymentDocument,
  useSubmitFiservPaymentMutation,
} from "./submitFiservPayment";

export type {
  SubmitChangesMutationVariables,
  SubmitChangesMutation,
  SubmitChangesMutationFn,
  SubmitChangesMutationHookResult,
  SubmitChangesMutationResult,
  SubmitChangesMutationOptions,
} from "./submitChanges";

export {
  SubmitChangesDocument,
  useSubmitChangesMutation,
} from "./submitChanges";

export type {
  SubmitApplicationQuotationMutationVariables,
  SubmitApplicationQuotationMutation,
  SubmitApplicationQuotationMutationFn,
  SubmitApplicationQuotationMutationHookResult,
  SubmitApplicationQuotationMutationResult,
  SubmitApplicationQuotationMutationOptions,
} from "./submitApplicationQuotation";

export {
  SubmitApplicationQuotationDocument,
  useSubmitApplicationQuotationMutation,
} from "./submitApplicationQuotation";

export type {
  SubmitApplicantVisitAppointmentMutationVariables,
  SubmitApplicantVisitAppointmentMutation,
  SubmitApplicantVisitAppointmentMutationFn,
  SubmitApplicantVisitAppointmentMutationHookResult,
  SubmitApplicantVisitAppointmentMutationResult,
  SubmitApplicantVisitAppointmentMutationOptions,
} from "./submitApplicantVisitAppointment";

export {
  SubmitApplicantVisitAppointmentDocument,
  useSubmitApplicantVisitAppointmentMutation,
} from "./submitApplicantVisitAppointment";

export type {
  SubmitApplicantKycMutationVariables,
  SubmitApplicantKycMutation,
  SubmitApplicantKycMutationFn,
  SubmitApplicantKycMutationHookResult,
  SubmitApplicantKycMutationResult,
  SubmitApplicantKycMutationOptions,
} from "./submitApplicantKYC";

export {
  SubmitApplicantKycDocument,
  useSubmitApplicantKycMutation,
} from "./submitApplicantKYC";

export type {
  SubmitApplicantAppointmentMutationVariables,
  SubmitApplicantAppointmentMutation,
  SubmitApplicantAppointmentMutationFn,
  SubmitApplicantAppointmentMutationHookResult,
  SubmitApplicantAppointmentMutationResult,
  SubmitApplicantAppointmentMutationOptions,
} from "./submitApplicantAppointment";

export {
  SubmitApplicantAppointmentDocument,
  useSubmitApplicantAppointmentMutation,
} from "./submitApplicantAppointment";

export type {
  SubmitApplicantAgreementsMutationVariables,
  SubmitApplicantAgreementsMutation,
  SubmitApplicantAgreementsMutationFn,
  SubmitApplicantAgreementsMutationHookResult,
  SubmitApplicantAgreementsMutationResult,
  SubmitApplicantAgreementsMutationOptions,
} from "./submitApplicantAgreements";

export {
  SubmitApplicantAgreementsDocument,
  useSubmitApplicantAgreementsMutation,
} from "./submitApplicantAgreements";

export type {
  SubmitAdyenPaymentMutationVariables,
  SubmitAdyenPaymentMutation,
  SubmitAdyenPaymentMutationFn,
  SubmitAdyenPaymentMutationHookResult,
  SubmitAdyenPaymentMutationResult,
  SubmitAdyenPaymentMutationOptions,
} from "./submitAdyenPayment";

export {
  SubmitAdyenPaymentDocument,
  useSubmitAdyenPaymentMutation,
} from "./submitAdyenPayment";

export type {
  StartTestDriveMutationVariables,
  StartTestDriveMutation,
  StartTestDriveMutationFn,
  StartTestDriveMutationHookResult,
  StartTestDriveMutationResult,
  StartTestDriveMutationOptions,
} from "./startTestDrive";

export {
  StartTestDriveDocument,
  useStartTestDriveMutation,
} from "./startTestDrive";

export type {
  ShareStandardApplicationMutationVariables,
  ShareStandardApplicationMutation,
  ShareStandardApplicationMutationFn,
  ShareStandardApplicationMutationHookResult,
  ShareStandardApplicationMutationResult,
  ShareStandardApplicationMutationOptions,
} from "./shareStandardApplication";

export {
  ShareStandardApplicationDocument,
  useShareStandardApplicationMutation,
} from "./shareStandardApplication";

export type {
  ShareSalesOfferDocumentMutationVariables,
  ShareSalesOfferDocumentMutation,
  ShareSalesOfferDocumentMutationFn,
  ShareSalesOfferDocumentMutationHookResult,
  ShareSalesOfferDocumentMutationResult,
  ShareSalesOfferDocumentMutationOptions,
} from "./shareSalesOfferDocument";

export {
  ShareSalesOfferDocumentDocument,
  useShareSalesOfferDocumentMutation,
} from "./shareSalesOfferDocument";

export type {
  SendSalesOfferMutationVariables,
  SendSalesOfferMutation,
  SendSalesOfferMutationFn,
  SendSalesOfferMutationHookResult,
  SendSalesOfferMutationResult,
  SendSalesOfferMutationOptions,
} from "./sendSalesOffer";

export {
  SendSalesOfferDocument,
  useSendSalesOfferMutation,
} from "./sendSalesOffer";

export type {
  SendMobileVerificationOtpMutationVariables,
  SendMobileVerificationOtpMutation,
  SendMobileVerificationOtpMutationFn,
  SendMobileVerificationOtpMutationHookResult,
  SendMobileVerificationOtpMutationResult,
  SendMobileVerificationOtpMutationOptions,
} from "./sendMobileVerificationOTP";

export {
  SendMobileVerificationOtpDocument,
  useSendMobileVerificationOtpMutation,
} from "./sendMobileVerificationOTP";

export type {
  RevokeWebPublicKeyCredentialMutationVariables,
  RevokeWebPublicKeyCredentialMutation,
  RevokeWebPublicKeyCredentialMutationFn,
  RevokeWebPublicKeyCredentialMutationHookResult,
  RevokeWebPublicKeyCredentialMutationResult,
  RevokeWebPublicKeyCredentialMutationOptions,
} from "./revokeWebPublicKeyCredential";

export {
  RevokeWebPublicKeyCredentialDocument,
  useRevokeWebPublicKeyCredentialMutation,
} from "./revokeWebPublicKeyCredential";

export type {
  RevokeSessionMutationVariables,
  RevokeSessionMutation,
  RevokeSessionMutationFn,
  RevokeSessionMutationHookResult,
  RevokeSessionMutationResult,
  RevokeSessionMutationOptions,
} from "./revokeSession";

export {
  RevokeSessionDocument,
  useRevokeSessionMutation,
} from "./revokeSession";

export type {
  ResubmitLeadToCapMutationVariables,
  ResubmitLeadToCapMutation,
  ResubmitLeadToCapMutationFn,
  ResubmitLeadToCapMutationHookResult,
  ResubmitLeadToCapMutationResult,
  ResubmitLeadToCapMutationOptions,
} from "./resubmitLeadToCap";

export {
  ResubmitLeadToCapDocument,
  useResubmitLeadToCapMutation,
} from "./resubmitLeadToCap";

export type {
  ResetFinderVehicleMutationVariables,
  ResetFinderVehicleMutation,
  ResetFinderVehicleMutationFn,
  ResetFinderVehicleMutationHookResult,
  ResetFinderVehicleMutationResult,
  ResetFinderVehicleMutationOptions,
} from "./resetFinderVehicle";

export {
  ResetFinderVehicleDocument,
  useResetFinderVehicleMutation,
} from "./resetFinderVehicle";

export type {
  ResendSmsOtpForUpdateEmailMutationVariables,
  ResendSmsOtpForUpdateEmailMutation,
  ResendSmsOtpForUpdateEmailMutationFn,
  ResendSmsOtpForUpdateEmailMutationHookResult,
  ResendSmsOtpForUpdateEmailMutationResult,
  ResendSmsOtpForUpdateEmailMutationOptions,
} from "./resendSmsOTPForUpdateEmail";

export {
  ResendSmsOtpForUpdateEmailDocument,
  useResendSmsOtpForUpdateEmailMutation,
} from "./resendSmsOTPForUpdateEmail";

export type {
  ResendSmsOtpForResetPasswordMutationVariables,
  ResendSmsOtpForResetPasswordMutation,
  ResendSmsOtpForResetPasswordMutationFn,
  ResendSmsOtpForResetPasswordMutationHookResult,
  ResendSmsOtpForResetPasswordMutationResult,
  ResendSmsOtpForResetPasswordMutationOptions,
} from "./resendSmsOTPForResetPassword";

export {
  ResendSmsOtpForResetPasswordDocument,
  useResendSmsOtpForResetPasswordMutation,
} from "./resendSmsOTPForResetPassword";

export type {
  ResendActivationLinkMutationVariables,
  ResendActivationLinkMutation,
  ResendActivationLinkMutationFn,
  ResendActivationLinkMutationHookResult,
  ResendActivationLinkMutationResult,
  ResendActivationLinkMutationOptions,
} from "./resendActivationLink";

export {
  ResendActivationLinkDocument,
  useResendActivationLinkMutation,
} from "./resendActivationLink";

export type {
  RequestWebPublicKeyCredentialRegistrationMutationVariables,
  RequestWebPublicKeyCredentialRegistrationMutation,
  RequestWebPublicKeyCredentialRegistrationMutationFn,
  RequestWebPublicKeyCredentialRegistrationMutationHookResult,
  RequestWebPublicKeyCredentialRegistrationMutationResult,
  RequestWebPublicKeyCredentialRegistrationMutationOptions,
} from "./requestWebPublicKeyCredentialRegistration";

export {
  RequestWebPublicKeyCredentialRegistrationDocument,
  useRequestWebPublicKeyCredentialRegistrationMutation,
} from "./requestWebPublicKeyCredentialRegistration";

export type {
  RequestReleaseLetterMutationVariables,
  RequestReleaseLetterMutation,
  RequestReleaseLetterMutationFn,
  RequestReleaseLetterMutationHookResult,
  RequestReleaseLetterMutationResult,
  RequestReleaseLetterMutationOptions,
} from "./requestReleaseLetter";

export {
  RequestReleaseLetterDocument,
  useRequestReleaseLetterMutation,
} from "./requestReleaseLetter";

export type {
  RequestDisbursementMutationVariables,
  RequestDisbursementMutation,
  RequestDisbursementMutationFn,
  RequestDisbursementMutationHookResult,
  RequestDisbursementMutationResult,
  RequestDisbursementMutationOptions,
} from "./requestDisbursement";

export {
  RequestDisbursementDocument,
  useRequestDisbursementMutation,
} from "./requestDisbursement";

export type {
  RemoveUserOnUserGroupMutationVariables,
  RemoveUserOnUserGroupMutation,
  RemoveUserOnUserGroupMutationFn,
  RemoveUserOnUserGroupMutationHookResult,
  RemoveUserOnUserGroupMutationResult,
  RemoveUserOnUserGroupMutationOptions,
} from "./removeUserOnUserGroup";

export {
  RemoveUserOnUserGroupDocument,
  useRemoveUserOnUserGroupMutation,
} from "./removeUserOnUserGroup";

export type {
  RemoveUserOnRoleMutationVariables,
  RemoveUserOnRoleMutation,
  RemoveUserOnRoleMutationFn,
  RemoveUserOnRoleMutationHookResult,
  RemoveUserOnRoleMutationResult,
  RemoveUserOnRoleMutationOptions,
} from "./removeUserOnRole";

export {
  RemoveUserOnRoleDocument,
  useRemoveUserOnRoleMutation,
} from "./removeUserOnRole";

export type {
  RemovePermissionOnRoleMutationVariables,
  RemovePermissionOnRoleMutation,
  RemovePermissionOnRoleMutationFn,
  RemovePermissionOnRoleMutationHookResult,
  RemovePermissionOnRoleMutationResult,
  RemovePermissionOnRoleMutationOptions,
} from "./removePermissionOnRole";

export {
  RemovePermissionOnRoleDocument,
  useRemovePermissionOnRoleMutation,
} from "./removePermissionOnRole";

export type {
  RemoveDealerOnUserGroupMutationVariables,
  RemoveDealerOnUserGroupMutation,
  RemoveDealerOnUserGroupMutationFn,
  RemoveDealerOnUserGroupMutationHookResult,
  RemoveDealerOnUserGroupMutationResult,
  RemoveDealerOnUserGroupMutationOptions,
} from "./removeDealerOnUserGroup";

export {
  RemoveDealerOnUserGroupDocument,
  useRemoveDealerOnUserGroupMutation,
} from "./removeDealerOnUserGroup";

export type {
  ReleaseReservedMobilityStockMutationVariables,
  ReleaseReservedMobilityStockMutation,
  ReleaseReservedMobilityStockMutationFn,
  ReleaseReservedMobilityStockMutationHookResult,
  ReleaseReservedMobilityStockMutationResult,
  ReleaseReservedMobilityStockMutationOptions,
} from "./releaseReservedMobilityStock";

export {
  ReleaseReservedMobilityStockDocument,
  useReleaseReservedMobilityStockMutation,
} from "./releaseReservedMobilityStock";

export type {
  ReleaseReservedConfiguratorStockMutationVariables,
  ReleaseReservedConfiguratorStockMutation,
  ReleaseReservedConfiguratorStockMutationFn,
  ReleaseReservedConfiguratorStockMutationHookResult,
  ReleaseReservedConfiguratorStockMutationResult,
  ReleaseReservedConfiguratorStockMutationOptions,
} from "./releaseReservedConfiguratorStock";

export {
  ReleaseReservedConfiguratorStockDocument,
  useReleaseReservedConfiguratorStockMutation,
} from "./releaseReservedConfiguratorStock";

export type {
  ReleaseFinderVehicleExpiryMutationVariables,
  ReleaseFinderVehicleExpiryMutation,
  ReleaseFinderVehicleExpiryMutationFn,
  ReleaseFinderVehicleExpiryMutationHookResult,
  ReleaseFinderVehicleExpiryMutationResult,
  ReleaseFinderVehicleExpiryMutationOptions,
} from "./releaseFinderVehicleExpiry";

export {
  ReleaseFinderVehicleExpiryDocument,
  useReleaseFinderVehicleExpiryMutation,
} from "./releaseFinderVehicleExpiry";

export type {
  RefreshCredentialsMutationVariables,
  RefreshCredentialsMutation,
  RefreshCredentialsMutationFn,
  RefreshCredentialsMutationHookResult,
  RefreshCredentialsMutationResult,
  RefreshCredentialsMutationOptions,
} from "./refreshCredentials";

export {
  RefreshCredentialsDocument,
  useRefreshCredentialsMutation,
} from "./refreshCredentials";

export type {
  RefreshApplicationStatusMutationVariables,
  RefreshApplicationStatusMutation,
  RefreshApplicationStatusMutationFn,
  RefreshApplicationStatusMutationHookResult,
  RefreshApplicationStatusMutationResult,
  RefreshApplicationStatusMutationOptions,
} from "./refreshApplicationStatus";

export {
  RefreshApplicationStatusDocument,
  useRefreshApplicationStatusMutation,
} from "./refreshApplicationStatus";

export type {
  QualifyLeadWithCapValuesMutationVariables,
  QualifyLeadWithCapValuesMutation,
  QualifyLeadWithCapValuesMutationFn,
  QualifyLeadWithCapValuesMutationHookResult,
  QualifyLeadWithCapValuesMutationResult,
  QualifyLeadWithCapValuesMutationOptions,
} from "./qualifyLeadWithCapValues";

export {
  QualifyLeadWithCapValuesDocument,
  useQualifyLeadWithCapValuesMutation,
} from "./qualifyLeadWithCapValues";

export type {
  QualifyLeadMutationVariables,
  QualifyLeadMutation,
  QualifyLeadMutationFn,
  QualifyLeadMutationHookResult,
  QualifyLeadMutationResult,
  QualifyLeadMutationOptions,
} from "./qualifyLead";

export { QualifyLeadDocument, useQualifyLeadMutation } from "./qualifyLead";

export type {
  ProceedWithCustomerDeviceMutationVariables,
  ProceedWithCustomerDeviceMutation,
  ProceedWithCustomerDeviceMutationFn,
  ProceedWithCustomerDeviceMutationHookResult,
  ProceedWithCustomerDeviceMutationResult,
  ProceedWithCustomerDeviceMutationOptions,
} from "./proceedWithCustomerDevice";

export {
  ProceedWithCustomerDeviceDocument,
  useProceedWithCustomerDeviceMutation,
} from "./proceedWithCustomerDevice";

export type {
  MarkLeadAsLostMutationVariables,
  MarkLeadAsLostMutation,
  MarkLeadAsLostMutationFn,
  MarkLeadAsLostMutationHookResult,
  MarkLeadAsLostMutationResult,
  MarkLeadAsLostMutationOptions,
} from "./markLeadAsLost";

export {
  MarkLeadAsLostDocument,
  useMarkLeadAsLostMutation,
} from "./markLeadAsLost";

export type {
  MarkAsContactedMutationVariables,
  MarkAsContactedMutation,
  MarkAsContactedMutationFn,
  MarkAsContactedMutationHookResult,
  MarkAsContactedMutationResult,
  MarkAsContactedMutationOptions,
} from "./markAsContacted";

export {
  MarkAsContactedDocument,
  useMarkAsContactedMutation,
} from "./markAsContacted";

export type {
  InitialSalesOfferSigningMutationVariables,
  InitialSalesOfferSigningMutation,
  InitialSalesOfferSigningMutationFn,
  InitialSalesOfferSigningMutationHookResult,
  InitialSalesOfferSigningMutationResult,
  InitialSalesOfferSigningMutationOptions,
} from "./initialSalesOfferSigning";

export {
  InitialSalesOfferSigningDocument,
  useInitialSalesOfferSigningMutation,
} from "./initialSalesOfferSigning";

export type {
  InitialSalesOfferPaymentMutationVariables,
  InitialSalesOfferPaymentMutation,
  InitialSalesOfferPaymentMutationFn,
  InitialSalesOfferPaymentMutationHookResult,
  InitialSalesOfferPaymentMutationResult,
  InitialSalesOfferPaymentMutationOptions,
} from "./initialSalesOfferPayment";

export {
  InitialSalesOfferPaymentDocument,
  useInitialSalesOfferPaymentMutation,
} from "./initialSalesOfferPayment";

export type {
  ImportVariantImagesMutationVariables,
  ImportVariantImagesMutation,
  ImportVariantImagesMutationFn,
  ImportVariantImagesMutationHookResult,
  ImportVariantImagesMutationResult,
  ImportVariantImagesMutationOptions,
} from "./importVariantImages";

export {
  ImportVariantImagesDocument,
  useImportVariantImagesMutation,
} from "./importVariantImages";

export type {
  ImportVariantMutationVariables,
  ImportVariantMutation,
  ImportVariantMutationFn,
  ImportVariantMutationHookResult,
  ImportVariantMutationResult,
  ImportVariantMutationOptions,
} from "./importVariant";

export {
  ImportVariantDocument,
  useImportVariantMutation,
} from "./importVariant";

export type {
  ImportSubModelMutationVariables,
  ImportSubModelMutation,
  ImportSubModelMutationFn,
  ImportSubModelMutationHookResult,
  ImportSubModelMutationResult,
  ImportSubModelMutationOptions,
} from "./importSubModel";

export {
  ImportSubModelDocument,
  useImportSubModelMutation,
} from "./importSubModel";

export type {
  ImportSalesControlBoardDataMutationVariables,
  ImportSalesControlBoardDataMutation,
  ImportSalesControlBoardDataMutationFn,
  ImportSalesControlBoardDataMutationHookResult,
  ImportSalesControlBoardDataMutationResult,
  ImportSalesControlBoardDataMutationOptions,
} from "./importSalesControlBoardData";

export {
  ImportSalesControlBoardDataDocument,
  useImportSalesControlBoardDataMutation,
} from "./importSalesControlBoardData";

export type {
  ImportModelMutationVariables,
  ImportModelMutation,
  ImportModelMutationFn,
  ImportModelMutationHookResult,
  ImportModelMutationResult,
  ImportModelMutationOptions,
} from "./importModel";

export { ImportModelDocument, useImportModelMutation } from "./importModel";

export type {
  ImportMakeMutationVariables,
  ImportMakeMutation,
  ImportMakeMutationFn,
  ImportMakeMutationHookResult,
  ImportMakeMutationResult,
  ImportMakeMutationOptions,
} from "./importMake";

export { ImportMakeDocument, useImportMakeMutation } from "./importMake";

export type {
  ImportLanguagePackMutationVariables,
  ImportLanguagePackMutation,
  ImportLanguagePackMutationFn,
  ImportLanguagePackMutationHookResult,
  ImportLanguagePackMutationResult,
  ImportLanguagePackMutationOptions,
} from "./importLanguagePack";

export {
  ImportLanguagePackDocument,
  useImportLanguagePackMutation,
} from "./importLanguagePack";

export type {
  ImportInventoriesMutationVariables,
  ImportInventoriesMutation,
  ImportInventoriesMutationFn,
  ImportInventoriesMutationHookResult,
  ImportInventoriesMutationResult,
  ImportInventoriesMutationOptions,
} from "./importInventories";

export {
  ImportInventoriesDocument,
  useImportInventoriesMutation,
} from "./importInventories";

export type {
  ImportFinderVehiclesLtaMutationVariables,
  ImportFinderVehiclesLtaMutation,
  ImportFinderVehiclesLtaMutationFn,
  ImportFinderVehiclesLtaMutationHookResult,
  ImportFinderVehiclesLtaMutationResult,
  ImportFinderVehiclesLtaMutationOptions,
} from "./importFinderVehiclesLta";

export {
  ImportFinderVehiclesLtaDocument,
  useImportFinderVehiclesLtaMutation,
} from "./importFinderVehiclesLta";

export type {
  ImportDealersMutationVariables,
  ImportDealersMutation,
  ImportDealersMutationFn,
  ImportDealersMutationHookResult,
  ImportDealersMutationResult,
  ImportDealersMutationOptions,
} from "./importDealers";

export {
  ImportDealersDocument,
  useImportDealersMutation,
} from "./importDealers";

export type {
  ImportConfiguratorVariantPricesMutationVariables,
  ImportConfiguratorVariantPricesMutation,
  ImportConfiguratorVariantPricesMutationFn,
  ImportConfiguratorVariantPricesMutationHookResult,
  ImportConfiguratorVariantPricesMutationResult,
  ImportConfiguratorVariantPricesMutationOptions,
} from "./importConfiguratorVariantPrices";

export {
  ImportConfiguratorVariantPricesDocument,
  useImportConfiguratorVariantPricesMutation,
} from "./importConfiguratorVariantPrices";

export type {
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutationVariables,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutation,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutationFn,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutationHookResult,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutationResult,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutationOptions,
} from "./getAgreementsAndKycFieldsFromUpdatedConfiguration";

export {
  GetAgreementsAndKycFieldsFromUpdatedConfigurationDocument,
  useGetAgreementsAndKycFieldsFromUpdatedConfigurationMutation,
} from "./getAgreementsAndKycFieldsFromUpdatedConfiguration";

export type {
  GenerateSigningOtpMutationVariables,
  GenerateSigningOtpMutation,
  GenerateSigningOtpMutationFn,
  GenerateSigningOtpMutationHookResult,
  GenerateSigningOtpMutationResult,
  GenerateSigningOtpMutationOptions,
} from "./generateSigningOTP";

export {
  GenerateSigningOtpDocument,
  useGenerateSigningOtpMutation,
} from "./generateSigningOTP";

export type {
  GenerateSalesOfferJourneyTokenMutationVariables,
  GenerateSalesOfferJourneyTokenMutation,
  GenerateSalesOfferJourneyTokenMutationFn,
  GenerateSalesOfferJourneyTokenMutationHookResult,
  GenerateSalesOfferJourneyTokenMutationResult,
  GenerateSalesOfferJourneyTokenMutationOptions,
} from "./generateSalesOfferJourneyToken";

export {
  GenerateSalesOfferJourneyTokenDocument,
  useGenerateSalesOfferJourneyTokenMutation,
} from "./generateSalesOfferJourneyToken";

export type {
  GenerateRemoteJourneyPasscodeMutationVariables,
  GenerateRemoteJourneyPasscodeMutation,
  GenerateRemoteJourneyPasscodeMutationFn,
  GenerateRemoteJourneyPasscodeMutationHookResult,
  GenerateRemoteJourneyPasscodeMutationResult,
  GenerateRemoteJourneyPasscodeMutationOptions,
} from "./generateRemoteJourneyPasscode";

export {
  GenerateRemoteJourneyPasscodeDocument,
  useGenerateRemoteJourneyPasscodeMutation,
} from "./generateRemoteJourneyPasscode";

export type {
  GenerateCancelMobilityApplicationAccessMutationVariables,
  GenerateCancelMobilityApplicationAccessMutation,
  GenerateCancelMobilityApplicationAccessMutationFn,
  GenerateCancelMobilityApplicationAccessMutationHookResult,
  GenerateCancelMobilityApplicationAccessMutationResult,
  GenerateCancelMobilityApplicationAccessMutationOptions,
} from "./generateCancelMobilityApplicationAccess";

export {
  GenerateCancelMobilityApplicationAccessDocument,
  useGenerateCancelMobilityApplicationAccessMutation,
} from "./generateCancelMobilityApplicationAccess";

export type {
  GenerateAmendMobilityApplicationAccessMutationVariables,
  GenerateAmendMobilityApplicationAccessMutation,
  GenerateAmendMobilityApplicationAccessMutationFn,
  GenerateAmendMobilityApplicationAccessMutationHookResult,
  GenerateAmendMobilityApplicationAccessMutationResult,
  GenerateAmendMobilityApplicationAccessMutationOptions,
} from "./generateAmendMobilityApplicationAccess";

export {
  GenerateAmendMobilityApplicationAccessDocument,
  useGenerateAmendMobilityApplicationAccessMutation,
} from "./generateAmendMobilityApplicationAccess";

export type {
  ExtendMobilityStockExpiryMutationVariables,
  ExtendMobilityStockExpiryMutation,
  ExtendMobilityStockExpiryMutationFn,
  ExtendMobilityStockExpiryMutationHookResult,
  ExtendMobilityStockExpiryMutationResult,
  ExtendMobilityStockExpiryMutationOptions,
} from "./extendMobilityStockExpiry";

export {
  ExtendMobilityStockExpiryDocument,
  useExtendMobilityStockExpiryMutation,
} from "./extendMobilityStockExpiry";

export type {
  ExtendGiftVoucherJourneyExpiryMutationVariables,
  ExtendGiftVoucherJourneyExpiryMutation,
  ExtendGiftVoucherJourneyExpiryMutationFn,
  ExtendGiftVoucherJourneyExpiryMutationHookResult,
  ExtendGiftVoucherJourneyExpiryMutationResult,
  ExtendGiftVoucherJourneyExpiryMutationOptions,
} from "./extendGiftVoucherJourneyExpiry";

export {
  ExtendGiftVoucherJourneyExpiryDocument,
  useExtendGiftVoucherJourneyExpiryMutation,
} from "./extendGiftVoucherJourneyExpiry";

export type {
  ExtendFinderVehicleExpiryMutationVariables,
  ExtendFinderVehicleExpiryMutation,
  ExtendFinderVehicleExpiryMutationFn,
  ExtendFinderVehicleExpiryMutationHookResult,
  ExtendFinderVehicleExpiryMutationResult,
  ExtendFinderVehicleExpiryMutationOptions,
} from "./extendFinderVehicleExpiry";

export {
  ExtendFinderVehicleExpiryDocument,
  useExtendFinderVehicleExpiryMutation,
} from "./extendFinderVehicleExpiry";

export type {
  ExtendEventJourneyExpiryMutationVariables,
  ExtendEventJourneyExpiryMutation,
  ExtendEventJourneyExpiryMutationFn,
  ExtendEventJourneyExpiryMutationHookResult,
  ExtendEventJourneyExpiryMutationResult,
  ExtendEventJourneyExpiryMutationOptions,
} from "./extendEventJourneyExpiry";

export {
  ExtendEventJourneyExpiryDocument,
  useExtendEventJourneyExpiryMutation,
} from "./extendEventJourneyExpiry";

export type {
  ExtendConfiguratorStockExpiryMutationVariables,
  ExtendConfiguratorStockExpiryMutation,
  ExtendConfiguratorStockExpiryMutationFn,
  ExtendConfiguratorStockExpiryMutationHookResult,
  ExtendConfiguratorStockExpiryMutationResult,
  ExtendConfiguratorStockExpiryMutationOptions,
} from "./extendConfiguratorStockExpiry";

export {
  ExtendConfiguratorStockExpiryDocument,
  useExtendConfiguratorStockExpiryMutation,
} from "./extendConfiguratorStockExpiry";

export type {
  EndTestDriveMutationVariables,
  EndTestDriveMutation,
  EndTestDriveMutationFn,
  EndTestDriveMutationHookResult,
  EndTestDriveMutationResult,
  EndTestDriveMutationOptions,
} from "./endTestDrive";

export { EndTestDriveDocument, useEndTestDriveMutation } from "./endTestDrive";

export type {
  EnableAuthenticatorMutationVariables,
  EnableAuthenticatorMutation,
  EnableAuthenticatorMutationFn,
  EnableAuthenticatorMutationHookResult,
  EnableAuthenticatorMutationResult,
  EnableAuthenticatorMutationOptions,
} from "./enableAuthenticator";

export {
  EnableAuthenticatorDocument,
  useEnableAuthenticatorMutation,
} from "./enableAuthenticator";

export type {
  DraftStandardApplicationFromLeadMutationVariables,
  DraftStandardApplicationFromLeadMutation,
  DraftStandardApplicationFromLeadMutationFn,
  DraftStandardApplicationFromLeadMutationHookResult,
  DraftStandardApplicationFromLeadMutationResult,
  DraftStandardApplicationFromLeadMutationOptions,
} from "./draftStandardApplicationFromLead";

export {
  DraftStandardApplicationFromLeadDocument,
  useDraftStandardApplicationFromLeadMutation,
} from "./draftStandardApplicationFromLead";

export type {
  DraftMobilityApplicationMutationVariables,
  DraftMobilityApplicationMutation,
  DraftMobilityApplicationMutationFn,
  DraftMobilityApplicationMutationHookResult,
  DraftMobilityApplicationMutationResult,
  DraftMobilityApplicationMutationOptions,
} from "./draftMobilityApplication";

export {
  DraftMobilityApplicationDocument,
  useDraftMobilityApplicationMutation,
} from "./draftMobilityApplication";

export type {
  DraftLaunchpadApplicationMutationVariables,
  DraftLaunchpadApplicationMutation,
  DraftLaunchpadApplicationMutationFn,
  DraftLaunchpadApplicationMutationHookResult,
  DraftLaunchpadApplicationMutationResult,
  DraftLaunchpadApplicationMutationOptions,
} from "./draftLaunchpadApplication";

export {
  DraftLaunchpadApplicationDocument,
  useDraftLaunchpadApplicationMutation,
} from "./draftLaunchpadApplication";

export type {
  DraftGiftVoucherMutationVariables,
  DraftGiftVoucherMutation,
  DraftGiftVoucherMutationFn,
  DraftGiftVoucherMutationHookResult,
  DraftGiftVoucherMutationResult,
  DraftGiftVoucherMutationOptions,
} from "./draftGiftVoucher";

export {
  DraftGiftVoucherDocument,
  useDraftGiftVoucherMutation,
} from "./draftGiftVoucher";

export type {
  DraftFinderApplicationFromLeadMutationVariables,
  DraftFinderApplicationFromLeadMutation,
  DraftFinderApplicationFromLeadMutationFn,
  DraftFinderApplicationFromLeadMutationHookResult,
  DraftFinderApplicationFromLeadMutationResult,
  DraftFinderApplicationFromLeadMutationOptions,
} from "./draftFinderApplicationFromLead";

export {
  DraftFinderApplicationFromLeadDocument,
  useDraftFinderApplicationFromLeadMutation,
} from "./draftFinderApplicationFromLead";

export type {
  DraftFinderApplicationMutationVariables,
  DraftFinderApplicationMutation,
  DraftFinderApplicationMutationFn,
  DraftFinderApplicationMutationHookResult,
  DraftFinderApplicationMutationResult,
  DraftFinderApplicationMutationOptions,
} from "./draftFinderApplication";

export {
  DraftFinderApplicationDocument,
  useDraftFinderApplicationMutation,
} from "./draftFinderApplication";

export type {
  DraftEventApplicationMutationVariables,
  DraftEventApplicationMutation,
  DraftEventApplicationMutationFn,
  DraftEventApplicationMutationHookResult,
  DraftEventApplicationMutationResult,
  DraftEventApplicationMutationOptions,
} from "./draftEventApplication";

export {
  DraftEventApplicationDocument,
  useDraftEventApplicationMutation,
} from "./draftEventApplication";

export type {
  DraftConfiguratorApplicationMutationVariables,
  DraftConfiguratorApplicationMutation,
  DraftConfiguratorApplicationMutationFn,
  DraftConfiguratorApplicationMutationHookResult,
  DraftConfiguratorApplicationMutationResult,
  DraftConfiguratorApplicationMutationOptions,
} from "./draftConfiguratorApplication";

export {
  DraftConfiguratorApplicationDocument,
  useDraftConfiguratorApplicationMutation,
} from "./draftConfiguratorApplication";

export type {
  DraftApplicationMutationVariables,
  DraftApplicationMutation,
  DraftApplicationMutationFn,
  DraftApplicationMutationHookResult,
  DraftApplicationMutationResult,
  DraftApplicationMutationOptions,
} from "./draftApplication";

export {
  DraftApplicationDocument,
  useDraftApplicationMutation,
} from "./draftApplication";

export type {
  DownloadSpecificationDocumentMutationVariables,
  DownloadSpecificationDocumentMutation,
  DownloadSpecificationDocumentMutationFn,
  DownloadSpecificationDocumentMutationHookResult,
  DownloadSpecificationDocumentMutationResult,
  DownloadSpecificationDocumentMutationOptions,
} from "./downloadSpecificationDocument";

export {
  DownloadSpecificationDocumentDocument,
  useDownloadSpecificationDocumentMutation,
} from "./downloadSpecificationDocument";

export type {
  DownloadSalesOfferDocumentMutationVariables,
  DownloadSalesOfferDocumentMutation,
  DownloadSalesOfferDocumentMutationFn,
  DownloadSalesOfferDocumentMutationHookResult,
  DownloadSalesOfferDocumentMutationResult,
  DownloadSalesOfferDocumentMutationOptions,
} from "./downloadSalesOfferDocument";

export {
  DownloadSalesOfferDocumentDocument,
  useDownloadSalesOfferDocumentMutation,
} from "./downloadSalesOfferDocument";

export type {
  DisableAuthenticatorMutationVariables,
  DisableAuthenticatorMutation,
  DisableAuthenticatorMutationFn,
  DisableAuthenticatorMutationHookResult,
  DisableAuthenticatorMutationResult,
  DisableAuthenticatorMutationOptions,
} from "./disableAuthenticator";

export {
  DisableAuthenticatorDocument,
  useDisableAuthenticatorMutation,
} from "./disableAuthenticator";

export type {
  DeleteWhatsappLiveChatSettingsMutationVariables,
  DeleteWhatsappLiveChatSettingsMutation,
  DeleteWhatsappLiveChatSettingsMutationFn,
  DeleteWhatsappLiveChatSettingsMutationHookResult,
  DeleteWhatsappLiveChatSettingsMutationResult,
  DeleteWhatsappLiveChatSettingsMutationOptions,
} from "./deleteWhatsappLiveChatSettings";

export {
  DeleteWhatsappLiveChatSettingsDocument,
  useDeleteWhatsappLiveChatSettingsMutation,
} from "./deleteWhatsappLiveChatSettings";

export type {
  DeleteWebsiteSocialMediaAssetMutationVariables,
  DeleteWebsiteSocialMediaAssetMutation,
  DeleteWebsiteSocialMediaAssetMutationFn,
  DeleteWebsiteSocialMediaAssetMutationHookResult,
  DeleteWebsiteSocialMediaAssetMutationResult,
  DeleteWebsiteSocialMediaAssetMutationOptions,
} from "./deleteWebsiteSocialMediaAsset";

export {
  DeleteWebsiteSocialMediaAssetDocument,
  useDeleteWebsiteSocialMediaAssetMutation,
} from "./deleteWebsiteSocialMediaAsset";

export type {
  DeleteWebsiteSocialMediaMutationVariables,
  DeleteWebsiteSocialMediaMutation,
  DeleteWebsiteSocialMediaMutationFn,
  DeleteWebsiteSocialMediaMutationHookResult,
  DeleteWebsiteSocialMediaMutationResult,
  DeleteWebsiteSocialMediaMutationOptions,
} from "./deleteWebsiteSocialMedia";

export {
  DeleteWebsiteSocialMediaDocument,
  useDeleteWebsiteSocialMediaMutation,
} from "./deleteWebsiteSocialMedia";

export type {
  DeleteWebPageImageMutationVariables,
  DeleteWebPageImageMutation,
  DeleteWebPageImageMutationFn,
  DeleteWebPageImageMutationHookResult,
  DeleteWebPageImageMutationResult,
  DeleteWebPageImageMutationOptions,
} from "./deleteWebPageImage";

export {
  DeleteWebPageImageDocument,
  useDeleteWebPageImageMutation,
} from "./deleteWebPageImage";

export type {
  DeleteWebPageBlockMutationVariables,
  DeleteWebPageBlockMutation,
  DeleteWebPageBlockMutationFn,
  DeleteWebPageBlockMutationHookResult,
  DeleteWebPageBlockMutationResult,
  DeleteWebPageBlockMutationOptions,
} from "./deleteWebPageBlock";

export {
  DeleteWebPageBlockDocument,
  useDeleteWebPageBlockMutation,
} from "./deleteWebPageBlock";

export type {
  DeleteWebPageMutationVariables,
  DeleteWebPageMutation,
  DeleteWebPageMutationFn,
  DeleteWebPageMutationHookResult,
  DeleteWebPageMutationResult,
  DeleteWebPageMutationOptions,
} from "./deleteWebPage";

export {
  DeleteWebPageDocument,
  useDeleteWebPageMutation,
} from "./deleteWebPage";

export type {
  DeleteVisitAppointmentModuleAssetMutationVariables,
  DeleteVisitAppointmentModuleAssetMutation,
  DeleteVisitAppointmentModuleAssetMutationFn,
  DeleteVisitAppointmentModuleAssetMutationHookResult,
  DeleteVisitAppointmentModuleAssetMutationResult,
  DeleteVisitAppointmentModuleAssetMutationOptions,
} from "./deleteVisitAppointmentModuleAsset";

export {
  DeleteVisitAppointmentModuleAssetDocument,
  useDeleteVisitAppointmentModuleAssetMutation,
} from "./deleteVisitAppointmentModuleAsset";

export type {
  DeleteVehicleMutationVariables,
  DeleteVehicleMutation,
  DeleteVehicleMutationFn,
  DeleteVehicleMutationHookResult,
  DeleteVehicleMutationResult,
  DeleteVehicleMutationOptions,
} from "./deleteVehicle";

export {
  DeleteVehicleDocument,
  useDeleteVehicleMutation,
} from "./deleteVehicle";

export type {
  DeleteVariantConfiguratorTrimSettingAssetMutationVariables,
  DeleteVariantConfiguratorTrimSettingAssetMutation,
  DeleteVariantConfiguratorTrimSettingAssetMutationFn,
  DeleteVariantConfiguratorTrimSettingAssetMutationHookResult,
  DeleteVariantConfiguratorTrimSettingAssetMutationResult,
  DeleteVariantConfiguratorTrimSettingAssetMutationOptions,
} from "./deleteVariantConfiguratorTrimSettingAsset";

export {
  DeleteVariantConfiguratorTrimSettingAssetDocument,
  useDeleteVariantConfiguratorTrimSettingAssetMutation,
} from "./deleteVariantConfiguratorTrimSettingAsset";

export type {
  DeleteVariantConfiguratorPackageSectionImageAssetMutationVariables,
  DeleteVariantConfiguratorPackageSectionImageAssetMutation,
  DeleteVariantConfiguratorPackageSectionImageAssetMutationFn,
  DeleteVariantConfiguratorPackageSectionImageAssetMutationHookResult,
  DeleteVariantConfiguratorPackageSectionImageAssetMutationResult,
  DeleteVariantConfiguratorPackageSectionImageAssetMutationOptions,
} from "./deleteVariantConfiguratorPackageSectionImageAsset";

export {
  DeleteVariantConfiguratorPackageSectionImageAssetDocument,
  useDeleteVariantConfiguratorPackageSectionImageAssetMutation,
} from "./deleteVariantConfiguratorPackageSectionImageAsset";

export type {
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutationVariables,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutation,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutationFn,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutationHookResult,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutationResult,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutationOptions,
} from "./deleteVariantConfiguratorPackageAdditionalDetailsAsset";

export {
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetDocument,
  useDeleteVariantConfiguratorPackageAdditionalDetailsAssetMutation,
} from "./deleteVariantConfiguratorPackageAdditionalDetailsAsset";

export type {
  DeleteVariantConfiguratorOptionSettingAssetMutationVariables,
  DeleteVariantConfiguratorOptionSettingAssetMutation,
  DeleteVariantConfiguratorOptionSettingAssetMutationFn,
  DeleteVariantConfiguratorOptionSettingAssetMutationHookResult,
  DeleteVariantConfiguratorOptionSettingAssetMutationResult,
  DeleteVariantConfiguratorOptionSettingAssetMutationOptions,
} from "./deleteVariantConfiguratorOptionSettingAsset";

export {
  DeleteVariantConfiguratorOptionSettingAssetDocument,
  useDeleteVariantConfiguratorOptionSettingAssetMutation,
} from "./deleteVariantConfiguratorOptionSettingAsset";

export type {
  DeleteVariantConfiguratorOptionAssetMutationVariables,
  DeleteVariantConfiguratorOptionAssetMutation,
  DeleteVariantConfiguratorOptionAssetMutationFn,
  DeleteVariantConfiguratorOptionAssetMutationHookResult,
  DeleteVariantConfiguratorOptionAssetMutationResult,
  DeleteVariantConfiguratorOptionAssetMutationOptions,
} from "./deleteVariantConfiguratorOptionAsset";

export {
  DeleteVariantConfiguratorOptionAssetDocument,
  useDeleteVariantConfiguratorOptionAssetMutation,
} from "./deleteVariantConfiguratorOptionAsset";

export type {
  DeleteVariantConfiguratorMatrixAssetMutationVariables,
  DeleteVariantConfiguratorMatrixAssetMutation,
  DeleteVariantConfiguratorMatrixAssetMutationFn,
  DeleteVariantConfiguratorMatrixAssetMutationHookResult,
  DeleteVariantConfiguratorMatrixAssetMutationResult,
  DeleteVariantConfiguratorMatrixAssetMutationOptions,
} from "./deleteVariantConfiguratorMatrixAsset";

export {
  DeleteVariantConfiguratorMatrixAssetDocument,
  useDeleteVariantConfiguratorMatrixAssetMutation,
} from "./deleteVariantConfiguratorMatrixAsset";

export type {
  DeleteVariantConfiguratorColorSettingAssetMutationVariables,
  DeleteVariantConfiguratorColorSettingAssetMutation,
  DeleteVariantConfiguratorColorSettingAssetMutationFn,
  DeleteVariantConfiguratorColorSettingAssetMutationHookResult,
  DeleteVariantConfiguratorColorSettingAssetMutationResult,
  DeleteVariantConfiguratorColorSettingAssetMutationOptions,
} from "./deleteVariantConfiguratorColorSettingAsset";

export {
  DeleteVariantConfiguratorColorSettingAssetDocument,
  useDeleteVariantConfiguratorColorSettingAssetMutation,
} from "./deleteVariantConfiguratorColorSettingAsset";

export type {
  DeleteVariantConfiguratorMutationVariables,
  DeleteVariantConfiguratorMutation,
  DeleteVariantConfiguratorMutationFn,
  DeleteVariantConfiguratorMutationHookResult,
  DeleteVariantConfiguratorMutationResult,
  DeleteVariantConfiguratorMutationOptions,
} from "./deleteVariantConfigurator";

export {
  DeleteVariantConfiguratorDocument,
  useDeleteVariantConfiguratorMutation,
} from "./deleteVariantConfigurator";

export type {
  DeleteVariantAssetMutationVariables,
  DeleteVariantAssetMutation,
  DeleteVariantAssetMutationFn,
  DeleteVariantAssetMutationHookResult,
  DeleteVariantAssetMutationResult,
  DeleteVariantAssetMutationOptions,
} from "./deleteVariantAsset";

export {
  DeleteVariantAssetDocument,
  useDeleteVariantAssetMutation,
} from "./deleteVariantAsset";

export type {
  DeleteUserlikeChatbotSettingsMutationVariables,
  DeleteUserlikeChatbotSettingsMutation,
  DeleteUserlikeChatbotSettingsMutationFn,
  DeleteUserlikeChatbotSettingsMutationHookResult,
  DeleteUserlikeChatbotSettingsMutationResult,
  DeleteUserlikeChatbotSettingsMutationOptions,
} from "./deleteUserlikeChatbotSettings";

export {
  DeleteUserlikeChatbotSettingsDocument,
  useDeleteUserlikeChatbotSettingsMutation,
} from "./deleteUserlikeChatbotSettings";

export type {
  DeleteUserGroupMutationVariables,
  DeleteUserGroupMutation,
  DeleteUserGroupMutationFn,
  DeleteUserGroupMutationHookResult,
  DeleteUserGroupMutationResult,
  DeleteUserGroupMutationOptions,
} from "./deleteUserGroup";

export {
  DeleteUserGroupDocument,
  useDeleteUserGroupMutation,
} from "./deleteUserGroup";

export type {
  DeleteUserAssetMutationVariables,
  DeleteUserAssetMutation,
  DeleteUserAssetMutationFn,
  DeleteUserAssetMutationHookResult,
  DeleteUserAssetMutationResult,
  DeleteUserAssetMutationOptions,
} from "./deleteUserAsset";

export {
  DeleteUserAssetDocument,
  useDeleteUserAssetMutation,
} from "./deleteUserAsset";

export type {
  DeleteUserMutationVariables,
  DeleteUserMutation,
  DeleteUserMutationFn,
  DeleteUserMutationHookResult,
  DeleteUserMutationResult,
  DeleteUserMutationOptions,
} from "./deleteUser";

export { DeleteUserDocument, useDeleteUserMutation } from "./deleteUser";

export type {
  DeleteTtbPaymentSettingsMutationVariables,
  DeleteTtbPaymentSettingsMutation,
  DeleteTtbPaymentSettingsMutationFn,
  DeleteTtbPaymentSettingsMutationHookResult,
  DeleteTtbPaymentSettingsMutationResult,
  DeleteTtbPaymentSettingsMutationOptions,
} from "./deleteTtbPaymentSettings";

export {
  DeleteTtbPaymentSettingsDocument,
  useDeleteTtbPaymentSettingsMutation,
} from "./deleteTtbPaymentSettings";

export type {
  DeleteStockInventoryMutationVariables,
  DeleteStockInventoryMutation,
  DeleteStockInventoryMutationFn,
  DeleteStockInventoryMutationHookResult,
  DeleteStockInventoryMutationResult,
  DeleteStockInventoryMutationOptions,
} from "./deleteStockInventory";

export {
  DeleteStockInventoryDocument,
  useDeleteStockInventoryMutation,
} from "./deleteStockInventory";

export type {
  DeleteStockAssetMutationVariables,
  DeleteStockAssetMutation,
  DeleteStockAssetMutationFn,
  DeleteStockAssetMutationHookResult,
  DeleteStockAssetMutationResult,
  DeleteStockAssetMutationOptions,
} from "./deleteStockAsset";

export {
  DeleteStockAssetDocument,
  useDeleteStockAssetMutation,
} from "./deleteStockAsset";

export type {
  DeleteStandardApplicationModuleAssetMutationVariables,
  DeleteStandardApplicationModuleAssetMutation,
  DeleteStandardApplicationModuleAssetMutationFn,
  DeleteStandardApplicationModuleAssetMutationHookResult,
  DeleteStandardApplicationModuleAssetMutationResult,
  DeleteStandardApplicationModuleAssetMutationOptions,
} from "./deleteStandardApplicationModuleAsset";

export {
  DeleteStandardApplicationModuleAssetDocument,
  useDeleteStandardApplicationModuleAssetMutation,
} from "./deleteStandardApplicationModuleAsset";

export type {
  DeleteSalesOfferDocumentMutationVariables,
  DeleteSalesOfferDocumentMutation,
  DeleteSalesOfferDocumentMutationFn,
  DeleteSalesOfferDocumentMutationHookResult,
  DeleteSalesOfferDocumentMutationResult,
  DeleteSalesOfferDocumentMutationOptions,
} from "./deleteSalesOfferDocument";

export {
  DeleteSalesOfferDocumentDocument,
  useDeleteSalesOfferDocumentMutation,
} from "./deleteSalesOfferDocument";

export type {
  DeleteRouterMutationVariables,
  DeleteRouterMutation,
  DeleteRouterMutationFn,
  DeleteRouterMutationHookResult,
  DeleteRouterMutationResult,
  DeleteRouterMutationOptions,
} from "./deleteRouter";

export { DeleteRouterDocument, useDeleteRouterMutation } from "./deleteRouter";

export type {
  DeleteRoleMutationVariables,
  DeleteRoleMutation,
  DeleteRoleMutationFn,
  DeleteRoleMutationHookResult,
  DeleteRoleMutationResult,
  DeleteRoleMutationOptions,
} from "./deleteRole";

export { DeleteRoleDocument, useDeleteRoleMutation } from "./deleteRole";

export type {
  DeletePromoCodeMutationVariables,
  DeletePromoCodeMutation,
  DeletePromoCodeMutationFn,
  DeletePromoCodeMutationHookResult,
  DeletePromoCodeMutationResult,
  DeletePromoCodeMutationOptions,
} from "./deletePromoCode";

export {
  DeletePromoCodeDocument,
  useDeletePromoCodeMutation,
} from "./deletePromoCode";

export type {
  DeletePorschePaymentSettingsMutationVariables,
  DeletePorschePaymentSettingsMutation,
  DeletePorschePaymentSettingsMutationFn,
  DeletePorschePaymentSettingsMutationHookResult,
  DeletePorschePaymentSettingsMutationResult,
  DeletePorschePaymentSettingsMutationOptions,
} from "./deletePorschePaymentSettings";

export {
  DeletePorschePaymentSettingsDocument,
  useDeletePorschePaymentSettingsMutation,
} from "./deletePorschePaymentSettings";

export type {
  DeletePayGatePaymentSettingsMutationVariables,
  DeletePayGatePaymentSettingsMutation,
  DeletePayGatePaymentSettingsMutationFn,
  DeletePayGatePaymentSettingsMutationHookResult,
  DeletePayGatePaymentSettingsMutationResult,
  DeletePayGatePaymentSettingsMutationOptions,
} from "./deletePayGatePaymentSettings";

export {
  DeletePayGatePaymentSettingsDocument,
  useDeletePayGatePaymentSettingsMutation,
} from "./deletePayGatePaymentSettings";

export type {
  DeletePackageSettingMutationVariables,
  DeletePackageSettingMutation,
  DeletePackageSettingMutationFn,
  DeletePackageSettingMutationHookResult,
  DeletePackageSettingMutationResult,
  DeletePackageSettingMutationOptions,
} from "./deletePackageSetting";

export {
  DeletePackageSettingDocument,
  useDeletePackageSettingMutation,
} from "./deletePackageSetting";

export type {
  DeleteOptionsBlockMutationVariables,
  DeleteOptionsBlockMutation,
  DeleteOptionsBlockMutationFn,
  DeleteOptionsBlockMutationHookResult,
  DeleteOptionsBlockMutationResult,
  DeleteOptionsBlockMutationOptions,
} from "./deleteOptionsBlock";

export {
  DeleteOptionsBlockDocument,
  useDeleteOptionsBlockMutation,
} from "./deleteOptionsBlock";

export type {
  DeleteMyInfoSettingMutationVariables,
  DeleteMyInfoSettingMutation,
  DeleteMyInfoSettingMutationFn,
  DeleteMyInfoSettingMutationHookResult,
  DeleteMyInfoSettingMutationResult,
  DeleteMyInfoSettingMutationOptions,
} from "./deleteMyInfoSetting";

export {
  DeleteMyInfoSettingDocument,
  useDeleteMyInfoSettingMutation,
} from "./deleteMyInfoSetting";

export type {
  DeleteModuleMutationVariables,
  DeleteModuleMutation,
  DeleteModuleMutationFn,
  DeleteModuleMutationHookResult,
  DeleteModuleMutationResult,
  DeleteModuleMutationOptions,
} from "./deleteModule";

export { DeleteModuleDocument, useDeleteModuleMutation } from "./deleteModule";

export type {
  DeleteModelConfiguratorAssetMutationVariables,
  DeleteModelConfiguratorAssetMutation,
  DeleteModelConfiguratorAssetMutationFn,
  DeleteModelConfiguratorAssetMutationHookResult,
  DeleteModelConfiguratorAssetMutationResult,
  DeleteModelConfiguratorAssetMutationOptions,
} from "./deleteModelConfiguratorAsset";

export {
  DeleteModelConfiguratorAssetDocument,
  useDeleteModelConfiguratorAssetMutation,
} from "./deleteModelConfiguratorAsset";

export type {
  DeleteModelConfiguratorMutationVariables,
  DeleteModelConfiguratorMutation,
  DeleteModelConfiguratorMutationFn,
  DeleteModelConfiguratorMutationHookResult,
  DeleteModelConfiguratorMutationResult,
  DeleteModelConfiguratorMutationOptions,
} from "./deleteModelConfigurator";

export {
  DeleteModelConfiguratorDocument,
  useDeleteModelConfiguratorMutation,
} from "./deleteModelConfigurator";

export type {
  DeleteMobilityModuleEmailAssetMutationVariables,
  DeleteMobilityModuleEmailAssetMutation,
  DeleteMobilityModuleEmailAssetMutationFn,
  DeleteMobilityModuleEmailAssetMutationHookResult,
  DeleteMobilityModuleEmailAssetMutationResult,
  DeleteMobilityModuleEmailAssetMutationOptions,
} from "./deleteMobilityModuleEmailAsset";

export {
  DeleteMobilityModuleEmailAssetDocument,
  useDeleteMobilityModuleEmailAssetMutation,
} from "./deleteMobilityModuleEmailAsset";

export type {
  DeleteMobilityMutationVariables,
  DeleteMobilityMutation,
  DeleteMobilityMutationFn,
  DeleteMobilityMutationHookResult,
  DeleteMobilityMutationResult,
  DeleteMobilityMutationOptions,
} from "./deleteMobility";

export {
  DeleteMobilityDocument,
  useDeleteMobilityMutation,
} from "./deleteMobility";

export type {
  DeleteMaintenanceModuleAssetMutationVariables,
  DeleteMaintenanceModuleAssetMutation,
  DeleteMaintenanceModuleAssetMutationFn,
  DeleteMaintenanceModuleAssetMutationHookResult,
  DeleteMaintenanceModuleAssetMutationResult,
  DeleteMaintenanceModuleAssetMutationOptions,
} from "./deleteMaintenanceModuleAsset";

export {
  DeleteMaintenanceModuleAssetDocument,
  useDeleteMaintenanceModuleAssetMutation,
} from "./deleteMaintenanceModuleAsset";

export type {
  DeleteLeadDocumentMutationVariables,
  DeleteLeadDocumentMutation,
  DeleteLeadDocumentMutationFn,
  DeleteLeadDocumentMutationHookResult,
  DeleteLeadDocumentMutationResult,
  DeleteLeadDocumentMutationOptions,
} from "./deleteLeadDocument";

export {
  DeleteLeadDocumentDocument,
  useDeleteLeadDocumentMutation,
} from "./deleteLeadDocument";

export type {
  DeleteLanguagePackMutationVariables,
  DeleteLanguagePackMutation,
  DeleteLanguagePackMutationFn,
  DeleteLanguagePackMutationHookResult,
  DeleteLanguagePackMutationResult,
  DeleteLanguagePackMutationOptions,
} from "./deleteLanguagePack";

export {
  DeleteLanguagePackDocument,
  useDeleteLanguagePackMutation,
} from "./deleteLanguagePack";

export type {
  DeleteLabelsMutationVariables,
  DeleteLabelsMutation,
  DeleteLabelsMutationFn,
  DeleteLabelsMutationHookResult,
  DeleteLabelsMutationResult,
  DeleteLabelsMutationOptions,
} from "./deleteLabels";

export { DeleteLabelsDocument, useDeleteLabelsMutation } from "./deleteLabels";

export type {
  DeleteKycPresetsMutationVariables,
  DeleteKycPresetsMutation,
  DeleteKycPresetsMutationFn,
  DeleteKycPresetsMutationHookResult,
  DeleteKycPresetsMutationResult,
  DeleteKycPresetsMutationOptions,
} from "./deleteKYCPresets";

export {
  DeleteKycPresetsDocument,
  useDeleteKycPresetsMutation,
} from "./deleteKYCPresets";

export type {
  DeleteInventoriesMutationVariables,
  DeleteInventoriesMutation,
  DeleteInventoriesMutationFn,
  DeleteInventoriesMutationHookResult,
  DeleteInventoriesMutationResult,
  DeleteInventoriesMutationOptions,
} from "./deleteInventories";

export {
  DeleteInventoriesDocument,
  useDeleteInventoriesMutation,
} from "./deleteInventories";

export type {
  DeleteInsurerMutationVariables,
  DeleteInsurerMutation,
  DeleteInsurerMutationFn,
  DeleteInsurerMutationHookResult,
  DeleteInsurerMutationResult,
  DeleteInsurerMutationOptions,
} from "./deleteInsurer";

export {
  DeleteInsurerDocument,
  useDeleteInsurerMutation,
} from "./deleteInsurer";

export type {
  DeleteInsuranceProductMutationVariables,
  DeleteInsuranceProductMutation,
  DeleteInsuranceProductMutationFn,
  DeleteInsuranceProductMutationHookResult,
  DeleteInsuranceProductMutationResult,
  DeleteInsuranceProductMutationOptions,
} from "./deleteInsuranceProduct";

export {
  DeleteInsuranceProductDocument,
  useDeleteInsuranceProductMutation,
} from "./deleteInsuranceProduct";

export type {
  DeleteGiftVoucherModuleAssetMutationVariables,
  DeleteGiftVoucherModuleAssetMutation,
  DeleteGiftVoucherModuleAssetMutationFn,
  DeleteGiftVoucherModuleAssetMutationHookResult,
  DeleteGiftVoucherModuleAssetMutationResult,
  DeleteGiftVoucherModuleAssetMutationOptions,
} from "./deleteGiftVoucherModuleAsset";

export {
  DeleteGiftVoucherModuleAssetDocument,
  useDeleteGiftVoucherModuleAssetMutation,
} from "./deleteGiftVoucherModuleAsset";

export type {
  DeleteGiftVoucherDocumentMutationVariables,
  DeleteGiftVoucherDocumentMutation,
  DeleteGiftVoucherDocumentMutationFn,
  DeleteGiftVoucherDocumentMutationHookResult,
  DeleteGiftVoucherDocumentMutationResult,
  DeleteGiftVoucherDocumentMutationOptions,
} from "./deleteGiftVoucherDocument";

export {
  DeleteGiftVoucherDocumentDocument,
  useDeleteGiftVoucherDocumentMutation,
} from "./deleteGiftVoucherDocument";

export type {
  DeleteFiservPaymentSettingsMutationVariables,
  DeleteFiservPaymentSettingsMutation,
  DeleteFiservPaymentSettingsMutationFn,
  DeleteFiservPaymentSettingsMutationHookResult,
  DeleteFiservPaymentSettingsMutationResult,
  DeleteFiservPaymentSettingsMutationOptions,
} from "./deleteFiservPaymentSettings";

export {
  DeleteFiservPaymentSettingsDocument,
  useDeleteFiservPaymentSettingsMutation,
} from "./deleteFiservPaymentSettings";

export type {
  DeleteFinderApplicationModuleAssetMutationVariables,
  DeleteFinderApplicationModuleAssetMutation,
  DeleteFinderApplicationModuleAssetMutationFn,
  DeleteFinderApplicationModuleAssetMutationHookResult,
  DeleteFinderApplicationModuleAssetMutationResult,
  DeleteFinderApplicationModuleAssetMutationOptions,
} from "./deleteFinderApplicationModuleAsset";

export {
  DeleteFinderApplicationModuleAssetDocument,
  useDeleteFinderApplicationModuleAssetMutation,
} from "./deleteFinderApplicationModuleAsset";

export type {
  DeleteFinanceProductMutationVariables,
  DeleteFinanceProductMutation,
  DeleteFinanceProductMutationFn,
  DeleteFinanceProductMutationHookResult,
  DeleteFinanceProductMutationResult,
  DeleteFinanceProductMutationOptions,
} from "./deleteFinanceProduct";

export {
  DeleteFinanceProductDocument,
  useDeleteFinanceProductMutation,
} from "./deleteFinanceProduct";

export type {
  DeleteEventLevelAssetMutationVariables,
  DeleteEventLevelAssetMutation,
  DeleteEventLevelAssetMutationFn,
  DeleteEventLevelAssetMutationHookResult,
  DeleteEventLevelAssetMutationResult,
  DeleteEventLevelAssetMutationOptions,
} from "./deleteEventLevelAsset";

export {
  DeleteEventLevelAssetDocument,
  useDeleteEventLevelAssetMutation,
} from "./deleteEventLevelAsset";

export type {
  DeleteEventApplicationModuleAssetMutationVariables,
  DeleteEventApplicationModuleAssetMutation,
  DeleteEventApplicationModuleAssetMutationFn,
  DeleteEventApplicationModuleAssetMutationHookResult,
  DeleteEventApplicationModuleAssetMutationResult,
  DeleteEventApplicationModuleAssetMutationOptions,
} from "./deleteEventApplicationModuleAsset";

export {
  DeleteEventApplicationModuleAssetDocument,
  useDeleteEventApplicationModuleAssetMutation,
} from "./deleteEventApplicationModuleAsset";

export type {
  DeleteEventMutationVariables,
  DeleteEventMutation,
  DeleteEventMutationFn,
  DeleteEventMutationHookResult,
  DeleteEventMutationResult,
  DeleteEventMutationOptions,
} from "./deleteEvent";

export { DeleteEventDocument, useDeleteEventMutation } from "./deleteEvent";

export type {
  DeleteEndpointMutationVariables,
  DeleteEndpointMutation,
  DeleteEndpointMutationFn,
  DeleteEndpointMutationHookResult,
  DeleteEndpointMutationResult,
  DeleteEndpointMutationOptions,
} from "./deleteEndpoint";

export {
  DeleteEndpointDocument,
  useDeleteEndpointMutation,
} from "./deleteEndpoint";

export type {
  DeleteEdmSocialMediaAssetMutationVariables,
  DeleteEdmSocialMediaAssetMutation,
  DeleteEdmSocialMediaAssetMutationFn,
  DeleteEdmSocialMediaAssetMutationHookResult,
  DeleteEdmSocialMediaAssetMutationResult,
  DeleteEdmSocialMediaAssetMutationOptions,
} from "./deleteEdmSocialMediaAsset";

export {
  DeleteEdmSocialMediaAssetDocument,
  useDeleteEdmSocialMediaAssetMutation,
} from "./deleteEdmSocialMediaAsset";

export type {
  DeleteEdmEmailSocialMediaMutationVariables,
  DeleteEdmEmailSocialMediaMutation,
  DeleteEdmEmailSocialMediaMutationFn,
  DeleteEdmEmailSocialMediaMutationHookResult,
  DeleteEdmEmailSocialMediaMutationResult,
  DeleteEdmEmailSocialMediaMutationOptions,
} from "./deleteEdmEmailSocialMedia";

export {
  DeleteEdmEmailSocialMediaDocument,
  useDeleteEdmEmailSocialMediaMutation,
} from "./deleteEdmEmailSocialMedia";

export type {
  DeleteDocusignSettingMutationVariables,
  DeleteDocusignSettingMutation,
  DeleteDocusignSettingMutationFn,
  DeleteDocusignSettingMutationHookResult,
  DeleteDocusignSettingMutationResult,
  DeleteDocusignSettingMutationOptions,
} from "./deleteDocusignSetting";

export {
  DeleteDocusignSettingDocument,
  useDeleteDocusignSettingMutation,
} from "./deleteDocusignSetting";

export type {
  DeleteDealerSocialMediaAssetMutationVariables,
  DeleteDealerSocialMediaAssetMutation,
  DeleteDealerSocialMediaAssetMutationFn,
  DeleteDealerSocialMediaAssetMutationHookResult,
  DeleteDealerSocialMediaAssetMutationResult,
  DeleteDealerSocialMediaAssetMutationOptions,
} from "./deleteDealerSocialMediaAsset";

export {
  DeleteDealerSocialMediaAssetDocument,
  useDeleteDealerSocialMediaAssetMutation,
} from "./deleteDealerSocialMediaAsset";

export type {
  DeleteDealerSocialMediaMutationVariables,
  DeleteDealerSocialMediaMutation,
  DeleteDealerSocialMediaMutationFn,
  DeleteDealerSocialMediaMutationHookResult,
  DeleteDealerSocialMediaMutationResult,
  DeleteDealerSocialMediaMutationOptions,
} from "./deleteDealerSocialMedia";

export {
  DeleteDealerSocialMediaDocument,
  useDeleteDealerSocialMediaMutation,
} from "./deleteDealerSocialMedia";

export type {
  DeleteDealerMutationVariables,
  DeleteDealerMutation,
  DeleteDealerMutationFn,
  DeleteDealerMutationHookResult,
  DeleteDealerMutationResult,
  DeleteDealerMutationOptions,
} from "./deleteDealer";

export { DeleteDealerDocument, useDeleteDealerMutation } from "./deleteDealer";

export type {
  DeleteCtsModuleSettingMutationVariables,
  DeleteCtsModuleSettingMutation,
  DeleteCtsModuleSettingMutationFn,
  DeleteCtsModuleSettingMutationHookResult,
  DeleteCtsModuleSettingMutationResult,
  DeleteCtsModuleSettingMutationOptions,
} from "./deleteCtsModuleSetting";

export {
  DeleteCtsModuleSettingDocument,
  useDeleteCtsModuleSettingMutation,
} from "./deleteCtsModuleSetting";

export type {
  DeleteConsentsAndDeclarationsMutationVariables,
  DeleteConsentsAndDeclarationsMutation,
  DeleteConsentsAndDeclarationsMutationFn,
  DeleteConsentsAndDeclarationsMutationHookResult,
  DeleteConsentsAndDeclarationsMutationResult,
  DeleteConsentsAndDeclarationsMutationOptions,
} from "./deleteConsentsAndDeclarations";

export {
  DeleteConsentsAndDeclarationsDocument,
  useDeleteConsentsAndDeclarationsMutation,
} from "./deleteConsentsAndDeclarations";

export type {
  DeleteConfiguratorModuleAssetMutationVariables,
  DeleteConfiguratorModuleAssetMutation,
  DeleteConfiguratorModuleAssetMutationFn,
  DeleteConfiguratorModuleAssetMutationHookResult,
  DeleteConfiguratorModuleAssetMutationResult,
  DeleteConfiguratorModuleAssetMutationOptions,
} from "./deleteConfiguratorModuleAsset";

export {
  DeleteConfiguratorModuleAssetDocument,
  useDeleteConfiguratorModuleAssetMutation,
} from "./deleteConfiguratorModuleAsset";

export type {
  DeleteConfiguratorDescriptionImageMutationVariables,
  DeleteConfiguratorDescriptionImageMutation,
  DeleteConfiguratorDescriptionImageMutationFn,
  DeleteConfiguratorDescriptionImageMutationHookResult,
  DeleteConfiguratorDescriptionImageMutationResult,
  DeleteConfiguratorDescriptionImageMutationOptions,
} from "./deleteConfiguratorDescriptionImage";

export {
  DeleteConfiguratorDescriptionImageDocument,
  useDeleteConfiguratorDescriptionImageMutation,
} from "./deleteConfiguratorDescriptionImage";

export type {
  DeleteCompanyAssetMutationVariables,
  DeleteCompanyAssetMutation,
  DeleteCompanyAssetMutationFn,
  DeleteCompanyAssetMutationHookResult,
  DeleteCompanyAssetMutationResult,
  DeleteCompanyAssetMutationOptions,
} from "./deleteCompanyAsset";

export {
  DeleteCompanyAssetDocument,
  useDeleteCompanyAssetMutation,
} from "./deleteCompanyAsset";

export type {
  DeleteCompanyMutationVariables,
  DeleteCompanyMutation,
  DeleteCompanyMutationFn,
  DeleteCompanyMutationHookResult,
  DeleteCompanyMutationResult,
  DeleteCompanyMutationOptions,
} from "./deleteCompany";

export {
  DeleteCompanyDocument,
  useDeleteCompanyMutation,
} from "./deleteCompany";

export type {
  DeleteBannerImageMutationVariables,
  DeleteBannerImageMutation,
  DeleteBannerImageMutationFn,
  DeleteBannerImageMutationHookResult,
  DeleteBannerImageMutationResult,
  DeleteBannerImageMutationOptions,
} from "./deleteBannerImage";

export {
  DeleteBannerImageDocument,
  useDeleteBannerImageMutation,
} from "./deleteBannerImage";

export type {
  DeleteBannerMutationVariables,
  DeleteBannerMutation,
  DeleteBannerMutationFn,
  DeleteBannerMutationHookResult,
  DeleteBannerMutationResult,
  DeleteBannerMutationOptions,
} from "./deleteBanner";

export { DeleteBannerDocument, useDeleteBannerMutation } from "./deleteBanner";

export type {
  DeleteBankAssetMutationVariables,
  DeleteBankAssetMutation,
  DeleteBankAssetMutationFn,
  DeleteBankAssetMutationHookResult,
  DeleteBankAssetMutationResult,
  DeleteBankAssetMutationOptions,
} from "./deleteBankAsset";

export {
  DeleteBankAssetDocument,
  useDeleteBankAssetMutation,
} from "./deleteBankAsset";

export type {
  DeleteBankMutationVariables,
  DeleteBankMutation,
  DeleteBankMutationFn,
  DeleteBankMutationHookResult,
  DeleteBankMutationResult,
  DeleteBankMutationOptions,
} from "./deleteBank";

export { DeleteBankDocument, useDeleteBankMutation } from "./deleteBank";

export type {
  DeleteAutoplayModuleSettingMutationVariables,
  DeleteAutoplayModuleSettingMutation,
  DeleteAutoplayModuleSettingMutationFn,
  DeleteAutoplayModuleSettingMutationHookResult,
  DeleteAutoplayModuleSettingMutationResult,
  DeleteAutoplayModuleSettingMutationOptions,
} from "./deleteAutoplayModuleSetting";

export {
  DeleteAutoplayModuleSettingDocument,
  useDeleteAutoplayModuleSettingMutation,
} from "./deleteAutoplayModuleSetting";

export type {
  DeleteAppointmentModuleAssetMutationVariables,
  DeleteAppointmentModuleAssetMutation,
  DeleteAppointmentModuleAssetMutationFn,
  DeleteAppointmentModuleAssetMutationHookResult,
  DeleteAppointmentModuleAssetMutationResult,
  DeleteAppointmentModuleAssetMutationOptions,
} from "./deleteAppointmentModuleAsset";

export {
  DeleteAppointmentModuleAssetDocument,
  useDeleteAppointmentModuleAssetMutation,
} from "./deleteAppointmentModuleAsset";

export type {
  DeleteApplicationDocumentMutationVariables,
  DeleteApplicationDocumentMutation,
  DeleteApplicationDocumentMutationFn,
  DeleteApplicationDocumentMutationHookResult,
  DeleteApplicationDocumentMutationResult,
  DeleteApplicationDocumentMutationOptions,
} from "./deleteApplicationDocument";

export {
  DeleteApplicationDocumentDocument,
  useDeleteApplicationDocumentMutation,
} from "./deleteApplicationDocument";

export type {
  DeleteAdyenPaymentSettingsMutationVariables,
  DeleteAdyenPaymentSettingsMutation,
  DeleteAdyenPaymentSettingsMutationFn,
  DeleteAdyenPaymentSettingsMutationHookResult,
  DeleteAdyenPaymentSettingsMutationResult,
  DeleteAdyenPaymentSettingsMutationOptions,
} from "./deleteAdyenPaymentSettings";

export {
  DeleteAdyenPaymentSettingsDocument,
  useDeleteAdyenPaymentSettingsMutation,
} from "./deleteAdyenPaymentSettings";

export type {
  DeleteAdditionalDetailMutationVariables,
  DeleteAdditionalDetailMutation,
  DeleteAdditionalDetailMutationFn,
  DeleteAdditionalDetailMutationHookResult,
  DeleteAdditionalDetailMutationResult,
  DeleteAdditionalDetailMutationOptions,
} from "./deleteAdditionalDetail";

export {
  DeleteAdditionalDetailDocument,
  useDeleteAdditionalDetailMutation,
} from "./deleteAdditionalDetail";

export type {
  DeclineApplicationMutationVariables,
  DeclineApplicationMutation,
  DeclineApplicationMutationFn,
  DeclineApplicationMutationHookResult,
  DeclineApplicationMutationResult,
  DeclineApplicationMutationOptions,
} from "./declineApplication";

export {
  DeclineApplicationDocument,
  useDeclineApplicationMutation,
} from "./declineApplication";

export type {
  CreateWhatsappLiveChatSettingsMutationVariables,
  CreateWhatsappLiveChatSettingsMutation,
  CreateWhatsappLiveChatSettingsMutationFn,
  CreateWhatsappLiveChatSettingsMutationHookResult,
  CreateWhatsappLiveChatSettingsMutationResult,
  CreateWhatsappLiveChatSettingsMutationOptions,
} from "./createWhatsappLiveChatSettings";

export {
  CreateWhatsappLiveChatSettingsDocument,
  useCreateWhatsappLiveChatSettingsMutation,
} from "./createWhatsappLiveChatSettings";

export type {
  CreateWhatsappLiveChatModuleMutationVariables,
  CreateWhatsappLiveChatModuleMutation,
  CreateWhatsappLiveChatModuleMutationFn,
  CreateWhatsappLiveChatModuleMutationHookResult,
  CreateWhatsappLiveChatModuleMutationResult,
  CreateWhatsappLiveChatModuleMutationOptions,
} from "./createWhatsappLiveChatModule";

export {
  CreateWhatsappLiveChatModuleDocument,
  useCreateWhatsappLiveChatModuleMutation,
} from "./createWhatsappLiveChatModule";

export type {
  CreateWebsiteSocialMediaMutationVariables,
  CreateWebsiteSocialMediaMutation,
  CreateWebsiteSocialMediaMutationFn,
  CreateWebsiteSocialMediaMutationHookResult,
  CreateWebsiteSocialMediaMutationResult,
  CreateWebsiteSocialMediaMutationOptions,
} from "./createWebsiteSocialMedia";

export {
  CreateWebsiteSocialMediaDocument,
  useCreateWebsiteSocialMediaMutation,
} from "./createWebsiteSocialMedia";

export type {
  CreateWebsiteModuleMutationVariables,
  CreateWebsiteModuleMutation,
  CreateWebsiteModuleMutationFn,
  CreateWebsiteModuleMutationHookResult,
  CreateWebsiteModuleMutationResult,
  CreateWebsiteModuleMutationOptions,
} from "./createWebsiteModule";

export {
  CreateWebsiteModuleDocument,
  useCreateWebsiteModuleMutation,
} from "./createWebsiteModule";

export type {
  CreateWebPageEndpointMutationVariables,
  CreateWebPageEndpointMutation,
  CreateWebPageEndpointMutationFn,
  CreateWebPageEndpointMutationHookResult,
  CreateWebPageEndpointMutationResult,
  CreateWebPageEndpointMutationOptions,
} from "./createWebPageEndpoint";

export {
  CreateWebPageEndpointDocument,
  useCreateWebPageEndpointMutation,
} from "./createWebPageEndpoint";

export type {
  CreateWebPageMutationVariables,
  CreateWebPageMutation,
  CreateWebPageMutationFn,
  CreateWebPageMutationHookResult,
  CreateWebPageMutationResult,
  CreateWebPageMutationOptions,
} from "./createWebPage";

export {
  CreateWebPageDocument,
  useCreateWebPageMutation,
} from "./createWebPage";

export type {
  CreateWebCalcSettingMutationVariables,
  CreateWebCalcSettingMutation,
  CreateWebCalcSettingMutationFn,
  CreateWebCalcSettingMutationHookResult,
  CreateWebCalcSettingMutationResult,
  CreateWebCalcSettingMutationOptions,
} from "./createWebCalcSetting";

export {
  CreateWebCalcSettingDocument,
  useCreateWebCalcSettingMutation,
} from "./createWebCalcSetting";

export type {
  CreateVisitAppointmentModuleMutationVariables,
  CreateVisitAppointmentModuleMutation,
  CreateVisitAppointmentModuleMutationFn,
  CreateVisitAppointmentModuleMutationHookResult,
  CreateVisitAppointmentModuleMutationResult,
  CreateVisitAppointmentModuleMutationOptions,
} from "./createVisitAppointmentModule";

export {
  CreateVisitAppointmentModuleDocument,
  useCreateVisitAppointmentModuleMutation,
} from "./createVisitAppointmentModule";

export type {
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutationVariables,
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutation,
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutationFn,
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutationHookResult,
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutationResult,
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutationOptions,
} from "./createVehicleDataWithPorscheCodeIntegrationModule";

export {
  CreateVehicleDataWithPorscheCodeIntegrationModuleDocument,
  useCreateVehicleDataWithPorscheCodeIntegrationModuleMutation,
} from "./createVehicleDataWithPorscheCodeIntegrationModule";

export type {
  CreateVariantConfiguratorMutationVariables,
  CreateVariantConfiguratorMutation,
  CreateVariantConfiguratorMutationFn,
  CreateVariantConfiguratorMutationHookResult,
  CreateVariantConfiguratorMutationResult,
  CreateVariantConfiguratorMutationOptions,
} from "./createVariantConfigurator";

export {
  CreateVariantConfiguratorDocument,
  useCreateVariantConfiguratorMutation,
} from "./createVariantConfigurator";

export type {
  CreateVariantMutationVariables,
  CreateVariantMutation,
  CreateVariantMutationFn,
  CreateVariantMutationHookResult,
  CreateVariantMutationResult,
  CreateVariantMutationOptions,
} from "./createVariant";

export {
  CreateVariantDocument,
  useCreateVariantMutation,
} from "./createVariant";

export type {
  CreateUserlikeChatbotSettingsMutationVariables,
  CreateUserlikeChatbotSettingsMutation,
  CreateUserlikeChatbotSettingsMutationFn,
  CreateUserlikeChatbotSettingsMutationHookResult,
  CreateUserlikeChatbotSettingsMutationResult,
  CreateUserlikeChatbotSettingsMutationOptions,
} from "./createUserlikeChatbotSettings";

export {
  CreateUserlikeChatbotSettingsDocument,
  useCreateUserlikeChatbotSettingsMutation,
} from "./createUserlikeChatbotSettings";

export type {
  CreateUserlikeChatbotModuleMutationVariables,
  CreateUserlikeChatbotModuleMutation,
  CreateUserlikeChatbotModuleMutationFn,
  CreateUserlikeChatbotModuleMutationHookResult,
  CreateUserlikeChatbotModuleMutationResult,
  CreateUserlikeChatbotModuleMutationOptions,
} from "./createUserlikeChatbotModule";

export {
  CreateUserlikeChatbotModuleDocument,
  useCreateUserlikeChatbotModuleMutation,
} from "./createUserlikeChatbotModule";

export type {
  CreateUserGroupMutationVariables,
  CreateUserGroupMutation,
  CreateUserGroupMutationFn,
  CreateUserGroupMutationHookResult,
  CreateUserGroupMutationResult,
  CreateUserGroupMutationOptions,
} from "./createUserGroup";

export {
  CreateUserGroupDocument,
  useCreateUserGroupMutation,
} from "./createUserGroup";

export type {
  CreateUserMutationVariables,
  CreateUserMutation,
  CreateUserMutationFn,
  CreateUserMutationHookResult,
  CreateUserMutationResult,
  CreateUserMutationOptions,
} from "./createUser";

export { CreateUserDocument, useCreateUserMutation } from "./createUser";

export type {
  CreateTtbPaymentSettingsMutationVariables,
  CreateTtbPaymentSettingsMutation,
  CreateTtbPaymentSettingsMutationFn,
  CreateTtbPaymentSettingsMutationHookResult,
  CreateTtbPaymentSettingsMutationResult,
  CreateTtbPaymentSettingsMutationOptions,
} from "./createTtbPaymentSettings";

export {
  CreateTtbPaymentSettingsDocument,
  useCreateTtbPaymentSettingsMutation,
} from "./createTtbPaymentSettings";

export type {
  CreateTtbPaymentModuleMutationVariables,
  CreateTtbPaymentModuleMutation,
  CreateTtbPaymentModuleMutationFn,
  CreateTtbPaymentModuleMutationHookResult,
  CreateTtbPaymentModuleMutationResult,
  CreateTtbPaymentModuleMutationOptions,
} from "./createTtbPaymentModule";

export {
  CreateTtbPaymentModuleDocument,
  useCreateTtbPaymentModuleMutation,
} from "./createTtbPaymentModule";

export type {
  CreateTrimBlockMutationVariables,
  CreateTrimBlockMutation,
  CreateTrimBlockMutationFn,
  CreateTrimBlockMutationHookResult,
  CreateTrimBlockMutationResult,
  CreateTrimBlockMutationOptions,
} from "./createTrimBlock";

export {
  CreateTrimBlockDocument,
  useCreateTrimBlockMutation,
} from "./createTrimBlock";

export type {
  CreateTradeInModuleMutationVariables,
  CreateTradeInModuleMutation,
  CreateTradeInModuleMutationFn,
  CreateTradeInModuleMutationHookResult,
  CreateTradeInModuleMutationResult,
  CreateTradeInModuleMutationOptions,
} from "./createTradeInModule";

export {
  CreateTradeInModuleDocument,
  useCreateTradeInModuleMutation,
} from "./createTradeInModule";

export type {
  CreateTextConsentsAndDeclarationsMutationVariables,
  CreateTextConsentsAndDeclarationsMutation,
  CreateTextConsentsAndDeclarationsMutationFn,
  CreateTextConsentsAndDeclarationsMutationHookResult,
  CreateTextConsentsAndDeclarationsMutationResult,
  CreateTextConsentsAndDeclarationsMutationOptions,
} from "./createTextConsentsAndDeclarations";

export {
  CreateTextConsentsAndDeclarationsDocument,
  useCreateTextConsentsAndDeclarationsMutation,
} from "./createTextConsentsAndDeclarations";

export type {
  CreateSubmodelMutationVariables,
  CreateSubmodelMutation,
  CreateSubmodelMutationFn,
  CreateSubmodelMutationHookResult,
  CreateSubmodelMutationResult,
  CreateSubmodelMutationOptions,
} from "./createSubmodel";

export {
  CreateSubmodelDocument,
  useCreateSubmodelMutation,
} from "./createSubmodel";

export type {
  CreateStandardApplicationPublicAccessEntrypointMutationVariables,
  CreateStandardApplicationPublicAccessEntrypointMutation,
  CreateStandardApplicationPublicAccessEntrypointMutationFn,
  CreateStandardApplicationPublicAccessEntrypointMutationHookResult,
  CreateStandardApplicationPublicAccessEntrypointMutationResult,
  CreateStandardApplicationPublicAccessEntrypointMutationOptions,
} from "./createStandardApplicationPublicAccessEntrypoint";

export {
  CreateStandardApplicationPublicAccessEntrypointDocument,
  useCreateStandardApplicationPublicAccessEntrypointMutation,
} from "./createStandardApplicationPublicAccessEntrypoint";

export type {
  CreateStandardApplicationModuleMutationVariables,
  CreateStandardApplicationModuleMutation,
  CreateStandardApplicationModuleMutationFn,
  CreateStandardApplicationModuleMutationHookResult,
  CreateStandardApplicationModuleMutationResult,
  CreateStandardApplicationModuleMutationOptions,
} from "./createStandardApplicationModule";

export {
  CreateStandardApplicationModuleDocument,
  useCreateStandardApplicationModuleMutation,
} from "./createStandardApplicationModule";

export type {
  CreateStandardApplicationEntrypointMutationVariables,
  CreateStandardApplicationEntrypointMutation,
  CreateStandardApplicationEntrypointMutationFn,
  CreateStandardApplicationEntrypointMutationHookResult,
  CreateStandardApplicationEntrypointMutationResult,
  CreateStandardApplicationEntrypointMutationOptions,
} from "./createStandardApplicationEntrypoint";

export {
  CreateStandardApplicationEntrypointDocument,
  useCreateStandardApplicationEntrypointMutation,
} from "./createStandardApplicationEntrypoint";

export type {
  CreateSimpleVehicleManagementModuleMutationVariables,
  CreateSimpleVehicleManagementModuleMutation,
  CreateSimpleVehicleManagementModuleMutationFn,
  CreateSimpleVehicleManagementModuleMutationHookResult,
  CreateSimpleVehicleManagementModuleMutationResult,
  CreateSimpleVehicleManagementModuleMutationOptions,
} from "./createSimpleVehicleManagementModule";

export {
  CreateSimpleVehicleManagementModuleDocument,
  useCreateSimpleVehicleManagementModuleMutation,
} from "./createSimpleVehicleManagementModule";

export type {
  CreateShowroomVisitAppointmentFromLeadMutationVariables,
  CreateShowroomVisitAppointmentFromLeadMutation,
  CreateShowroomVisitAppointmentFromLeadMutationFn,
  CreateShowroomVisitAppointmentFromLeadMutationHookResult,
  CreateShowroomVisitAppointmentFromLeadMutationResult,
  CreateShowroomVisitAppointmentFromLeadMutationOptions,
} from "./createShowroomVisitAppointmentFromLead";

export {
  CreateShowroomVisitAppointmentFromLeadDocument,
  useCreateShowroomVisitAppointmentFromLeadMutation,
} from "./createShowroomVisitAppointmentFromLead";

export type {
  CreateSalesOfferModuleMutationVariables,
  CreateSalesOfferModuleMutation,
  CreateSalesOfferModuleMutationFn,
  CreateSalesOfferModuleMutationHookResult,
  CreateSalesOfferModuleMutationResult,
  CreateSalesOfferModuleMutationOptions,
} from "./createSalesOfferModule";

export {
  CreateSalesOfferModuleDocument,
  useCreateSalesOfferModuleMutation,
} from "./createSalesOfferModule";

export type {
  CreateSalesOfferMutationVariables,
  CreateSalesOfferMutation,
  CreateSalesOfferMutationFn,
  CreateSalesOfferMutationHookResult,
  CreateSalesOfferMutationResult,
  CreateSalesOfferMutationOptions,
} from "./createSalesOffer";

export {
  CreateSalesOfferDocument,
  useCreateSalesOfferMutation,
} from "./createSalesOffer";

export type {
  CreateSalesControlBoardModuleMutationVariables,
  CreateSalesControlBoardModuleMutation,
  CreateSalesControlBoardModuleMutationFn,
  CreateSalesControlBoardModuleMutationHookResult,
  CreateSalesControlBoardModuleMutationResult,
  CreateSalesControlBoardModuleMutationOptions,
} from "./createSalesControlBoardModule";

export {
  CreateSalesControlBoardModuleDocument,
  useCreateSalesControlBoardModuleMutation,
} from "./createSalesControlBoardModule";

export type {
  CreateRouterMenuMutationVariables,
  CreateRouterMenuMutation,
  CreateRouterMenuMutationFn,
  CreateRouterMenuMutationHookResult,
  CreateRouterMenuMutationResult,
  CreateRouterMenuMutationOptions,
} from "./createRouterMenu";

export {
  CreateRouterMenuDocument,
  useCreateRouterMenuMutation,
} from "./createRouterMenu";

export type {
  CreateRouterMutationVariables,
  CreateRouterMutation,
  CreateRouterMutationFn,
  CreateRouterMutationHookResult,
  CreateRouterMutationResult,
  CreateRouterMutationOptions,
} from "./createRouter";

export { CreateRouterDocument, useCreateRouterMutation } from "./createRouter";

export type {
  CreateRoleMutationVariables,
  CreateRoleMutation,
  CreateRoleMutationFn,
  CreateRoleMutationHookResult,
  CreateRoleMutationResult,
  CreateRoleMutationOptions,
} from "./createRole";

export { CreateRoleDocument, useCreateRoleMutation } from "./createRole";

export type {
  CreatePromoCodeModuleMutationVariables,
  CreatePromoCodeModuleMutation,
  CreatePromoCodeModuleMutationFn,
  CreatePromoCodeModuleMutationHookResult,
  CreatePromoCodeModuleMutationResult,
  CreatePromoCodeModuleMutationOptions,
} from "./createPromoCodeModule";

export {
  CreatePromoCodeModuleDocument,
  useCreatePromoCodeModuleMutation,
} from "./createPromoCodeModule";

export type {
  CreatePromoCodeMutationVariables,
  CreatePromoCodeMutation,
  CreatePromoCodeMutationFn,
  CreatePromoCodeMutationHookResult,
  CreatePromoCodeMutationResult,
  CreatePromoCodeMutationOptions,
} from "./createPromoCode";

export {
  CreatePromoCodeDocument,
  useCreatePromoCodeMutation,
} from "./createPromoCode";

export type {
  CreatePorscheRetainModuleMutationVariables,
  CreatePorscheRetainModuleMutation,
  CreatePorscheRetainModuleMutationFn,
  CreatePorscheRetainModuleMutationHookResult,
  CreatePorscheRetainModuleMutationResult,
  CreatePorscheRetainModuleMutationOptions,
} from "./createPorscheRetainModule";

export {
  CreatePorscheRetainModuleDocument,
  useCreatePorscheRetainModuleMutation,
} from "./createPorscheRetainModule";

export type {
  CreatePorschePaymentSettingsMutationVariables,
  CreatePorschePaymentSettingsMutation,
  CreatePorschePaymentSettingsMutationFn,
  CreatePorschePaymentSettingsMutationHookResult,
  CreatePorschePaymentSettingsMutationResult,
  CreatePorschePaymentSettingsMutationOptions,
} from "./createPorschePaymentSettings";

export {
  CreatePorschePaymentSettingsDocument,
  useCreatePorschePaymentSettingsMutation,
} from "./createPorschePaymentSettings";

export type {
  CreatePorschePaymentModuleMutationVariables,
  CreatePorschePaymentModuleMutation,
  CreatePorschePaymentModuleMutationFn,
  CreatePorschePaymentModuleMutationHookResult,
  CreatePorschePaymentModuleMutationResult,
  CreatePorschePaymentModuleMutationOptions,
} from "./createPorschePaymentModule";

export {
  CreatePorschePaymentModuleDocument,
  useCreatePorschePaymentModuleMutation,
} from "./createPorschePaymentModule";

export type {
  CreatePorscheMasterDataModuleMutationVariables,
  CreatePorscheMasterDataModuleMutation,
  CreatePorscheMasterDataModuleMutationFn,
  CreatePorscheMasterDataModuleMutationHookResult,
  CreatePorscheMasterDataModuleMutationResult,
  CreatePorscheMasterDataModuleMutationOptions,
} from "./createPorscheMasterDataModule";

export {
  CreatePorscheMasterDataModuleDocument,
  useCreatePorscheMasterDataModuleMutation,
} from "./createPorscheMasterDataModule";

export type {
  CreatePorscheIdModuleMutationVariables,
  CreatePorscheIdModuleMutation,
  CreatePorscheIdModuleMutationFn,
  CreatePorscheIdModuleMutationHookResult,
  CreatePorscheIdModuleMutationResult,
  CreatePorscheIdModuleMutationOptions,
} from "./createPorscheIdModule";

export {
  CreatePorscheIdModuleDocument,
  useCreatePorscheIdModuleMutation,
} from "./createPorscheIdModule";

export type {
  CreatePayGatePaymentSettingsMutationVariables,
  CreatePayGatePaymentSettingsMutation,
  CreatePayGatePaymentSettingsMutationFn,
  CreatePayGatePaymentSettingsMutationHookResult,
  CreatePayGatePaymentSettingsMutationResult,
  CreatePayGatePaymentSettingsMutationOptions,
} from "./createPayGatePaymentSettings";

export {
  CreatePayGatePaymentSettingsDocument,
  useCreatePayGatePaymentSettingsMutation,
} from "./createPayGatePaymentSettings";

export type {
  CreatePayGatePaymentModuleMutationVariables,
  CreatePayGatePaymentModuleMutation,
  CreatePayGatePaymentModuleMutationFn,
  CreatePayGatePaymentModuleMutationHookResult,
  CreatePayGatePaymentModuleMutationResult,
  CreatePayGatePaymentModuleMutationOptions,
} from "./createPayGatePaymentModule";

export {
  CreatePayGatePaymentModuleDocument,
  useCreatePayGatePaymentModuleMutation,
} from "./createPayGatePaymentModule";

export type {
  CreatePackageSettingMutationVariables,
  CreatePackageSettingMutation,
  CreatePackageSettingMutationFn,
  CreatePackageSettingMutationHookResult,
  CreatePackageSettingMutationResult,
  CreatePackageSettingMutationOptions,
} from "./createPackageSetting";

export {
  CreatePackageSettingDocument,
  useCreatePackageSettingMutation,
} from "./createPackageSetting";

export type {
  CreatePackageBlockMutationVariables,
  CreatePackageBlockMutation,
  CreatePackageBlockMutationFn,
  CreatePackageBlockMutationHookResult,
  CreatePackageBlockMutationResult,
  CreatePackageBlockMutationOptions,
} from "./createPackageBlock";

export {
  CreatePackageBlockDocument,
  useCreatePackageBlockMutation,
} from "./createPackageBlock";

export type {
  CreateOptionsBlockMutationVariables,
  CreateOptionsBlockMutation,
  CreateOptionsBlockMutationFn,
  CreateOptionsBlockMutationHookResult,
  CreateOptionsBlockMutationResult,
  CreateOptionsBlockMutationOptions,
} from "./createOptionsBlock";

export {
  CreateOptionsBlockDocument,
  useCreateOptionsBlockMutation,
} from "./createOptionsBlock";

export type {
  CreateOidcModuleMutationVariables,
  CreateOidcModuleMutation,
  CreateOidcModuleMutationFn,
  CreateOidcModuleMutationHookResult,
  CreateOidcModuleMutationResult,
  CreateOidcModuleMutationOptions,
} from "./createOIDCModule";

export {
  CreateOidcModuleDocument,
  useCreateOidcModuleMutation,
} from "./createOIDCModule";

export type {
  CreateNewContactFromBpMutationVariables,
  CreateNewContactFromBpMutation,
  CreateNewContactFromBpMutationFn,
  CreateNewContactFromBpMutationHookResult,
  CreateNewContactFromBpMutationResult,
  CreateNewContactFromBpMutationOptions,
} from "./createNewContactFromBp";

export {
  CreateNewContactFromBpDocument,
  useCreateNewContactFromBpMutation,
} from "./createNewContactFromBp";

export type {
  CreateNamirialSigningModuleMutationVariables,
  CreateNamirialSigningModuleMutation,
  CreateNamirialSigningModuleMutationFn,
  CreateNamirialSigningModuleMutationHookResult,
  CreateNamirialSigningModuleMutationResult,
  CreateNamirialSigningModuleMutationOptions,
} from "./createNamirialSigningModule";

export {
  CreateNamirialSigningModuleDocument,
  useCreateNamirialSigningModuleMutation,
} from "./createNamirialSigningModule";

export type {
  CreateMyInfoSettingMutationVariables,
  CreateMyInfoSettingMutation,
  CreateMyInfoSettingMutationFn,
  CreateMyInfoSettingMutationHookResult,
  CreateMyInfoSettingMutationResult,
  CreateMyInfoSettingMutationOptions,
} from "./createMyInfoSetting";

export {
  CreateMyInfoSettingDocument,
  useCreateMyInfoSettingMutation,
} from "./createMyInfoSetting";

export type {
  CreateMyInfoModuleMutationVariables,
  CreateMyInfoModuleMutation,
  CreateMyInfoModuleMutationFn,
  CreateMyInfoModuleMutationHookResult,
  CreateMyInfoModuleMutationResult,
  CreateMyInfoModuleMutationOptions,
} from "./createMyInfoModule";

export {
  CreateMyInfoModuleDocument,
  useCreateMyInfoModuleMutation,
} from "./createMyInfoModule";

export type {
  CreateModelConfiguratorMutationVariables,
  CreateModelConfiguratorMutation,
  CreateModelConfiguratorMutationFn,
  CreateModelConfiguratorMutationHookResult,
  CreateModelConfiguratorMutationResult,
  CreateModelConfiguratorMutationOptions,
} from "./createModelConfigurator";

export {
  CreateModelConfiguratorDocument,
  useCreateModelConfiguratorMutation,
} from "./createModelConfigurator";

export type {
  CreateModelMutationVariables,
  CreateModelMutation,
  CreateModelMutationFn,
  CreateModelMutationHookResult,
  CreateModelMutationResult,
  CreateModelMutationOptions,
} from "./createModel";

export { CreateModelDocument, useCreateModelMutation } from "./createModel";

export type {
  CreateMobilityModuleMutationVariables,
  CreateMobilityModuleMutation,
  CreateMobilityModuleMutationFn,
  CreateMobilityModuleMutationHookResult,
  CreateMobilityModuleMutationResult,
  CreateMobilityModuleMutationOptions,
} from "./createMobilityModule";

export {
  CreateMobilityModuleDocument,
  useCreateMobilityModuleMutation,
} from "./createMobilityModule";

export type {
  CreateMobilityApplicationEntrypointMutationVariables,
  CreateMobilityApplicationEntrypointMutation,
  CreateMobilityApplicationEntrypointMutationFn,
  CreateMobilityApplicationEntrypointMutationHookResult,
  CreateMobilityApplicationEntrypointMutationResult,
  CreateMobilityApplicationEntrypointMutationOptions,
} from "./createMobilityApplicationEntrypoint";

export {
  CreateMobilityApplicationEntrypointDocument,
  useCreateMobilityApplicationEntrypointMutation,
} from "./createMobilityApplicationEntrypoint";

export type {
  CreateMobilityAddonMutationVariables,
  CreateMobilityAddonMutation,
  CreateMobilityAddonMutationFn,
  CreateMobilityAddonMutationHookResult,
  CreateMobilityAddonMutationResult,
  CreateMobilityAddonMutationOptions,
} from "./createMobilityAddon";

export {
  CreateMobilityAddonDocument,
  useCreateMobilityAddonMutation,
} from "./createMobilityAddon";

export type {
  CreateMobilityAdditionalInfoMutationVariables,
  CreateMobilityAdditionalInfoMutation,
  CreateMobilityAdditionalInfoMutationFn,
  CreateMobilityAdditionalInfoMutationHookResult,
  CreateMobilityAdditionalInfoMutationResult,
  CreateMobilityAdditionalInfoMutationOptions,
} from "./createMobilityAdditionalInfo";

export {
  CreateMobilityAdditionalInfoDocument,
  useCreateMobilityAdditionalInfoMutation,
} from "./createMobilityAdditionalInfo";

export type {
  CreateMarketingModuleMutationVariables,
  CreateMarketingModuleMutation,
  CreateMarketingModuleMutationFn,
  CreateMarketingModuleMutationHookResult,
  CreateMarketingModuleMutationResult,
  CreateMarketingModuleMutationOptions,
} from "./createMarketingModule";

export {
  CreateMarketingModuleDocument,
  useCreateMarketingModuleMutation,
} from "./createMarketingModule";

export type {
  CreateMarketingConsentsAndDeclarationsMutationVariables,
  CreateMarketingConsentsAndDeclarationsMutation,
  CreateMarketingConsentsAndDeclarationsMutationFn,
  CreateMarketingConsentsAndDeclarationsMutationHookResult,
  CreateMarketingConsentsAndDeclarationsMutationResult,
  CreateMarketingConsentsAndDeclarationsMutationOptions,
} from "./createMarketingConsentsAndDeclarations";

export {
  CreateMarketingConsentsAndDeclarationsDocument,
  useCreateMarketingConsentsAndDeclarationsMutation,
} from "./createMarketingConsentsAndDeclarations";

export type {
  CreateMakeMutationVariables,
  CreateMakeMutation,
  CreateMakeMutationFn,
  CreateMakeMutationHookResult,
  CreateMakeMutationResult,
  CreateMakeMutationOptions,
} from "./createMake";

export { CreateMakeDocument, useCreateMakeMutation } from "./createMake";

export type {
  CreateMaintenanceModuleMutationVariables,
  CreateMaintenanceModuleMutation,
  CreateMaintenanceModuleMutationFn,
  CreateMaintenanceModuleMutationHookResult,
  CreateMaintenanceModuleMutationResult,
  CreateMaintenanceModuleMutationOptions,
} from "./createMaintenanceModule";

export {
  CreateMaintenanceModuleDocument,
  useCreateMaintenanceModuleMutation,
} from "./createMaintenanceModule";

export type {
  CreateLocalCustomerManagementMutationVariables,
  CreateLocalCustomerManagementMutation,
  CreateLocalCustomerManagementMutationFn,
  CreateLocalCustomerManagementMutationHookResult,
  CreateLocalCustomerManagementMutationResult,
  CreateLocalCustomerManagementMutationOptions,
} from "./createLocalCustomerManagement";

export {
  CreateLocalCustomerManagementDocument,
  useCreateLocalCustomerManagementMutation,
} from "./createLocalCustomerManagement";

export type {
  CreateLeadListEndpointMutationVariables,
  CreateLeadListEndpointMutation,
  CreateLeadListEndpointMutationFn,
  CreateLeadListEndpointMutationHookResult,
  CreateLeadListEndpointMutationResult,
  CreateLeadListEndpointMutationOptions,
} from "./createLeadListEndpoint";

export {
  CreateLeadListEndpointDocument,
  useCreateLeadListEndpointMutation,
} from "./createLeadListEndpoint";

export type {
  CreateLeadFollowUpMutationVariables,
  CreateLeadFollowUpMutation,
  CreateLeadFollowUpMutationFn,
  CreateLeadFollowUpMutationHookResult,
  CreateLeadFollowUpMutationResult,
  CreateLeadFollowUpMutationOptions,
} from "./createLeadFollowUp";

export {
  CreateLeadFollowUpDocument,
  useCreateLeadFollowUpMutation,
} from "./createLeadFollowUp";

export type {
  CreateLaunchPadModuleMutationVariables,
  CreateLaunchPadModuleMutation,
  CreateLaunchPadModuleMutationFn,
  CreateLaunchPadModuleMutationHookResult,
  CreateLaunchPadModuleMutationResult,
  CreateLaunchPadModuleMutationOptions,
} from "./createLaunchpadModule";

export {
  CreateLaunchPadModuleDocument,
  useCreateLaunchPadModuleMutation,
} from "./createLaunchpadModule";

export type {
  CreateLaunchPadApplicationEntrypointMutationVariables,
  CreateLaunchPadApplicationEntrypointMutation,
  CreateLaunchPadApplicationEntrypointMutationFn,
  CreateLaunchPadApplicationEntrypointMutationHookResult,
  CreateLaunchPadApplicationEntrypointMutationResult,
  CreateLaunchPadApplicationEntrypointMutationOptions,
} from "./createLaunchPadApplicationEntrypoint";

export {
  CreateLaunchPadApplicationEntrypointDocument,
  useCreateLaunchPadApplicationEntrypointMutation,
} from "./createLaunchPadApplicationEntrypoint";

export type {
  CreateLanguagePackMutationVariables,
  CreateLanguagePackMutation,
  CreateLanguagePackMutationFn,
  CreateLanguagePackMutationHookResult,
  CreateLanguagePackMutationResult,
  CreateLanguagePackMutationOptions,
} from "./createLanguagePack";

export {
  CreateLanguagePackDocument,
  useCreateLanguagePackMutation,
} from "./createLanguagePack";

export type {
  CreateLabelsModuleMutationVariables,
  CreateLabelsModuleMutation,
  CreateLabelsModuleMutationFn,
  CreateLabelsModuleMutationHookResult,
  CreateLabelsModuleMutationResult,
  CreateLabelsModuleMutationOptions,
} from "./createLabelsModule";

export {
  CreateLabelsModuleDocument,
  useCreateLabelsModuleMutation,
} from "./createLabelsModule";

export type {
  CreateLabelsMutationVariables,
  CreateLabelsMutation,
  CreateLabelsMutationFn,
  CreateLabelsMutationHookResult,
  CreateLabelsMutationResult,
  CreateLabelsMutationOptions,
} from "./createLabels";

export { CreateLabelsDocument, useCreateLabelsMutation } from "./createLabels";

export type {
  CreateKycPresetMutationVariables,
  CreateKycPresetMutation,
  CreateKycPresetMutationFn,
  CreateKycPresetMutationHookResult,
  CreateKycPresetMutationResult,
  CreateKycPresetMutationOptions,
} from "./createKYCPreset";

export {
  CreateKycPresetDocument,
  useCreateKycPresetMutation,
} from "./createKYCPreset";

export type {
  CreateInventoryMutationVariables,
  CreateInventoryMutation,
  CreateInventoryMutationFn,
  CreateInventoryMutationHookResult,
  CreateInventoryMutationResult,
  CreateInventoryMutationOptions,
} from "./createInventory";

export {
  CreateInventoryDocument,
  useCreateInventoryMutation,
} from "./createInventory";

export type {
  CreateInsurerMutationVariables,
  CreateInsurerMutation,
  CreateInsurerMutationFn,
  CreateInsurerMutationHookResult,
  CreateInsurerMutationResult,
  CreateInsurerMutationOptions,
} from "./createInsurer";

export {
  CreateInsurerDocument,
  useCreateInsurerMutation,
} from "./createInsurer";

export type {
  CreateInsuranceProductMutationVariables,
  CreateInsuranceProductMutation,
  CreateInsuranceProductMutationFn,
  CreateInsuranceProductMutationHookResult,
  CreateInsuranceProductMutationResult,
  CreateInsuranceProductMutationOptions,
} from "./createInsuranceProduct";

export {
  CreateInsuranceProductDocument,
  useCreateInsuranceProductMutation,
} from "./createInsuranceProduct";

export type {
  CreateInsuranceModuleMutationVariables,
  CreateInsuranceModuleMutation,
  CreateInsuranceModuleMutationFn,
  CreateInsuranceModuleMutationHookResult,
  CreateInsuranceModuleMutationResult,
  CreateInsuranceModuleMutationOptions,
} from "./createInsuranceModule";

export {
  CreateInsuranceModuleDocument,
  useCreateInsuranceModuleMutation,
} from "./createInsuranceModule";

export type {
  CreateGroupConsentsAndDeclarationsMutationVariables,
  CreateGroupConsentsAndDeclarationsMutation,
  CreateGroupConsentsAndDeclarationsMutationFn,
  CreateGroupConsentsAndDeclarationsMutationHookResult,
  CreateGroupConsentsAndDeclarationsMutationResult,
  CreateGroupConsentsAndDeclarationsMutationOptions,
} from "./createGroupConsentsAndDeclarations";

export {
  CreateGroupConsentsAndDeclarationsDocument,
  useCreateGroupConsentsAndDeclarationsMutation,
} from "./createGroupConsentsAndDeclarations";

export type {
  CreateGiftVoucherModuleMutationVariables,
  CreateGiftVoucherModuleMutation,
  CreateGiftVoucherModuleMutationFn,
  CreateGiftVoucherModuleMutationHookResult,
  CreateGiftVoucherModuleMutationResult,
  CreateGiftVoucherModuleMutationOptions,
} from "./createGiftVoucherModule";

export {
  CreateGiftVoucherModuleDocument,
  useCreateGiftVoucherModuleMutation,
} from "./createGiftVoucherModule";

export type {
  CreateFiservPaymentSettingsMutationVariables,
  CreateFiservPaymentSettingsMutation,
  CreateFiservPaymentSettingsMutationFn,
  CreateFiservPaymentSettingsMutationHookResult,
  CreateFiservPaymentSettingsMutationResult,
  CreateFiservPaymentSettingsMutationOptions,
} from "./createFiservPaymentSettings";

export {
  CreateFiservPaymentSettingsDocument,
  useCreateFiservPaymentSettingsMutation,
} from "./createFiservPaymentSettings";

export type {
  CreateFiservPaymentModuleMutationVariables,
  CreateFiservPaymentModuleMutation,
  CreateFiservPaymentModuleMutationFn,
  CreateFiservPaymentModuleMutationHookResult,
  CreateFiservPaymentModuleMutationResult,
  CreateFiservPaymentModuleMutationOptions,
} from "./createFiservPaymentModule";

export {
  CreateFiservPaymentModuleDocument,
  useCreateFiservPaymentModuleMutation,
} from "./createFiservPaymentModule";

export type {
  CreateFinderVehicleManagementModuleMutationVariables,
  CreateFinderVehicleManagementModuleMutation,
  CreateFinderVehicleManagementModuleMutationFn,
  CreateFinderVehicleManagementModuleMutationHookResult,
  CreateFinderVehicleManagementModuleMutationResult,
  CreateFinderVehicleManagementModuleMutationOptions,
} from "./createFinderVehicleManagementModule";

export {
  CreateFinderVehicleManagementModuleDocument,
  useCreateFinderVehicleManagementModuleMutation,
} from "./createFinderVehicleManagementModule";

export type {
  CreateFinderApplicationPublicAccessEntrypointMutationVariables,
  CreateFinderApplicationPublicAccessEntrypointMutation,
  CreateFinderApplicationPublicAccessEntrypointMutationFn,
  CreateFinderApplicationPublicAccessEntrypointMutationHookResult,
  CreateFinderApplicationPublicAccessEntrypointMutationResult,
  CreateFinderApplicationPublicAccessEntrypointMutationOptions,
} from "./createFinderApplicationPublicAccessEntrypoint";

export {
  CreateFinderApplicationPublicAccessEntrypointDocument,
  useCreateFinderApplicationPublicAccessEntrypointMutation,
} from "./createFinderApplicationPublicAccessEntrypoint";

export type {
  CreateFinderApplicationPrivateModuleMutationVariables,
  CreateFinderApplicationPrivateModuleMutation,
  CreateFinderApplicationPrivateModuleMutationFn,
  CreateFinderApplicationPrivateModuleMutationHookResult,
  CreateFinderApplicationPrivateModuleMutationResult,
  CreateFinderApplicationPrivateModuleMutationOptions,
} from "./createFinderApplicationPrivateModule";

export {
  CreateFinderApplicationPrivateModuleDocument,
  useCreateFinderApplicationPrivateModuleMutation,
} from "./createFinderApplicationPrivateModule";

export type {
  CreateFinderApplicationPublicModuleMutationVariables,
  CreateFinderApplicationPublicModuleMutation,
  CreateFinderApplicationPublicModuleMutationFn,
  CreateFinderApplicationPublicModuleMutationHookResult,
  CreateFinderApplicationPublicModuleMutationResult,
  CreateFinderApplicationPublicModuleMutationOptions,
} from "./createFinderApplicationModule";

export {
  CreateFinderApplicationPublicModuleDocument,
  useCreateFinderApplicationPublicModuleMutation,
} from "./createFinderApplicationModule";

export type {
  CreateFinderApplicationEntrypointMutationVariables,
  CreateFinderApplicationEntrypointMutation,
  CreateFinderApplicationEntrypointMutationFn,
  CreateFinderApplicationEntrypointMutationHookResult,
  CreateFinderApplicationEntrypointMutationResult,
  CreateFinderApplicationEntrypointMutationOptions,
} from "./createFinderApplicationEntrypoint";

export {
  CreateFinderApplicationEntrypointDocument,
  useCreateFinderApplicationEntrypointMutation,
} from "./createFinderApplicationEntrypoint";

export type {
  CreateFinanceProductMutationVariables,
  CreateFinanceProductMutation,
  CreateFinanceProductMutationFn,
  CreateFinanceProductMutationHookResult,
  CreateFinanceProductMutationResult,
  CreateFinanceProductMutationOptions,
} from "./createFinanceProduct";

export {
  CreateFinanceProductDocument,
  useCreateFinanceProductMutation,
} from "./createFinanceProduct";

export type {
  CreateEventApplicationModuleMutationVariables,
  CreateEventApplicationModuleMutation,
  CreateEventApplicationModuleMutationFn,
  CreateEventApplicationModuleMutationHookResult,
  CreateEventApplicationModuleMutationResult,
  CreateEventApplicationModuleMutationOptions,
} from "./createEventApplicationModule";

export {
  CreateEventApplicationModuleDocument,
  useCreateEventApplicationModuleMutation,
} from "./createEventApplicationModule";

export type {
  CreateEventApplicationEntrypointMutationVariables,
  CreateEventApplicationEntrypointMutation,
  CreateEventApplicationEntrypointMutationFn,
  CreateEventApplicationEntrypointMutationHookResult,
  CreateEventApplicationEntrypointMutationResult,
  CreateEventApplicationEntrypointMutationOptions,
} from "./createEventApplicationEntrypoint";

export {
  CreateEventApplicationEntrypointDocument,
  useCreateEventApplicationEntrypointMutation,
} from "./createEventApplicationEntrypoint";

export type {
  CreateEventMutationVariables,
  CreateEventMutation,
  CreateEventMutationFn,
  CreateEventMutationHookResult,
  CreateEventMutationResult,
  CreateEventMutationOptions,
} from "./createEvent";

export { CreateEventDocument, useCreateEventMutation } from "./createEvent";

export type {
  CreateEdmEmailSocialMediaMutationVariables,
  CreateEdmEmailSocialMediaMutation,
  CreateEdmEmailSocialMediaMutationFn,
  CreateEdmEmailSocialMediaMutationHookResult,
  CreateEdmEmailSocialMediaMutationResult,
  CreateEdmEmailSocialMediaMutationOptions,
} from "./createEdmEmailSocialMedia";

export {
  CreateEdmEmailSocialMediaDocument,
  useCreateEdmEmailSocialMediaMutation,
} from "./createEdmEmailSocialMedia";

export type {
  CreateDummyWelcomePageEndpointMutationVariables,
  CreateDummyWelcomePageEndpointMutation,
  CreateDummyWelcomePageEndpointMutationFn,
  CreateDummyWelcomePageEndpointMutationHookResult,
  CreateDummyWelcomePageEndpointMutationResult,
  CreateDummyWelcomePageEndpointMutationOptions,
} from "./createDummyWelcomePageEndpoint";

export {
  CreateDummyWelcomePageEndpointDocument,
  useCreateDummyWelcomePageEndpointMutation,
} from "./createDummyWelcomePageEndpoint";

export type {
  CreateDummyPrivatePageEndpointMutationVariables,
  CreateDummyPrivatePageEndpointMutation,
  CreateDummyPrivatePageEndpointMutationFn,
  CreateDummyPrivatePageEndpointMutationHookResult,
  CreateDummyPrivatePageEndpointMutationResult,
  CreateDummyPrivatePageEndpointMutationOptions,
} from "./createDummyPrivatePageEndpoint";

export {
  CreateDummyPrivatePageEndpointDocument,
  useCreateDummyPrivatePageEndpointMutation,
} from "./createDummyPrivatePageEndpoint";

export type {
  CreateDocusignSettingMutationVariables,
  CreateDocusignSettingMutation,
  CreateDocusignSettingMutationFn,
  CreateDocusignSettingMutationHookResult,
  CreateDocusignSettingMutationResult,
  CreateDocusignSettingMutationOptions,
} from "./createDocusignSetting";

export {
  CreateDocusignSettingDocument,
  useCreateDocusignSettingMutation,
} from "./createDocusignSetting";

export type {
  CreateDocusignModuleMutationVariables,
  CreateDocusignModuleMutation,
  CreateDocusignModuleMutationFn,
  CreateDocusignModuleMutationHookResult,
  CreateDocusignModuleMutationResult,
  CreateDocusignModuleMutationOptions,
} from "./createDocusignModule";

export {
  CreateDocusignModuleDocument,
  useCreateDocusignModuleMutation,
} from "./createDocusignModule";

export type {
  CreateDealerSocialMediaMutationVariables,
  CreateDealerSocialMediaMutation,
  CreateDealerSocialMediaMutationFn,
  CreateDealerSocialMediaMutationHookResult,
  CreateDealerSocialMediaMutationResult,
  CreateDealerSocialMediaMutationOptions,
} from "./createDealerSocialMedia";

export {
  CreateDealerSocialMediaDocument,
  useCreateDealerSocialMediaMutation,
} from "./createDealerSocialMedia";

export type {
  CreateDealerMutationVariables,
  CreateDealerMutation,
  CreateDealerMutationFn,
  CreateDealerMutationHookResult,
  CreateDealerMutationResult,
  CreateDealerMutationOptions,
} from "./createDealer";

export { CreateDealerDocument, useCreateDealerMutation } from "./createDealer";

export type {
  CreateCustomerListEndpointMutationVariables,
  CreateCustomerListEndpointMutation,
  CreateCustomerListEndpointMutationFn,
  CreateCustomerListEndpointMutationHookResult,
  CreateCustomerListEndpointMutationResult,
  CreateCustomerListEndpointMutationOptions,
} from "./createCustomerListEndpoint";

export {
  CreateCustomerListEndpointDocument,
  useCreateCustomerListEndpointMutation,
} from "./createCustomerListEndpoint";

export type {
  CreateCtsModuleSettingMutationVariables,
  CreateCtsModuleSettingMutation,
  CreateCtsModuleSettingMutationFn,
  CreateCtsModuleSettingMutationHookResult,
  CreateCtsModuleSettingMutationResult,
  CreateCtsModuleSettingMutationOptions,
} from "./createCtsModuleSetting";

export {
  CreateCtsModuleSettingDocument,
  useCreateCtsModuleSettingMutation,
} from "./createCtsModuleSetting";

export type {
  CreateCtsModuleMutationVariables,
  CreateCtsModuleMutation,
  CreateCtsModuleMutationFn,
  CreateCtsModuleMutationHookResult,
  CreateCtsModuleMutationResult,
  CreateCtsModuleMutationOptions,
} from "./createCtsModule";

export {
  CreateCtsModuleDocument,
  useCreateCtsModuleMutation,
} from "./createCtsModule";

export type {
  CreateConsentsAndDeclarationsModuleMutationVariables,
  CreateConsentsAndDeclarationsModuleMutation,
  CreateConsentsAndDeclarationsModuleMutationFn,
  CreateConsentsAndDeclarationsModuleMutationHookResult,
  CreateConsentsAndDeclarationsModuleMutationResult,
  CreateConsentsAndDeclarationsModuleMutationOptions,
} from "./createConsentsAndDeclarationsModule";

export {
  CreateConsentsAndDeclarationsModuleDocument,
  useCreateConsentsAndDeclarationsModuleMutation,
} from "./createConsentsAndDeclarationsModule";

export type {
  CreateConfiguratorModuleMutationVariables,
  CreateConfiguratorModuleMutation,
  CreateConfiguratorModuleMutationFn,
  CreateConfiguratorModuleMutationHookResult,
  CreateConfiguratorModuleMutationResult,
  CreateConfiguratorModuleMutationOptions,
} from "./createConfiguratorModule";

export {
  CreateConfiguratorModuleDocument,
  useCreateConfiguratorModuleMutation,
} from "./createConfiguratorModule";

export type {
  CreateConfiguratorApplicationEntrypointMutationVariables,
  CreateConfiguratorApplicationEntrypointMutation,
  CreateConfiguratorApplicationEntrypointMutationFn,
  CreateConfiguratorApplicationEntrypointMutationHookResult,
  CreateConfiguratorApplicationEntrypointMutationResult,
  CreateConfiguratorApplicationEntrypointMutationOptions,
} from "./createConfiguratorApplicationEntrypoint";

export {
  CreateConfiguratorApplicationEntrypointDocument,
  useCreateConfiguratorApplicationEntrypointMutation,
} from "./createConfiguratorApplicationEntrypoint";

export type {
  CreateCompanyMutationVariables,
  CreateCompanyMutation,
  CreateCompanyMutationFn,
  CreateCompanyMutationHookResult,
  CreateCompanyMutationResult,
  CreateCompanyMutationOptions,
} from "./createCompany";

export {
  CreateCompanyDocument,
  useCreateCompanyMutation,
} from "./createCompany";

export type {
  CreateColorBlockMutationVariables,
  CreateColorBlockMutation,
  CreateColorBlockMutationFn,
  CreateColorBlockMutationHookResult,
  CreateColorBlockMutationResult,
  CreateColorBlockMutationOptions,
} from "./createColorBlock";

export {
  CreateColorBlockDocument,
  useCreateColorBlockMutation,
} from "./createColorBlock";

export type {
  CreateCheckboxConsentsAndDeclarationsMutationVariables,
  CreateCheckboxConsentsAndDeclarationsMutation,
  CreateCheckboxConsentsAndDeclarationsMutationFn,
  CreateCheckboxConsentsAndDeclarationsMutationHookResult,
  CreateCheckboxConsentsAndDeclarationsMutationResult,
  CreateCheckboxConsentsAndDeclarationsMutationOptions,
} from "./createCheckboxConsentsAndDeclarations";

export {
  CreateCheckboxConsentsAndDeclarationsDocument,
  useCreateCheckboxConsentsAndDeclarationsMutation,
} from "./createCheckboxConsentsAndDeclarations";

export type {
  CreateCapModuleMutationVariables,
  CreateCapModuleMutation,
  CreateCapModuleMutationFn,
  CreateCapModuleMutationHookResult,
  CreateCapModuleMutationResult,
  CreateCapModuleMutationOptions,
} from "./createCapModule";

export {
  CreateCapModuleDocument,
  useCreateCapModuleMutation,
} from "./createCapModule";

export type {
  CreateBasicSigningModuleMutationVariables,
  CreateBasicSigningModuleMutation,
  CreateBasicSigningModuleMutationFn,
  CreateBasicSigningModuleMutationHookResult,
  CreateBasicSigningModuleMutationResult,
  CreateBasicSigningModuleMutationOptions,
} from "./createBasicSigningModule";

export {
  CreateBasicSigningModuleDocument,
  useCreateBasicSigningModuleMutation,
} from "./createBasicSigningModule";

export type {
  CreateBannerMutationVariables,
  CreateBannerMutation,
  CreateBannerMutationFn,
  CreateBannerMutationHookResult,
  CreateBannerMutationResult,
  CreateBannerMutationOptions,
} from "./createBanner";

export { CreateBannerDocument, useCreateBannerMutation } from "./createBanner";

export type {
  CreateBankModuleMutationVariables,
  CreateBankModuleMutation,
  CreateBankModuleMutationFn,
  CreateBankModuleMutationHookResult,
  CreateBankModuleMutationResult,
  CreateBankModuleMutationOptions,
} from "./createBankModule";

export {
  CreateBankModuleDocument,
  useCreateBankModuleMutation,
} from "./createBankModule";

export type {
  CreateBankMutationVariables,
  CreateBankMutation,
  CreateBankMutationFn,
  CreateBankMutationHookResult,
  CreateBankMutationResult,
  CreateBankMutationOptions,
} from "./createBank";

export { CreateBankDocument, useCreateBankMutation } from "./createBank";

export type {
  CreateAutoplayModuleSettingMutationVariables,
  CreateAutoplayModuleSettingMutation,
  CreateAutoplayModuleSettingMutationFn,
  CreateAutoplayModuleSettingMutationHookResult,
  CreateAutoplayModuleSettingMutationResult,
  CreateAutoplayModuleSettingMutationOptions,
} from "./createAutoplayModuleSetting";

export {
  CreateAutoplayModuleSettingDocument,
  useCreateAutoplayModuleSettingMutation,
} from "./createAutoplayModuleSetting";

export type {
  CreateAutoplayModuleMutationVariables,
  CreateAutoplayModuleMutation,
  CreateAutoplayModuleMutationFn,
  CreateAutoplayModuleMutationHookResult,
  CreateAutoplayModuleMutationResult,
  CreateAutoplayModuleMutationOptions,
} from "./createAutoplayModule";

export {
  CreateAutoplayModuleDocument,
  useCreateAutoplayModuleMutation,
} from "./createAutoplayModule";

export type {
  CreateAppointmentModuleMutationVariables,
  CreateAppointmentModuleMutation,
  CreateAppointmentModuleMutationFn,
  CreateAppointmentModuleMutationHookResult,
  CreateAppointmentModuleMutationResult,
  CreateAppointmentModuleMutationOptions,
} from "./createAppointmentModule";

export {
  CreateAppointmentModuleDocument,
  useCreateAppointmentModuleMutation,
} from "./createAppointmentModule";

export type {
  CreateAppointmentFromLeadMutationVariables,
  CreateAppointmentFromLeadMutation,
  CreateAppointmentFromLeadMutationFn,
  CreateAppointmentFromLeadMutationHookResult,
  CreateAppointmentFromLeadMutationResult,
  CreateAppointmentFromLeadMutationOptions,
} from "./createAppointmentFromLead";

export {
  CreateAppointmentFromLeadDocument,
  useCreateAppointmentFromLeadMutation,
} from "./createAppointmentFromLead";

export type {
  CreateApplicationListEndpointMutationVariables,
  CreateApplicationListEndpointMutation,
  CreateApplicationListEndpointMutationFn,
  CreateApplicationListEndpointMutationHookResult,
  CreateApplicationListEndpointMutationResult,
  CreateApplicationListEndpointMutationOptions,
} from "./createApplicationListEndpoint";

export {
  CreateApplicationListEndpointDocument,
  useCreateApplicationListEndpointMutation,
} from "./createApplicationListEndpoint";

export type {
  CreateAdyenPaymentSettingsMutationVariables,
  CreateAdyenPaymentSettingsMutation,
  CreateAdyenPaymentSettingsMutationFn,
  CreateAdyenPaymentSettingsMutationHookResult,
  CreateAdyenPaymentSettingsMutationResult,
  CreateAdyenPaymentSettingsMutationOptions,
} from "./createAdyenPaymentSettings";

export {
  CreateAdyenPaymentSettingsDocument,
  useCreateAdyenPaymentSettingsMutation,
} from "./createAdyenPaymentSettings";

export type {
  CreateAdyenPaymentModuleMutationVariables,
  CreateAdyenPaymentModuleMutation,
  CreateAdyenPaymentModuleMutationFn,
  CreateAdyenPaymentModuleMutationHookResult,
  CreateAdyenPaymentModuleMutationResult,
  CreateAdyenPaymentModuleMutationOptions,
} from "./createAdyenPaymentModule";

export {
  CreateAdyenPaymentModuleDocument,
  useCreateAdyenPaymentModuleMutation,
} from "./createAdyenPaymentModule";

export type {
  CreateAdditionalDetailMutationVariables,
  CreateAdditionalDetailMutation,
  CreateAdditionalDetailMutationFn,
  CreateAdditionalDetailMutationHookResult,
  CreateAdditionalDetailMutationResult,
  CreateAdditionalDetailMutationOptions,
} from "./createAdditionalDetail";

export {
  CreateAdditionalDetailDocument,
  useCreateAdditionalDetailMutation,
} from "./createAdditionalDetail";

export type {
  CopyVariantConfiguratorMutationVariables,
  CopyVariantConfiguratorMutation,
  CopyVariantConfiguratorMutationFn,
  CopyVariantConfiguratorMutationHookResult,
  CopyVariantConfiguratorMutationResult,
  CopyVariantConfiguratorMutationOptions,
} from "./copyVariantConfigurator";

export {
  CopyVariantConfiguratorDocument,
  useCopyVariantConfiguratorMutation,
} from "./copyVariantConfigurator";

export type {
  ContinueApplicationMutationVariables,
  ContinueApplicationMutation,
  ContinueApplicationMutationFn,
  ContinueApplicationMutationHookResult,
  ContinueApplicationMutationResult,
  ContinueApplicationMutationOptions,
} from "./continueApplication";

export {
  ContinueApplicationDocument,
  useContinueApplicationMutation,
} from "./continueApplication";

export type {
  ContactApplicationMutationVariables,
  ContactApplicationMutation,
  ContactApplicationMutationFn,
  ContactApplicationMutationHookResult,
  ContactApplicationMutationResult,
  ContactApplicationMutationOptions,
} from "./contactApplication";

export {
  ContactApplicationDocument,
  useContactApplicationMutation,
} from "./contactApplication";

export type {
  ConfirmLeadFollowUpMutationVariables,
  ConfirmLeadFollowUpMutation,
  ConfirmLeadFollowUpMutationFn,
  ConfirmLeadFollowUpMutationHookResult,
  ConfirmLeadFollowUpMutationResult,
  ConfirmLeadFollowUpMutationOptions,
} from "./confirmLeadFollowUp";

export {
  ConfirmLeadFollowUpDocument,
  useConfirmLeadFollowUpMutation,
} from "./confirmLeadFollowUp";

export type {
  ConfirmBookingApplicationMutationVariables,
  ConfirmBookingApplicationMutation,
  ConfirmBookingApplicationMutationFn,
  ConfirmBookingApplicationMutationHookResult,
  ConfirmBookingApplicationMutationResult,
  ConfirmBookingApplicationMutationOptions,
} from "./confirmBookingApplication";

export {
  ConfirmBookingApplicationDocument,
  useConfirmBookingApplicationMutation,
} from "./confirmBookingApplication";

export type {
  ConcludeAgreementApplicationMutationVariables,
  ConcludeAgreementApplicationMutation,
  ConcludeAgreementApplicationMutationFn,
  ConcludeAgreementApplicationMutationHookResult,
  ConcludeAgreementApplicationMutationResult,
  ConcludeAgreementApplicationMutationOptions,
} from "./concludeAgreementApplication";

export {
  ConcludeAgreementApplicationDocument,
  useConcludeAgreementApplicationMutation,
} from "./concludeAgreementApplication";

export type {
  CompleteWebPublicKeyCredentialRegistrationMutationVariables,
  CompleteWebPublicKeyCredentialRegistrationMutation,
  CompleteWebPublicKeyCredentialRegistrationMutationFn,
  CompleteWebPublicKeyCredentialRegistrationMutationHookResult,
  CompleteWebPublicKeyCredentialRegistrationMutationResult,
  CompleteWebPublicKeyCredentialRegistrationMutationOptions,
} from "./completeWebPublicKeyCredentialRegistration";

export {
  CompleteWebPublicKeyCredentialRegistrationDocument,
  useCompleteWebPublicKeyCredentialRegistrationMutation,
} from "./completeWebPublicKeyCredentialRegistration";

export type {
  CompleteLeadMutationVariables,
  CompleteLeadMutation,
  CompleteLeadMutationFn,
  CompleteLeadMutationHookResult,
  CompleteLeadMutationResult,
  CompleteLeadMutationOptions,
} from "./completeLead";

export { CompleteLeadDocument, useCompleteLeadMutation } from "./completeLead";

export type {
  CompleteApplicationMutationVariables,
  CompleteApplicationMutation,
  CompleteApplicationMutationFn,
  CompleteApplicationMutationHookResult,
  CompleteApplicationMutationResult,
  CompleteApplicationMutationOptions,
} from "./completeApplication";

export {
  CompleteApplicationDocument,
  useCompleteApplicationMutation,
} from "./completeApplication";

export type {
  CheckInApplicationMutationVariables,
  CheckInApplicationMutation,
  CheckInApplicationMutationFn,
  CheckInApplicationMutationHookResult,
  CheckInApplicationMutationResult,
  CheckInApplicationMutationOptions,
} from "./checkInApplication";

export {
  CheckInApplicationDocument,
  useCheckInApplicationMutation,
} from "./checkInApplication";

export type {
  ChangePasswordFromTokenMutationVariables,
  ChangePasswordFromTokenMutation,
  ChangePasswordFromTokenMutationFn,
  ChangePasswordFromTokenMutationHookResult,
  ChangePasswordFromTokenMutationResult,
  ChangePasswordFromTokenMutationOptions,
} from "./changePasswordFromToken";

export {
  ChangePasswordFromTokenDocument,
  useChangePasswordFromTokenMutation,
} from "./changePasswordFromToken";

export type {
  ChangePasswordFromAuthenticationMutationVariables,
  ChangePasswordFromAuthenticationMutation,
  ChangePasswordFromAuthenticationMutationFn,
  ChangePasswordFromAuthenticationMutationHookResult,
  ChangePasswordFromAuthenticationMutationResult,
  ChangePasswordFromAuthenticationMutationOptions,
} from "./changePasswordFromAuthentication";

export {
  ChangePasswordFromAuthenticationDocument,
  useChangePasswordFromAuthenticationMutation,
} from "./changePasswordFromAuthentication";

export type {
  ChangePasswordMutationVariables,
  ChangePasswordMutation,
  ChangePasswordMutationFn,
  ChangePasswordMutationHookResult,
  ChangePasswordMutationResult,
  ChangePasswordMutationOptions,
} from "./changePassword";

export {
  ChangePasswordDocument,
  useChangePasswordMutation,
} from "./changePassword";

export type {
  CancelLeadFollowUpMutationVariables,
  CancelLeadFollowUpMutation,
  CancelLeadFollowUpMutationFn,
  CancelLeadFollowUpMutationHookResult,
  CancelLeadFollowUpMutationResult,
  CancelLeadFollowUpMutationOptions,
} from "./cancelLeadFollowUp";

export {
  CancelLeadFollowUpDocument,
  useCancelLeadFollowUpMutation,
} from "./cancelLeadFollowUp";

export type {
  CancelApplicationMutationVariables,
  CancelApplicationMutation,
  CancelApplicationMutationFn,
  CancelApplicationMutationHookResult,
  CancelApplicationMutationResult,
  CancelApplicationMutationOptions,
} from "./cancelApplication";

export {
  CancelApplicationDocument,
  useCancelApplicationMutation,
} from "./cancelApplication";

export type {
  CalculateMutationVariables,
  CalculateMutation,
  CalculateMutationFn,
  CalculateMutationHookResult,
  CalculateMutationResult,
  CalculateMutationOptions,
} from "./calculate";

export { CalculateDocument, useCalculateMutation } from "./calculate";

export type {
  AuthenticateWithWebAuthnMutationVariables,
  AuthenticateWithWebAuthnMutation,
  AuthenticateWithWebAuthnMutationFn,
  AuthenticateWithWebAuthnMutationHookResult,
  AuthenticateWithWebAuthnMutationResult,
  AuthenticateWithWebAuthnMutationOptions,
} from "./authenticateWithWebAuthn";

export {
  AuthenticateWithWebAuthnDocument,
  useAuthenticateWithWebAuthnMutation,
} from "./authenticateWithWebAuthn";

export type {
  AuthenticateWithTotpMutationVariables,
  AuthenticateWithTotpMutation,
  AuthenticateWithTotpMutationFn,
  AuthenticateWithTotpMutationHookResult,
  AuthenticateWithTotpMutationResult,
  AuthenticateWithTotpMutationOptions,
} from "./authenticateWithTOTP";

export {
  AuthenticateWithTotpDocument,
  useAuthenticateWithTotpMutation,
} from "./authenticateWithTOTP";

export type {
  AuthenticateWithSmsOtpMutationVariables,
  AuthenticateWithSmsOtpMutation,
  AuthenticateWithSmsOtpMutationFn,
  AuthenticateWithSmsOtpMutationHookResult,
  AuthenticateWithSmsOtpMutationResult,
  AuthenticateWithSmsOtpMutationOptions,
} from "./authenticateWithSmsOTP";

export {
  AuthenticateWithSmsOtpDocument,
  useAuthenticateWithSmsOtpMutation,
} from "./authenticateWithSmsOTP";

export type {
  AuthenticateMutationVariables,
  AuthenticateMutation,
  AuthenticateMutationFn,
  AuthenticateMutationHookResult,
  AuthenticateMutationResult,
  AuthenticateMutationOptions,
} from "./authenticate";

export { AuthenticateDocument, useAuthenticateMutation } from "./authenticate";

export type {
  AttachUsersToUserGroupMutationVariables,
  AttachUsersToUserGroupMutation,
  AttachUsersToUserGroupMutationFn,
  AttachUsersToUserGroupMutationHookResult,
  AttachUsersToUserGroupMutationResult,
  AttachUsersToUserGroupMutationOptions,
} from "./attachUsersToUserGroup";

export {
  AttachUsersToUserGroupDocument,
  useAttachUsersToUserGroupMutation,
} from "./attachUsersToUserGroup";

export type {
  AttachUserOnRoleMutationVariables,
  AttachUserOnRoleMutation,
  AttachUserOnRoleMutationFn,
  AttachUserOnRoleMutationHookResult,
  AttachUserOnRoleMutationResult,
  AttachUserOnRoleMutationOptions,
} from "./attachUserOnRole";

export {
  AttachUserOnRoleDocument,
  useAttachUserOnRoleMutation,
} from "./attachUserOnRole";

export type {
  AttachSuperiorGroupsToUserGroupMutationVariables,
  AttachSuperiorGroupsToUserGroupMutation,
  AttachSuperiorGroupsToUserGroupMutationFn,
  AttachSuperiorGroupsToUserGroupMutationHookResult,
  AttachSuperiorGroupsToUserGroupMutationResult,
  AttachSuperiorGroupsToUserGroupMutationOptions,
} from "./attachSuperiorGroupsToUserGroup";

export {
  AttachSuperiorGroupsToUserGroupDocument,
  useAttachSuperiorGroupsToUserGroupMutation,
} from "./attachSuperiorGroupsToUserGroup";

export type {
  AttachPermissionOnRoleMutationVariables,
  AttachPermissionOnRoleMutation,
  AttachPermissionOnRoleMutationFn,
  AttachPermissionOnRoleMutationHookResult,
  AttachPermissionOnRoleMutationResult,
  AttachPermissionOnRoleMutationOptions,
} from "./attachPermissionOnRole";

export {
  AttachPermissionOnRoleDocument,
  useAttachPermissionOnRoleMutation,
} from "./attachPermissionOnRole";

export type {
  AttachDealersToUserGroupMutationVariables,
  AttachDealersToUserGroupMutation,
  AttachDealersToUserGroupMutationFn,
  AttachDealersToUserGroupMutationHookResult,
  AttachDealersToUserGroupMutationResult,
  AttachDealersToUserGroupMutationOptions,
} from "./attachDealersToUserGroup";

export {
  AttachDealersToUserGroupDocument,
  useAttachDealersToUserGroupMutation,
} from "./attachDealersToUserGroup";

export type {
  ApproveApplicationMutationVariables,
  ApproveApplicationMutation,
  ApproveApplicationMutationFn,
  ApproveApplicationMutationHookResult,
  ApproveApplicationMutationResult,
  ApproveApplicationMutationOptions,
} from "./approveApplication";

export {
  ApproveApplicationDocument,
  useApproveApplicationMutation,
} from "./approveApplication";

export type {
  ApplyPorscheV3LayoutOnRouterMutationVariables,
  ApplyPorscheV3LayoutOnRouterMutation,
  ApplyPorscheV3LayoutOnRouterMutationFn,
  ApplyPorscheV3LayoutOnRouterMutationHookResult,
  ApplyPorscheV3LayoutOnRouterMutationResult,
  ApplyPorscheV3LayoutOnRouterMutationOptions,
} from "./applyPorscheV3LayoutOnRouter";

export {
  ApplyPorscheV3LayoutOnRouterDocument,
  useApplyPorscheV3LayoutOnRouterMutation,
} from "./applyPorscheV3LayoutOnRouter";

export type {
  ApplyNewStandardApplicationMutationVariables,
  ApplyNewStandardApplicationMutation,
  ApplyNewStandardApplicationMutationFn,
  ApplyNewStandardApplicationMutationHookResult,
  ApplyNewStandardApplicationMutationResult,
  ApplyNewStandardApplicationMutationOptions,
} from "./applyNewStandardApplication";

export {
  ApplyNewStandardApplicationDocument,
  useApplyNewStandardApplicationMutation,
} from "./applyNewStandardApplication";

export type {
  ApplyNewFinderApplicationMutationVariables,
  ApplyNewFinderApplicationMutation,
  ApplyNewFinderApplicationMutationFn,
  ApplyNewFinderApplicationMutationHookResult,
  ApplyNewFinderApplicationMutationResult,
  ApplyNewFinderApplicationMutationOptions,
} from "./applyNewFinderApplication";

export {
  ApplyNewFinderApplicationDocument,
  useApplyNewFinderApplicationMutation,
} from "./applyNewFinderApplication";

export type {
  ApplyNewConfiguratorApplicationMutationVariables,
  ApplyNewConfiguratorApplicationMutation,
  ApplyNewConfiguratorApplicationMutationFn,
  ApplyNewConfiguratorApplicationMutationHookResult,
  ApplyNewConfiguratorApplicationMutationResult,
  ApplyNewConfiguratorApplicationMutationOptions,
} from "./applyNewConfiguratorApplication";

export {
  ApplyNewConfiguratorApplicationDocument,
  useApplyNewConfiguratorApplicationMutation,
} from "./applyNewConfiguratorApplication";

export type {
  ApplyNewMutationVariables,
  ApplyNewMutation,
  ApplyNewMutationFn,
  ApplyNewMutationHookResult,
  ApplyNewMutationResult,
  ApplyNewMutationOptions,
} from "./applyNew";

export { ApplyNewDocument, useApplyNewMutation } from "./applyNew";

export type {
  ApplyGiftVoucherMutationVariables,
  ApplyGiftVoucherMutation,
  ApplyGiftVoucherMutationFn,
  ApplyGiftVoucherMutationHookResult,
  ApplyGiftVoucherMutationResult,
  ApplyGiftVoucherMutationOptions,
} from "./applyGiftVoucher";

export {
  ApplyGiftVoucherDocument,
  useApplyGiftVoucherMutation,
} from "./applyGiftVoucher";

export type {
  ApplyForPasswordChangeMutationVariables,
  ApplyForPasswordChangeMutation,
  ApplyForPasswordChangeMutationFn,
  ApplyForPasswordChangeMutationHookResult,
  ApplyForPasswordChangeMutationResult,
  ApplyForPasswordChangeMutationOptions,
} from "./applyForPasswordChange";

export {
  ApplyForPasswordChangeDocument,
  useApplyForPasswordChangeMutation,
} from "./applyForPasswordChange";

export type {
  ApplyEmptyLayoutOnRouterMutationVariables,
  ApplyEmptyLayoutOnRouterMutation,
  ApplyEmptyLayoutOnRouterMutationFn,
  ApplyEmptyLayoutOnRouterMutationHookResult,
  ApplyEmptyLayoutOnRouterMutationResult,
  ApplyEmptyLayoutOnRouterMutationOptions,
} from "./applyEmptyLayoutOnRouter";

export {
  ApplyEmptyLayoutOnRouterDocument,
  useApplyEmptyLayoutOnRouterMutation,
} from "./applyEmptyLayoutOnRouter";

export type {
  ApplyBasicProLayoutOnRouterMutationVariables,
  ApplyBasicProLayoutOnRouterMutation,
  ApplyBasicProLayoutOnRouterMutationFn,
  ApplyBasicProLayoutOnRouterMutationHookResult,
  ApplyBasicProLayoutOnRouterMutationResult,
  ApplyBasicProLayoutOnRouterMutationOptions,
} from "./applyBasicProLayoutOnRouter";

export {
  ApplyBasicProLayoutOnRouterDocument,
  useApplyBasicProLayoutOnRouterMutation,
} from "./applyBasicProLayoutOnRouter";

export type {
  ApplyBasicLayoutOnRouterMutationVariables,
  ApplyBasicLayoutOnRouterMutation,
  ApplyBasicLayoutOnRouterMutationFn,
  ApplyBasicLayoutOnRouterMutationHookResult,
  ApplyBasicLayoutOnRouterMutationResult,
  ApplyBasicLayoutOnRouterMutationOptions,
} from "./applyBasicLayoutOnRouter";

export {
  ApplyBasicLayoutOnRouterDocument,
  useApplyBasicLayoutOnRouterMutation,
} from "./applyBasicLayoutOnRouter";

export type {
  AmendMobilityApplicationMutationVariables,
  AmendMobilityApplicationMutation,
  AmendMobilityApplicationMutationFn,
  AmendMobilityApplicationMutationHookResult,
  AmendMobilityApplicationMutationResult,
  AmendMobilityApplicationMutationOptions,
} from "./amendMobilityApplication";

export {
  AmendMobilityApplicationDocument,
  useAmendMobilityApplicationMutation,
} from "./amendMobilityApplication";

export type {
  AddTradeInAuditTrailCommentMutationVariables,
  AddTradeInAuditTrailCommentMutation,
  AddTradeInAuditTrailCommentMutationFn,
  AddTradeInAuditTrailCommentMutationHookResult,
  AddTradeInAuditTrailCommentMutationResult,
  AddTradeInAuditTrailCommentMutationOptions,
} from "./addTradeInAuditTrailComment";

export {
  AddTradeInAuditTrailCommentDocument,
  useAddTradeInAuditTrailCommentMutation,
} from "./addTradeInAuditTrailComment";

export type {
  AddStockInventoryMutationVariables,
  AddStockInventoryMutation,
  AddStockInventoryMutationFn,
  AddStockInventoryMutationHookResult,
  AddStockInventoryMutationResult,
  AddStockInventoryMutationOptions,
} from "./addStockInventory";

export {
  AddStockInventoryDocument,
  useAddStockInventoryMutation,
} from "./addStockInventory";

export type {
  AddInventoryAuditTrailCommentMutationVariables,
  AddInventoryAuditTrailCommentMutation,
  AddInventoryAuditTrailCommentMutationFn,
  AddInventoryAuditTrailCommentMutationHookResult,
  AddInventoryAuditTrailCommentMutationResult,
  AddInventoryAuditTrailCommentMutationOptions,
} from "./addInventoryAuditTrailComment";

export {
  AddInventoryAuditTrailCommentDocument,
  useAddInventoryAuditTrailCommentMutation,
} from "./addInventoryAuditTrailComment";

export type {
  AddCustomerAuditTrailCommentMutationVariables,
  AddCustomerAuditTrailCommentMutation,
  AddCustomerAuditTrailCommentMutationFn,
  AddCustomerAuditTrailCommentMutationHookResult,
  AddCustomerAuditTrailCommentMutationResult,
  AddCustomerAuditTrailCommentMutationOptions,
} from "./addCustomerAuditTrailComment";

export {
  AddCustomerAuditTrailCommentDocument,
  useAddCustomerAuditTrailCommentMutation,
} from "./addCustomerAuditTrailComment";

export type {
  AddAuditTrailCommentMutationVariables,
  AddAuditTrailCommentMutation,
  AddAuditTrailCommentMutationFn,
  AddAuditTrailCommentMutationHookResult,
  AddAuditTrailCommentMutationResult,
  AddAuditTrailCommentMutationOptions,
} from "./addAuditTrailComment";

export {
  AddAuditTrailCommentDocument,
  useAddAuditTrailCommentMutation,
} from "./addAuditTrailComment";

export type {
  AuthorizeWithOidcMutationVariables,
  AuthorizeWithOidcMutation,
  AuthorizeWithOidcMutationFn,
  AuthorizeWithOidcMutationHookResult,
  AuthorizeWithOidcMutationResult,
  AuthorizeWithOidcMutationOptions,
} from "./OIDC";

export {
  AuthorizeWithOidcDocument,
  useAuthorizeWithOidcMutation,
} from "./OIDC";
