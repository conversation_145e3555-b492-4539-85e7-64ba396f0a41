import type * as SchemaTypes from '../types';

import type { ApplicationData_ConfiguratorApplication_Fragment, ApplicationData_EventApplication_Fragment, ApplicationData_FinderApplication_Fragment, ApplicationData_LaunchpadApplication_Fragment, ApplicationData_MobilityApplication_Fragment, ApplicationData_SalesOfferApplication_Fragment, ApplicationData_StandardApplication_Fragment } from '../fragments/ApplicationData';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from '../fragments/ApplicationStageData';
import type { EventApplicationModuleSpecsForApplicationFragment } from '../fragments/EventApplicationModuleSpecsForApplication';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { KycExtraSettingsSpecsFragment } from '../fragments/KYCExtraSettingsSpecs';
import type { StandardApplicationModuleSpecsForApplicationFragment } from '../fragments/StandardApplicationModuleSpecsForApplication';
import type { DepositAmountDataFragment } from '../fragments/DepositAmountData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from '../fragments/ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from '../fragments/DealerMarketData';
import type { BankDealerMarketDataFragment } from '../fragments/BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from '../fragments/NzFeesDealerMarketData';
import type { DealerDisclaimersConfiguratorDataFragment } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from '../fragments/DealershipSettingSpecData';
import type { FlexibleDiscountDataFragment } from '../fragments/FlexibleDiscountData';
import type { DealerPriceDisclaimerDataFragment } from '../fragments/DealerPriceDisclaimerData';
import type { ConfiguratorModuleSpecsForApplicationFragment } from '../fragments/ConfiguratorModuleSpecsForApplication';
import type { FinderApplicationPublicModuleSpecsForApplicationFragment } from '../fragments/FinderApplicationPublicModuleSpecsForApplication';
import type { KycPresetsOptionsDataFragment } from '../fragments/KYCPresetsOptionsData';
import type { FinderApplicationPrivateModuleSpecsForApplicationFragment } from '../fragments/FinderApplicationPrivateModuleSpecsForApplication';
import type { LaunchpadModuleSpecsForApplicationFragment } from '../fragments/LaunchpadModuleSpecsForApplication';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { LeadData_ConfiguratorLead_Fragment, LeadData_EventLead_Fragment, LeadData_FinderLead_Fragment, LeadData_LaunchpadLead_Fragment, LeadData_MobilityLead_Fragment, LeadData_StandardLead_Fragment } from '../fragments/LeadData';
import type { StandardLeadDataFragment } from '../fragments/StandardLeadData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from '../fragments/VehicleSpecs';
import type { LocalVariantSpecsFragment } from '../fragments/LocalVariantSpecs';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { FinderVehicleSpecsFragment } from '../fragments/FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from '../fragments/finderListing.fragment';
import type { FinderLeadDataFragment } from '../fragments/FinderLeadData';
import type { EventLeadDataFragment } from '../fragments/EventLeadData';
import type { ApplicationEventCustomizedFieldDataFragment } from '../fragments/ApplicationEventCustomizedFieldData';
import type { LaunchpadLeadDataFragment } from '../fragments/LaunchpadLeadData';
import type { ConfiguratorLeadDataFragment } from '../fragments/ConfiguratorLeadData';
import type { ConfiguratorJourneyBlocksData_ApplicationConfiguratorColorSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorOptionSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorPackageSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorTrimSetting_Fragment } from '../fragments/ConfiguratorJourneyBlocksData';
import type { BlockDetails_ColorBlock_Fragment, BlockDetails_OptionsBlock_Fragment, BlockDetails_PackageBlock_Fragment, BlockDetails_TrimBlock_Fragment } from '../fragments/BlockDetails';
import type { OptionSettingDetails_ComboOptionSettings_Fragment, OptionSettingDetails_DropdownOptionSettings_Fragment, OptionSettingDetails_MultiSelectOptionSettings_Fragment, OptionSettingDetails_SingleSelectOptionSettings_Fragment } from '../fragments/OptionSettingDetails';
import type { MobilityLeadDataFragment } from '../fragments/MobilityLeadData';
import type { CustomerSpecs_CorporateCustomer_Fragment, CustomerSpecs_Guarantor_Fragment, CustomerSpecs_LocalCustomer_Fragment } from '../fragments/CustomerSpecs';
import type { LocalCustomerDataFragment } from '../fragments/LocalCustomerData';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from '../fragments/LocalCustomerFieldData';
import type { CorporateCustomerDataFragment } from '../fragments/CorporateCustomerData';
import type { GuarantorDataFragment } from '../fragments/GuarantorData';
import type { UsersOptionsDataFragment } from '../fragments/UsersOptionsData';
import type { KycFieldSpecsFragment } from '../fragments/KYCFieldSpecs';
import type { LaunchPadModuleSpecsFragment } from '../fragments/LaunchPadModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from '../fragments/CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { AppointmentModuleSpecsFragment } from '../fragments/AppointmentModuleSpecs';
import type { AppointmentTimeSlotDataFragment } from '../fragments/AppointmentTimeSlotData';
import type { NamirialSigningModuleSpecsFragment } from '../fragments/NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from '../fragments/NamirialSettingsSpec';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from '../fragments/AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { DealerBooleanSettingDataFragment } from '../fragments/DealerBooleanSettingData';
import type { VisitAppointmentModuleSpecsFragment } from '../fragments/VisitAppointmentModuleSpecs';
import type { TimeSlotDataFragment } from '../fragments/TimeSlotData';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import type { CounterSettingsSpecsFragment } from '../fragments/CounterSettingsSpecs';
import type { DealerVehiclesSpecsFragment } from '../fragments/DealerVehiclesSpecs';
import type { ApplicationAgreementData_CheckboxApplicationAgreement_Fragment, ApplicationAgreementData_MarketingApplicationAgreement_Fragment, ApplicationAgreementData_TextApplicationAgreement_Fragment } from '../fragments/ApplicationAgreementData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { MarketingPlatformSpecsFragment } from '../fragments/MarketingPlatformSpecs';
import type { MarketingPlatformsAgreedSpecsFragment } from '../fragments/MarketingPlatformsAgreedSpecs';
import type { TradeInVehicleDataFragment } from '../fragments/TradeInVehicleData';
import type { DealerApplicationFragmentFragment } from '../fragments/DealerApplicationFragment';
import type { DealerContactFragmentFragment } from '../fragments/DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from '../fragments/DealerSocialMediaFragment';
import type { DealerDisclaimersFragmentFragment } from '../fragments/DealerDisclaimersFragment';
import type { DealerIntegrationDetailsFragmentFragment } from '../fragments/DealerIntegrationDetailsFragment';
import type { ApplicationDocumentDataFragment } from '../fragments/ApplicationDocumentData';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from '../fragments/ReferenceApplicationData';
import type { SalesOfferSpecsFragment } from '../fragments/SalesOfferSpecs';
import type { VehicleSalesOfferSpecsFragment } from '../fragments/VehicleSalesOfferSpecs';
import type { PorscheVehicleDataSpecsFragment, PorscheVehicleDataFeatureSpecsFragment, PorscheVehicleImagesSpecsFragment } from '../fragments/PorscheVehicleDataSpecs';
import type { LocalFittedOptionsSpecsFragment } from '../fragments/LocalFittedOptionsSpecs';
import type { SalesOfferDocumentDataFragment } from '../fragments/SalesOfferDocumentData';
import type { MainDetailsSalesOfferSpecsFragment } from '../fragments/MainDetailsSalesOfferSpecs';
import type { TradeInSalesOfferSpecsFragment } from '../fragments/TradeInSalesOfferSpecs';
import type { FinanceSalesOfferSpecsFragment } from '../fragments/FinanceSalesOfferSpecs';
import type { ApplicationFinancingData_DefaultApplicationFinancing_Fragment, ApplicationFinancingData_NewZealandApplicationFinancing_Fragment, ApplicationFinancingData_SingaporeApplicationFinancing_Fragment } from '../fragments/ApplicationFinancingData';
import type { InsuranceSalesOfferSpecsFragment } from '../fragments/InsuranceSalesOfferSpecs';
import type { ApplicationInsurancingData_DefaultApplicationInsurancing_Fragment, ApplicationInsurancingData_NewZealandApplicationInsurancing_Fragment, ApplicationInsurancingData_SingaporeApplicationInsurancing_Fragment } from '../fragments/ApplicationInsurancingData';
import type { DepositSalesOfferSpecsFragment } from '../fragments/DepositSalesOfferSpecs';
import type { VsaSalesOfferSpecsFragment } from '../fragments/VSASalesOfferSpecs';
import type { SalesOfferModuleSpecsFragment } from '../fragments/SalesOfferModuleSpecs';
import type { BankDetailsDataFragment } from '../fragments/BankDetailsData';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from '../fragments/BankIntegrationData';
import type { UploadFileFormDataFragment } from '../fragments/UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductDetailsData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { VehicleReferenceParametersDataFragment } from '../fragments/VehicleReferenceParametersData';
import type { PaymentSettingsDetailsFragment } from '../fragments/PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from '../fragments/LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from '../fragments/TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from '../fragments/InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from '../fragments/DownPaymentSettingsDetails';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from '../fragments/BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from '../fragments/BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from '../fragments/LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from '../fragments/DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from '../fragments/ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from '../fragments/LocalUcclLeasingOnlyDetails';
import type { DealerFinanceProductsSpecsFragment } from '../fragments/DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductListData';
import type { DealerInsuranceProductsSpecsFragment } from '../fragments/DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from '../fragments/InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from '../fragments/ErgoLookupTableSettingDetails';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from '../fragments/SalesOfferModuleEmailContentsSpecs';
import type { SalesOfferKycPresetSpecsFragment } from '../fragments/SalesOfferKYCPresetSpecs';
import type { SalesOfferConsentsSpecsFragment } from '../fragments/SalesOfferConsentsSpecs';
import type { SalesOfferSigningsSpecsFragment } from '../fragments/SalesOfferSigningsSpecs';
import type { NamirialSigningDataFragment } from '../fragments/NamirialSigningData';
import type { StandardApplicationDataFragment } from '../fragments/StandardApplicationData';
import type { DraftFlowConfigurationSpecFragment } from '../fragments/DraftFlowConfigurationSpec';
import type { InsuranceProductDetailsData_Eazy_Fragment, InsuranceProductDetailsData_ErgoLookupTable_Fragment } from '../fragments/InsuranceProductDetailsData';
import type { ApplicationConfigurationDataFragment } from '../fragments/ApplicationConfigurationData';
import type { FinanceProductDetails_LocalDeferredPrincipal_Fragment, FinanceProductDetails_LocalHirePurchase_Fragment, FinanceProductDetails_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetails_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetails_LocalLease_Fragment, FinanceProductDetails_LocalLeasePurchase_Fragment, FinanceProductDetails_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductDetails';
import type { LocalHirePurchaseDetailsFragment } from '../fragments/LocalHirePurchaseDetails';
import type { LocalHirePurchaseWithBalloonDetailsFragment } from '../fragments/LocalHirePurchaseWithBalloonDetails';
import type { LocalHirePurchaseWithBalloonGfvDetailsFragment } from '../fragments/LocalHirePurchaseWithBalloonGFVDetails';
import type { LocalLeaseDetailsFragment } from '../fragments/LocalLeaseDetails';
import type { LocalLeasePurchaseDetailsFragment } from '../fragments/LocalLeasePurchaseDetails';
import type { LocalDeferredPrincipalDetailsFragment } from '../fragments/LocalDeferredPrincipalDetails';
import type { LocalUcclLeasingDetailsFragment } from '../fragments/LocalUcclLeasingDetails';
import type { ApplicationAdyenDepositDataFragment } from '../fragments/ApplicationAdyenDepositData';
import type { ApplicationPorscheDepositDataFragment } from '../fragments/ApplicationPorscheDepositData';
import type { ApplicationFiservDepositDataFragment } from '../fragments/ApplicationFiservDepositData';
import type { ApplicationPayGateDepositDataFragment } from '../fragments/ApplicationPayGateDepositData';
import type { ApplicationTtbDepositDataFragment } from '../fragments/ApplicationTtbDepositData';
import type { PromoCodeDataFragment } from '../fragments/PromoCodeData';
import type { DealerFragmentFragment } from '../fragments/DealerFragment';
import type { CompanyContextDataFragment } from '../fragments/CompanyContextData';
import type { LanguagePackContextDataFragment } from '../fragments/LanguagePackContextData';
import type { AvailableModulesDataFragment } from '../fragments/AvailableModulesData';
import type { CompanyDealerDataFragment } from '../fragments/CompanyDealerData';
import type { MaintenanceUpdateFragment } from '../fragments/MaintenanceUpdate';
import type { UserAvatarSpecsFragment } from '../fragments/UserAvatarSpecs';
import type { EdmEmailFooterPublicDataFragment } from '../fragments/EdmEmailFooterPublicData';
import type { EdmSocialMediaDataFragment } from '../fragments/EdmSocialMediaData';
import type { ModuleInDealerSpecs_AdyenPaymentModule_Fragment, ModuleInDealerSpecs_AppointmentModule_Fragment, ModuleInDealerSpecs_AutoplayModule_Fragment, ModuleInDealerSpecs_BankModule_Fragment, ModuleInDealerSpecs_BasicSigningModule_Fragment, ModuleInDealerSpecs_CapModule_Fragment, ModuleInDealerSpecs_ConfiguratorModule_Fragment, ModuleInDealerSpecs_ConsentsAndDeclarationsModule_Fragment, ModuleInDealerSpecs_CtsModule_Fragment, ModuleInDealerSpecs_DocusignModule_Fragment, ModuleInDealerSpecs_EventApplicationModule_Fragment, ModuleInDealerSpecs_FinderApplicationPrivateModule_Fragment, ModuleInDealerSpecs_FinderApplicationPublicModule_Fragment, ModuleInDealerSpecs_FinderVehicleManagementModule_Fragment, ModuleInDealerSpecs_FiservPaymentModule_Fragment, ModuleInDealerSpecs_GiftVoucherModule_Fragment, ModuleInDealerSpecs_InsuranceModule_Fragment, ModuleInDealerSpecs_LabelsModule_Fragment, ModuleInDealerSpecs_LaunchPadModule_Fragment, ModuleInDealerSpecs_LocalCustomerManagementModule_Fragment, ModuleInDealerSpecs_MaintenanceModule_Fragment, ModuleInDealerSpecs_MarketingModule_Fragment, ModuleInDealerSpecs_MobilityModule_Fragment, ModuleInDealerSpecs_MyInfoModule_Fragment, ModuleInDealerSpecs_NamirialSigningModule_Fragment, ModuleInDealerSpecs_OidcModule_Fragment, ModuleInDealerSpecs_PayGatePaymentModule_Fragment, ModuleInDealerSpecs_PorscheIdModule_Fragment, ModuleInDealerSpecs_PorscheMasterDataModule_Fragment, ModuleInDealerSpecs_PorschePaymentModule_Fragment, ModuleInDealerSpecs_PorscheRetainModule_Fragment, ModuleInDealerSpecs_PromoCodeModule_Fragment, ModuleInDealerSpecs_SalesControlBoardModule_Fragment, ModuleInDealerSpecs_SalesOfferModule_Fragment, ModuleInDealerSpecs_SimpleVehicleManagementModule_Fragment, ModuleInDealerSpecs_StandardApplicationModule_Fragment, ModuleInDealerSpecs_TradeInModule_Fragment, ModuleInDealerSpecs_TtbPaymentModule_Fragment, ModuleInDealerSpecs_UserlikeChatbotModule_Fragment, ModuleInDealerSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleInDealerSpecs_VisitAppointmentModule_Fragment, ModuleInDealerSpecs_WebsiteModule_Fragment, ModuleInDealerSpecs_WhatsappLiveChatModule_Fragment } from '../fragments/ModuleInDealerSpecs';
import type { StandardApplicationModuleInDealerSpecsFragment } from '../fragments/StandardApplicationModuleInDealerSpecs';
import type { StandardApplicationModuleEmailContentsSpecsFragment, StandardApplicationModuleEmailContentCustomerSpecsFragment, StandardApplicationModuleEmailContentShareSubmissionSpecsFragment, StandardApplicationModuleEmailContentSpecsFragment, StandardApplicationModuleEmailContentSalesPersonSpecsFragment } from '../fragments/StandardApplicationModuleEmailContentsSpecs';
import type { DealerUploadedFileWithPreviewDataFragment } from '../fragments/DealerUploadedFileWithPreview';
import type { EventApplicationModuleInDealerSpecsFragment } from '../fragments/EventApplicationModuleInDealerSpecs';
import type { EventApplicationModuleEmailContentSpecsFragment } from '../fragments/EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { ConfiguratorModuleInDealerSpecsFragment } from '../fragments/ConfiguratorModuleInDealerSpecs';
import type { ConfiguratorModuleEmailContentSpecsFragment } from '../fragments/ConfiguratorModuleEmailContentSpecs';
import type { MobilityModuleInDealerSpecsFragment } from '../fragments/MobilityModuleInDealerSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from '../fragments/MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from '../fragments/MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from '../fragments/MobilityOperatorEmailContentData';
import type { DealerBookingCodeSpecsFragment } from '../fragments/DealerBookingCodeSpecs';
import type { MobilityHomeDeliveryDataFragment } from '../fragments/MobilityHomeDeliveryData';
import type { FinderApplicationPublicModuleInDealerSpecsFragment } from '../fragments/FinderApplicationPublicModuleInDealerSpecs';
import type { FinderApplicationModuleEmailContentSpecsFragment } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import type { FinderApplicationPrivateModuleInDealerSpecsFragment } from '../fragments/FinderApplicationPrivateModuleInDealerSpecs';
import type { AppointmentModuleInDealerSpecsFragment } from '../fragments/AppointmentModuleInDealerSpecs';
import type { VisitAppointmentModuleInDealerSpecsFragment } from '../fragments/VisitAppointmentModuleInDealerSpecs';
import type { GiftVoucherModuleInDealerSpecsFragment } from '../fragments/GiftVoucherModuleInDealerSpecs';
import type { GiftVoucherModuleEmailContentsSpecsFragment, GiftVoucherModuleEmailContentCustomerSpecsFragment, GiftVoucherModuleEmailDataFragment } from '../fragments/GiftVoucherModuleEmailContentsSpecs';
import type { LaunchPadModuleInDealerSpecsFragment } from '../fragments/LaunchPadModuleInDealerSpecs';
import type { SalesOfferModuleInDealerSpecsFragment } from '../fragments/SalesOfferModuleInDealerSpecs';
import type { SalesControlBoardModuleInDealerSpecsFragment } from '../fragments/SalesControlBoardModuleInDealerSpecs';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from '../fragments/DealerIntData';
import type { GiftPromoTypeDataFragment } from '../fragments/GiftPromoTypeData';
import type { DiscountPromoTypeDataFragment } from '../fragments/DiscountPromoTypeData';
import type { EndpointContextData_ApplicationListEndpoint_Fragment, EndpointContextData_ConfiguratorApplicationEntrypoint_Fragment, EndpointContextData_CustomerListEndpoint_Fragment, EndpointContextData_DummyPrivatePageEndpoint_Fragment, EndpointContextData_DummyWelcomePageEndpoint_Fragment, EndpointContextData_EventApplicationEntrypoint_Fragment, EndpointContextData_FinderApplicationEntrypoint_Fragment, EndpointContextData_FinderApplicationPublicAccessEntrypoint_Fragment, EndpointContextData_LaunchPadApplicationEntrypoint_Fragment, EndpointContextData_LeadListEndpoint_Fragment, EndpointContextData_MobilityApplicationEntrypoint_Fragment, EndpointContextData_StandardApplicationEntrypoint_Fragment, EndpointContextData_StandardApplicationPublicAccessEntrypoint_Fragment, EndpointContextData_WebPageEndpoint_Fragment } from '../fragments/EndpointContextData';
import type { DummyPrivatePageEndpointContextDataFragment } from '../fragments/DummyPrivatePageEndpointContextData';
import type { StandardApplicationEntrypointContextDataFragment } from '../fragments/StandardApplicationEntrypointContextData';
import type { InsurerEntrypointContextDataFragment } from '../fragments/InsurerEntrypointContextData';
import type { ApplicationListEndpointContextDataFragment } from '../fragments/ApplicationListEndpointContextData';
import type { LeadListEndpointContextDataFragment } from '../fragments/LeadListEndpointContextData';
import type { EventApplicationEntrypointContextDataFragment } from '../fragments/EventApplicationEntrypointContextData';
import type { LaunchPadApplicationEntrypointContextDataFragment } from '../fragments/LaunchPadApplicationEntrypointContextData';
import type { ConfiguratorApplicationEntrypointContextDataFragment } from '../fragments/ConfiguratorApplicationEntrypointContextData';
import type { MyInfoSettingSpecFragment } from '../fragments/MyInfoSettingSpec';
import type { CustomerListEndpointContextDataFragment } from '../fragments/CustomerListEndpointContextData';
import type { MobilityApplicationEntrypointContextDataFragment } from '../fragments/MobilityApplicationEntrypointContextData';
import type { DealerBookingCodeDataFragment } from '../fragments/DealerBookingCodeData';
import type { DateUnitDataFragment } from '../fragments/DateUnitData';
import type { WebpageEndpointContextDataFragment } from '../fragments/WebpageEndpointContextData';
import type { WebPageEndpointSpecsFragment } from '../fragments/WebPageEndpointSpecs';
import type { WebPagePathDataFragment } from '../fragments/WebPagePathData';
import type { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../fragments/StandardApplicationPublicAccessEntrypointContextData';
import type { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../fragments/FinderApplicationPublicAccessEntrypointContextData';
import type { EntrypointFinderApplicationPublicModuleFragment } from '../fragments/EntrypointFinderApplicationPublicModule';
import type { ModuleDisclaimersDataFragment } from '../fragments/ModuleDisclaimersData';
import type { FinderApplicationEntrypointContextDataFragment } from '../fragments/FinderApplicationEntrypointContextData';
import type { EntrypointFinderApplicationPrivateModuleFragment } from '../fragments/EntrypointFinderApplicationPrivateModule';
import type { ApplicationQuotationDataFragment, ApplicationQuotationOptionDataFragment } from '../fragments/ApplicationQuotationData';
import type { EventApplicationDataFragment } from '../fragments/EventApplicationData';
import type { EventApplicationConfigurationDataFragment } from '../fragments/EventApplicationConfigurationData';
import type { CustomTestDriveBookingSlotsDataFragment } from '../fragments/CustomTestDriveBookingSlotsData';
import type { TestDriveFixedPeriodDataFragment } from '../fragments/TestDriveFixedPeriodData';
import type { TestDriveBookingWindowSettingsDataFragment } from '../fragments/TestDriveBookingWindowSettingsData';
import type { ConfiguratorApplicationDataFragment } from '../fragments/ConfiguratorApplicationData';
import type { ConfiguratorApplicationConfigurationDataFragment } from '../fragments/ConfiguratorApplicationConfigurationData';
import type { VariantConfiguratorJourneyDataFragment } from '../fragments/VariantConfiguratorJourneyData';
import type { LocalVariantPublicSpecsFragment } from '../fragments/LocalVariantPublicSpecs';
import type { LocalModelPublicSpecsFragment } from '../fragments/LocalModelPublicSpecs';
import type { LocalMakePublicSpecsFragment } from '../fragments/LocalMakePublicSpecs';
import type { MatrixDataFragment } from '../fragments/MatrixData';
import type { InventoryDetailsPublicData_ConfiguratorInventory_Fragment, InventoryDetailsPublicData_MobilityInventory_Fragment } from '../fragments/InventoryDetailsPublicData';
import type { StockInventorySpecs_ConfiguratorStockInventory_Fragment, StockInventorySpecs_MobilityStockInventory_Fragment } from '../fragments/StockInventorySpecs';
import type { CompanyPublicSpecsFragment } from '../fragments/CompanyPublicSpecs';
import type { StockBlockingPeriodDataFragment } from '../fragments/StockBlockingPeriod';
import type { ConfiguratorInventoryPublicSpecsFragment } from '../fragments/ConfiguratorInventoryPublicSpecs';
import type { MobilityInventoryPublicSpecsFragment } from '../fragments/MobilityInventoryPublicSpecs';
import type { MobilityModuleSpecsFragment } from '../fragments/MobilityModuleSpecs';
import type { MobilitySigningSettingSpecsFragment } from '../fragments/MobilitySigningSettingSpecs';
import type { MobilityApplicationDataFragment } from '../fragments/MobilityApplicationData';
import type { MobilityApplicationModuleDataFragment } from '../fragments/MobilityApplicationModuleData';
import type { MobilitySnapshotData_MobilityAdditionalInfoSnapshot_Fragment, MobilitySnapshotData_MobilityAddonSnapshot_Fragment } from '../fragments/MobilitySnapshotData';
import type { MobilityBookingDetailsDataFragment } from '../fragments/MobilityBookingDetailsData';
import type { MobilityBookingLocationHomeDataFragment } from '../fragments/MobilityBookingLocationHomeData';
import type { MobilityBookingLocationPickupDataFragment } from '../fragments/MobilityBookingLocationPickupData';
import type { GiftVoucherDataFragment } from '../fragments/GiftVoucherData';
import type { ModuleSpecs_AdyenPaymentModule_Fragment, ModuleSpecs_AppointmentModule_Fragment, ModuleSpecs_AutoplayModule_Fragment, ModuleSpecs_BankModule_Fragment, ModuleSpecs_BasicSigningModule_Fragment, ModuleSpecs_CapModule_Fragment, ModuleSpecs_ConfiguratorModule_Fragment, ModuleSpecs_ConsentsAndDeclarationsModule_Fragment, ModuleSpecs_CtsModule_Fragment, ModuleSpecs_DocusignModule_Fragment, ModuleSpecs_EventApplicationModule_Fragment, ModuleSpecs_FinderApplicationPrivateModule_Fragment, ModuleSpecs_FinderApplicationPublicModule_Fragment, ModuleSpecs_FinderVehicleManagementModule_Fragment, ModuleSpecs_FiservPaymentModule_Fragment, ModuleSpecs_GiftVoucherModule_Fragment, ModuleSpecs_InsuranceModule_Fragment, ModuleSpecs_LabelsModule_Fragment, ModuleSpecs_LaunchPadModule_Fragment, ModuleSpecs_LocalCustomerManagementModule_Fragment, ModuleSpecs_MaintenanceModule_Fragment, ModuleSpecs_MarketingModule_Fragment, ModuleSpecs_MobilityModule_Fragment, ModuleSpecs_MyInfoModule_Fragment, ModuleSpecs_NamirialSigningModule_Fragment, ModuleSpecs_OidcModule_Fragment, ModuleSpecs_PayGatePaymentModule_Fragment, ModuleSpecs_PorscheIdModule_Fragment, ModuleSpecs_PorscheMasterDataModule_Fragment, ModuleSpecs_PorschePaymentModule_Fragment, ModuleSpecs_PorscheRetainModule_Fragment, ModuleSpecs_PromoCodeModule_Fragment, ModuleSpecs_SalesControlBoardModule_Fragment, ModuleSpecs_SalesOfferModule_Fragment, ModuleSpecs_SimpleVehicleManagementModule_Fragment, ModuleSpecs_StandardApplicationModule_Fragment, ModuleSpecs_TradeInModule_Fragment, ModuleSpecs_TtbPaymentModule_Fragment, ModuleSpecs_UserlikeChatbotModule_Fragment, ModuleSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleSpecs_VisitAppointmentModule_Fragment, ModuleSpecs_WebsiteModule_Fragment, ModuleSpecs_WhatsappLiveChatModule_Fragment } from '../fragments/ModuleSpecs';
import type { ConsentsAndDeclarationsModuleSpecsFragment } from '../fragments/ConsentsAndDeclarationsModuleSpecs';
import type { SimpleVehicleManagementModuleSpecsFragment } from '../fragments/SimpleVehicleManagementModuleSpecs';
import type { LocalCustomerManagementModuleSpecsFragment } from '../fragments/LocalCustomerManagementModuleSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import type { KycPresetsSpecFragment } from '../fragments/KYCPresetsSpec';
import type { BankModuleSpecsFragment } from '../fragments/BankModuleSpecs';
import type { BasicSigningModuleSpecsFragment } from '../fragments/BasicSigningModuleSpecs';
import type { StandardApplicationModuleSpecsFragment } from '../fragments/StandardApplicationModuleSpecs';
import type { EventApplicationModuleSpecsFragment } from '../fragments/EventApplicationModuleSpecs';
import type { AdyenPaymentModuleSpecsFragment } from '../fragments/AdyenPaymentModuleSpecs';
import type { AdyenPaymentSettingsSpecFragment } from '../fragments/AdyenPaymentSettingsSpec';
import type { PorschePaymentModuleSpecsFragment } from '../fragments/PorschePaymentModuleSpecs';
import type { PorschePaymentSettingsSpecFragment } from '../fragments/PorschePaymentSettingsSpec';
import type { FiservPaymentModuleSpecsFragment } from '../fragments/FiservPaymentModuleSpecs';
import type { FiservPaymentSettingsSpecFragment } from '../fragments/FiservPaymentSettingsSpec';
import type { PayGatePaymentModuleSpecsFragment } from '../fragments/PayGatePaymentModuleSpecs';
import type { PayGatePaymentSettingsSpecFragment } from '../fragments/PayGatePaymentSettingsSpec';
import type { TtbPaymentModuleSpecsFragment } from '../fragments/TtbPaymentModuleSpecs';
import type { TtbPaymentSettingsSpecFragment } from '../fragments/TtbPaymentSettingsSpec';
import type { MyInfoModuleSpecsFragment } from '../fragments/MyInfoModuleSpecs';
import type { ConfiguratorModuleSpecsFragment } from '../fragments/ConfiguratorModuleSpecs';
import type { WhatsappLiveChatModuleSpecsFragment } from '../fragments/WhatsappLiveChatModuleSpecs';
import type { WhatsappLiveChatSettingsSpecFragment } from '../fragments/WhatsappLiveChatSettingsSpec';
import type { UserlikeChatbotModuleSpecsFragment } from '../fragments/UserlikeChatbotModuleSpecs';
import type { UserlikeChatbotSettingsSpecFragment } from '../fragments/UserlikeChatbotSettingsSpec';
import type { PromoCodeModuleSpecsFragment } from '../fragments/PromoCodeModuleSpecs';
import type { MaintenanceModuleSpecsFragment } from '../fragments/MaintenanceModuleSpecs';
import type { WebsiteModuleSpecsFragment } from '../fragments/WebsiteModuleSpecs';
import type { LabelsModuleSpecsFragment } from '../fragments/LabelsModuleSpecs';
import type { FinderVehicleManagementModuleSpecsFragment } from '../fragments/FinderVehicleManagementModuleSpecs';
import type { FinderApplicationPublicModuleSpecsFragment } from '../fragments/FinderApplicationPublicModuleSpecs';
import type { FinderApplicationPrivateModuleSpecsFragment } from '../fragments/FinderApplicationPrivateModuleSpecs';
import type { AutoplayModuleSpecsFragment } from '../fragments/AutoplayModuleSpecs';
import type { AutoplaySettingSpecsFragment } from '../fragments/AutoplaySettingSpecs';
import type { CtsModuleSpecsFragment } from '../fragments/CtsModuleSpecs';
import type { CtsModuleSettingDataFragment } from '../fragments/CtsModuleSettingData';
import type { InsuranceModuleSpecsFragment } from '../fragments/InsuranceModuleSpecs';
import type { PorscheMasterDataModuleSpecsFragment } from '../fragments/PorscheMasterDataModuleSpecs';
import type { GiftVoucherModuleSpecsFragment } from '../fragments/GiftVoucherModuleSpecs';
import type { TradeInModuleSpecsFragment } from '../fragments/TradeInModuleSpecs';
import type { TradeInSettingSpecFragment } from '../fragments/TradeInSetting';
import type { CapModuleSpecsFragment } from '../fragments/CapModuleSpecs';
import type { CapSettingSpecFragment } from '../fragments/CapSettingSpec';
import type { PorscheIdModuleSpecsFragment } from '../fragments/PorscheIdModuleSpecs';
import type { PorscheIdSettingSpecFragment } from '../fragments/PorscheIdSettingSpec';
import type { PorscheRetainModuleSpecsFragment } from '../fragments/PorscheRetainModuleSpecs';
import type { DocusignModuleSpecsFragment } from '../fragments/DocusignModuleSpecs';
import type { DocusignSettingDataFragment } from '../fragments/DocusignSettingData';
import type { OidcModuleSpecsFragment } from '../fragments/OIDCModuleSpecs';
import type { MarketingModuleSpecsFragment } from '../fragments/MarketingModuleSpecs';
import type { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import type { SalesControlBoardModuleSpecsFragment } from '../fragments/SalesControlBoardModuleSpecs';
import type { MobilityModuleGiftVoucherDataFragment } from '../fragments/MobilityModuleGiftVoucherData';
import type { GiftVoucherDraftFlowDataFragment } from '../fragments/GiftVoucherDraftFlowData';
import type { DealerJourneyDataFragment } from '../fragments/DealerJourneyData';
import type { MobilityStockGiftVoucherDataFragment } from '../fragments/MobilityStockGiftVoucherData';
import type { FinderApplicationDataFragment } from '../fragments/FinderApplicationData';
import type { FinderConfigurationDataFragment } from '../fragments/FinderConfigurationData';
import type { LaunchpadApplicationDataFragment } from '../fragments/LaunchpadApplicationData';
import type { LaunchpadApplicationConfigurationDataFragment } from '../fragments/LaunchpadApplicationConfigurationData';
import type { SalesOfferApplicationDataFragment } from '../fragments/SalesOfferApplicationData';
import type { SalesOfferApplicationConfigurationDataFragment } from '../fragments/SalesOfferApplicationConfigurationData';
import { gql } from '@apollo/client';
import { ApplicationDataFragmentDoc } from '../fragments/ApplicationData';
import { ApplicationStageDataFragmentDoc } from '../fragments/ApplicationStageData';
import { EventApplicationModuleSpecsForApplicationFragmentDoc } from '../fragments/EventApplicationModuleSpecsForApplication';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { KycExtraSettingsSpecsFragmentDoc } from '../fragments/KYCExtraSettingsSpecs';
import { StandardApplicationModuleSpecsForApplicationFragmentDoc } from '../fragments/StandardApplicationModuleSpecsForApplication';
import { DepositAmountDataFragmentDoc } from '../fragments/DepositAmountData';
import { ApplicationMarketTypeFragmentFragmentDoc } from '../fragments/ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from '../fragments/DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from '../fragments/BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from '../fragments/NzFeesDealerMarketData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { DealershipSettingSpecDataFragmentDoc } from '../fragments/DealershipSettingSpecData';
import { FlexibleDiscountDataFragmentDoc } from '../fragments/FlexibleDiscountData';
import { DealerPriceDisclaimerDataFragmentDoc } from '../fragments/DealerPriceDisclaimerData';
import { ConfiguratorModuleSpecsForApplicationFragmentDoc } from '../fragments/ConfiguratorModuleSpecsForApplication';
import { FinderApplicationPublicModuleSpecsForApplicationFragmentDoc } from '../fragments/FinderApplicationPublicModuleSpecsForApplication';
import { KycPresetsOptionsDataFragmentDoc } from '../fragments/KYCPresetsOptionsData';
import { FinderApplicationPrivateModuleSpecsForApplicationFragmentDoc } from '../fragments/FinderApplicationPrivateModuleSpecsForApplication';
import { LaunchpadModuleSpecsForApplicationFragmentDoc } from '../fragments/LaunchpadModuleSpecsForApplication';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { LeadDataFragmentDoc } from '../fragments/LeadData';
import { StandardLeadDataFragmentDoc } from '../fragments/StandardLeadData';
import { VehicleSpecsFragmentDoc } from '../fragments/VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from '../fragments/LocalVariantSpecs';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { FinderVehicleSpecsFragmentDoc } from '../fragments/FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from '../fragments/finderListing.fragment';
import { FinderLeadDataFragmentDoc } from '../fragments/FinderLeadData';
import { EventLeadDataFragmentDoc } from '../fragments/EventLeadData';
import { ApplicationEventCustomizedFieldDataFragmentDoc } from '../fragments/ApplicationEventCustomizedFieldData';
import { LaunchpadLeadDataFragmentDoc } from '../fragments/LaunchpadLeadData';
import { ConfiguratorLeadDataFragmentDoc } from '../fragments/ConfiguratorLeadData';
import { ConfiguratorJourneyBlocksDataFragmentDoc } from '../fragments/ConfiguratorJourneyBlocksData';
import { BlockDetailsFragmentDoc } from '../fragments/BlockDetails';
import { OptionSettingDetailsFragmentDoc } from '../fragments/OptionSettingDetails';
import { MobilityLeadDataFragmentDoc } from '../fragments/MobilityLeadData';
import { CustomerSpecsFragmentDoc } from '../fragments/CustomerSpecs';
import { LocalCustomerDataFragmentDoc } from '../fragments/LocalCustomerData';
import { LocalCustomerFieldDataFragmentDoc } from '../fragments/LocalCustomerFieldData';
import { CorporateCustomerDataFragmentDoc } from '../fragments/CorporateCustomerData';
import { GuarantorDataFragmentDoc } from '../fragments/GuarantorData';
import { UsersOptionsDataFragmentDoc } from '../fragments/UsersOptionsData';
import { KycFieldSpecsFragmentDoc } from '../fragments/KYCFieldSpecs';
import { LaunchPadModuleSpecsFragmentDoc } from '../fragments/LaunchPadModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from '../fragments/CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { AppointmentModuleSpecsFragmentDoc } from '../fragments/AppointmentModuleSpecs';
import { AppointmentTimeSlotDataFragmentDoc } from '../fragments/AppointmentTimeSlotData';
import { NamirialSigningModuleSpecsFragmentDoc } from '../fragments/NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from '../fragments/NamirialSettingsSpec';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from '../fragments/AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { DealerBooleanSettingDataFragmentDoc } from '../fragments/DealerBooleanSettingData';
import { VisitAppointmentModuleSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleSpecs';
import { TimeSlotDataFragmentDoc } from '../fragments/TimeSlotData';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import { CounterSettingsSpecsFragmentDoc } from '../fragments/CounterSettingsSpecs';
import { DealerVehiclesSpecsFragmentDoc } from '../fragments/DealerVehiclesSpecs';
import { ApplicationAgreementDataFragmentDoc } from '../fragments/ApplicationAgreementData';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { MarketingPlatformSpecsFragmentDoc } from '../fragments/MarketingPlatformSpecs';
import { MarketingPlatformsAgreedSpecsFragmentDoc } from '../fragments/MarketingPlatformsAgreedSpecs';
import { TradeInVehicleDataFragmentDoc } from '../fragments/TradeInVehicleData';
import { DealerApplicationFragmentFragmentDoc } from '../fragments/DealerApplicationFragment';
import { DealerContactFragmentFragmentDoc } from '../fragments/DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from '../fragments/DealerSocialMediaFragment';
import { DealerDisclaimersFragmentFragmentDoc } from '../fragments/DealerDisclaimersFragment';
import { DealerIntegrationDetailsFragmentFragmentDoc } from '../fragments/DealerIntegrationDetailsFragment';
import { ApplicationDocumentDataFragmentDoc } from '../fragments/ApplicationDocumentData';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from '../fragments/ReferenceApplicationData';
import { SalesOfferSpecsFragmentDoc } from '../fragments/SalesOfferSpecs';
import { VehicleSalesOfferSpecsFragmentDoc } from '../fragments/VehicleSalesOfferSpecs';
import { PorscheVehicleDataSpecsFragmentDoc, PorscheVehicleDataFeatureSpecsFragmentDoc, PorscheVehicleImagesSpecsFragmentDoc } from '../fragments/PorscheVehicleDataSpecs';
import { LocalFittedOptionsSpecsFragmentDoc } from '../fragments/LocalFittedOptionsSpecs';
import { SalesOfferDocumentDataFragmentDoc } from '../fragments/SalesOfferDocumentData';
import { MainDetailsSalesOfferSpecsFragmentDoc } from '../fragments/MainDetailsSalesOfferSpecs';
import { TradeInSalesOfferSpecsFragmentDoc } from '../fragments/TradeInSalesOfferSpecs';
import { FinanceSalesOfferSpecsFragmentDoc } from '../fragments/FinanceSalesOfferSpecs';
import { ApplicationFinancingDataFragmentDoc } from '../fragments/ApplicationFinancingData';
import { InsuranceSalesOfferSpecsFragmentDoc } from '../fragments/InsuranceSalesOfferSpecs';
import { ApplicationInsurancingDataFragmentDoc } from '../fragments/ApplicationInsurancingData';
import { DepositSalesOfferSpecsFragmentDoc } from '../fragments/DepositSalesOfferSpecs';
import { VsaSalesOfferSpecsFragmentDoc } from '../fragments/VSASalesOfferSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from '../fragments/SalesOfferModuleSpecs';
import { BankDetailsDataFragmentDoc } from '../fragments/BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from '../fragments/BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from '../fragments/UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from '../fragments/FinanceProductDetailsData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from '../fragments/VehicleReferenceParametersData';
import { PaymentSettingsDetailsFragmentDoc } from '../fragments/PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from '../fragments/LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from '../fragments/TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from '../fragments/InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from '../fragments/DownPaymentSettingsDetails';
import { BalloonSettingsDetailsFragmentDoc } from '../fragments/BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from '../fragments/BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from '../fragments/LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from '../fragments/DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from '../fragments/ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from '../fragments/LocalUcclLeasingOnlyDetails';
import { DealerFinanceProductsSpecsFragmentDoc } from '../fragments/DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from '../fragments/FinanceProductListData';
import { DealerInsuranceProductsSpecsFragmentDoc } from '../fragments/DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from '../fragments/InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from '../fragments/ErgoLookupTableSettingDetails';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from '../fragments/SalesOfferModuleEmailContentsSpecs';
import { SalesOfferKycPresetSpecsFragmentDoc } from '../fragments/SalesOfferKYCPresetSpecs';
import { SalesOfferConsentsSpecsFragmentDoc } from '../fragments/SalesOfferConsentsSpecs';
import { SalesOfferSigningsSpecsFragmentDoc } from '../fragments/SalesOfferSigningsSpecs';
import { NamirialSigningDataFragmentDoc } from '../fragments/NamirialSigningData';
import { StandardApplicationDataFragmentDoc } from '../fragments/StandardApplicationData';
import { DraftFlowConfigurationSpecFragmentDoc } from '../fragments/DraftFlowConfigurationSpec';
import { InsuranceProductDetailsDataFragmentDoc } from '../fragments/InsuranceProductDetailsData';
import { ApplicationConfigurationDataFragmentDoc } from '../fragments/ApplicationConfigurationData';
import { FinanceProductDetailsFragmentDoc } from '../fragments/FinanceProductDetails';
import { LocalHirePurchaseDetailsFragmentDoc } from '../fragments/LocalHirePurchaseDetails';
import { LocalHirePurchaseWithBalloonDetailsFragmentDoc } from '../fragments/LocalHirePurchaseWithBalloonDetails';
import { LocalHirePurchaseWithBalloonGfvDetailsFragmentDoc } from '../fragments/LocalHirePurchaseWithBalloonGFVDetails';
import { LocalLeaseDetailsFragmentDoc } from '../fragments/LocalLeaseDetails';
import { LocalLeasePurchaseDetailsFragmentDoc } from '../fragments/LocalLeasePurchaseDetails';
import { LocalDeferredPrincipalDetailsFragmentDoc } from '../fragments/LocalDeferredPrincipalDetails';
import { LocalUcclLeasingDetailsFragmentDoc } from '../fragments/LocalUcclLeasingDetails';
import { ApplicationAdyenDepositDataFragmentDoc } from '../fragments/ApplicationAdyenDepositData';
import { ApplicationPorscheDepositDataFragmentDoc } from '../fragments/ApplicationPorscheDepositData';
import { ApplicationFiservDepositDataFragmentDoc } from '../fragments/ApplicationFiservDepositData';
import { ApplicationPayGateDepositDataFragmentDoc } from '../fragments/ApplicationPayGateDepositData';
import { ApplicationTtbDepositDataFragmentDoc } from '../fragments/ApplicationTtbDepositData';
import { PromoCodeDataFragmentDoc } from '../fragments/PromoCodeData';
import { DealerFragmentFragmentDoc } from '../fragments/DealerFragment';
import { CompanyContextDataFragmentDoc } from '../fragments/CompanyContextData';
import { LanguagePackContextDataFragmentDoc } from '../fragments/LanguagePackContextData';
import { AvailableModulesDataFragmentDoc } from '../fragments/AvailableModulesData';
import { CompanyDealerDataFragmentDoc } from '../fragments/CompanyDealerData';
import { MaintenanceUpdateFragmentDoc } from '../fragments/MaintenanceUpdate';
import { UserAvatarSpecsFragmentDoc } from '../fragments/UserAvatarSpecs';
import { EdmEmailFooterPublicDataFragmentDoc } from '../fragments/EdmEmailFooterPublicData';
import { EdmSocialMediaDataFragmentDoc } from '../fragments/EdmSocialMediaData';
import { ModuleInDealerSpecsFragmentDoc } from '../fragments/ModuleInDealerSpecs';
import { StandardApplicationModuleInDealerSpecsFragmentDoc } from '../fragments/StandardApplicationModuleInDealerSpecs';
import { StandardApplicationModuleEmailContentsSpecsFragmentDoc, StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc, StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc, StandardApplicationModuleEmailContentSpecsFragmentDoc, StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc } from '../fragments/StandardApplicationModuleEmailContentsSpecs';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from '../fragments/DealerUploadedFileWithPreview';
import { EventApplicationModuleInDealerSpecsFragmentDoc } from '../fragments/EventApplicationModuleInDealerSpecs';
import { EventApplicationModuleEmailContentSpecsFragmentDoc } from '../fragments/EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { ConfiguratorModuleInDealerSpecsFragmentDoc } from '../fragments/ConfiguratorModuleInDealerSpecs';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from '../fragments/ConfiguratorModuleEmailContentSpecs';
import { MobilityModuleInDealerSpecsFragmentDoc } from '../fragments/MobilityModuleInDealerSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from '../fragments/MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from '../fragments/MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from '../fragments/MobilityOperatorEmailContentData';
import { DealerBookingCodeSpecsFragmentDoc } from '../fragments/DealerBookingCodeSpecs';
import { MobilityHomeDeliveryDataFragmentDoc } from '../fragments/MobilityHomeDeliveryData';
import { FinderApplicationPublicModuleInDealerSpecsFragmentDoc } from '../fragments/FinderApplicationPublicModuleInDealerSpecs';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import { FinderApplicationPrivateModuleInDealerSpecsFragmentDoc } from '../fragments/FinderApplicationPrivateModuleInDealerSpecs';
import { AppointmentModuleInDealerSpecsFragmentDoc } from '../fragments/AppointmentModuleInDealerSpecs';
import { VisitAppointmentModuleInDealerSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleInDealerSpecs';
import { GiftVoucherModuleInDealerSpecsFragmentDoc } from '../fragments/GiftVoucherModuleInDealerSpecs';
import { GiftVoucherModuleEmailContentsSpecsFragmentDoc, GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc, GiftVoucherModuleEmailDataFragmentDoc } from '../fragments/GiftVoucherModuleEmailContentsSpecs';
import { LaunchPadModuleInDealerSpecsFragmentDoc } from '../fragments/LaunchPadModuleInDealerSpecs';
import { SalesOfferModuleInDealerSpecsFragmentDoc } from '../fragments/SalesOfferModuleInDealerSpecs';
import { SalesControlBoardModuleInDealerSpecsFragmentDoc } from '../fragments/SalesControlBoardModuleInDealerSpecs';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from '../fragments/DealerIntData';
import { GiftPromoTypeDataFragmentDoc } from '../fragments/GiftPromoTypeData';
import { DiscountPromoTypeDataFragmentDoc } from '../fragments/DiscountPromoTypeData';
import { EndpointContextDataFragmentDoc } from '../fragments/EndpointContextData';
import { DummyPrivatePageEndpointContextDataFragmentDoc } from '../fragments/DummyPrivatePageEndpointContextData';
import { StandardApplicationEntrypointContextDataFragmentDoc } from '../fragments/StandardApplicationEntrypointContextData';
import { InsurerEntrypointContextDataFragmentDoc } from '../fragments/InsurerEntrypointContextData';
import { ApplicationListEndpointContextDataFragmentDoc } from '../fragments/ApplicationListEndpointContextData';
import { LeadListEndpointContextDataFragmentDoc } from '../fragments/LeadListEndpointContextData';
import { EventApplicationEntrypointContextDataFragmentDoc } from '../fragments/EventApplicationEntrypointContextData';
import { LaunchPadApplicationEntrypointContextDataFragmentDoc } from '../fragments/LaunchPadApplicationEntrypointContextData';
import { ConfiguratorApplicationEntrypointContextDataFragmentDoc } from '../fragments/ConfiguratorApplicationEntrypointContextData';
import { MyInfoSettingSpecFragmentDoc } from '../fragments/MyInfoSettingSpec';
import { CustomerListEndpointContextDataFragmentDoc } from '../fragments/CustomerListEndpointContextData';
import { MobilityApplicationEntrypointContextDataFragmentDoc } from '../fragments/MobilityApplicationEntrypointContextData';
import { DealerBookingCodeDataFragmentDoc } from '../fragments/DealerBookingCodeData';
import { DateUnitDataFragmentDoc } from '../fragments/DateUnitData';
import { WebpageEndpointContextDataFragmentDoc } from '../fragments/WebpageEndpointContextData';
import { WebPageEndpointSpecsFragmentDoc } from '../fragments/WebPageEndpointSpecs';
import { WebPagePathDataFragmentDoc } from '../fragments/WebPagePathData';
import { StandardApplicationPublicAccessEntrypointContextDataFragmentDoc } from '../fragments/StandardApplicationPublicAccessEntrypointContextData';
import { FinderApplicationPublicAccessEntrypointContextDataFragmentDoc } from '../fragments/FinderApplicationPublicAccessEntrypointContextData';
import { EntrypointFinderApplicationPublicModuleFragmentDoc } from '../fragments/EntrypointFinderApplicationPublicModule';
import { ModuleDisclaimersDataFragmentDoc } from '../fragments/ModuleDisclaimersData';
import { FinderApplicationEntrypointContextDataFragmentDoc } from '../fragments/FinderApplicationEntrypointContextData';
import { EntrypointFinderApplicationPrivateModuleFragmentDoc } from '../fragments/EntrypointFinderApplicationPrivateModule';
import { ApplicationQuotationDataFragmentDoc, ApplicationQuotationOptionDataFragmentDoc } from '../fragments/ApplicationQuotationData';
import { EventApplicationDataFragmentDoc } from '../fragments/EventApplicationData';
import { EventApplicationConfigurationDataFragmentDoc } from '../fragments/EventApplicationConfigurationData';
import { CustomTestDriveBookingSlotsDataFragmentDoc } from '../fragments/CustomTestDriveBookingSlotsData';
import { TestDriveFixedPeriodDataFragmentDoc } from '../fragments/TestDriveFixedPeriodData';
import { TestDriveBookingWindowSettingsDataFragmentDoc } from '../fragments/TestDriveBookingWindowSettingsData';
import { ConfiguratorApplicationDataFragmentDoc } from '../fragments/ConfiguratorApplicationData';
import { ConfiguratorApplicationConfigurationDataFragmentDoc } from '../fragments/ConfiguratorApplicationConfigurationData';
import { VariantConfiguratorJourneyDataFragmentDoc } from '../fragments/VariantConfiguratorJourneyData';
import { LocalVariantPublicSpecsFragmentDoc } from '../fragments/LocalVariantPublicSpecs';
import { LocalModelPublicSpecsFragmentDoc } from '../fragments/LocalModelPublicSpecs';
import { LocalMakePublicSpecsFragmentDoc } from '../fragments/LocalMakePublicSpecs';
import { MatrixDataFragmentDoc } from '../fragments/MatrixData';
import { InventoryDetailsPublicDataFragmentDoc } from '../fragments/InventoryDetailsPublicData';
import { StockInventorySpecsFragmentDoc } from '../fragments/StockInventorySpecs';
import { CompanyPublicSpecsFragmentDoc } from '../fragments/CompanyPublicSpecs';
import { StockBlockingPeriodDataFragmentDoc } from '../fragments/StockBlockingPeriod';
import { ConfiguratorInventoryPublicSpecsFragmentDoc } from '../fragments/ConfiguratorInventoryPublicSpecs';
import { MobilityInventoryPublicSpecsFragmentDoc } from '../fragments/MobilityInventoryPublicSpecs';
import { MobilityModuleSpecsFragmentDoc } from '../fragments/MobilityModuleSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from '../fragments/MobilitySigningSettingSpecs';
import { MobilityApplicationDataFragmentDoc } from '../fragments/MobilityApplicationData';
import { MobilityApplicationModuleDataFragmentDoc } from '../fragments/MobilityApplicationModuleData';
import { MobilitySnapshotDataFragmentDoc } from '../fragments/MobilitySnapshotData';
import { MobilityBookingDetailsDataFragmentDoc } from '../fragments/MobilityBookingDetailsData';
import { MobilityBookingLocationHomeDataFragmentDoc } from '../fragments/MobilityBookingLocationHomeData';
import { MobilityBookingLocationPickupDataFragmentDoc } from '../fragments/MobilityBookingLocationPickupData';
import { GiftVoucherDataFragmentDoc } from '../fragments/GiftVoucherData';
import { ModuleSpecsFragmentDoc } from '../fragments/ModuleSpecs';
import { ConsentsAndDeclarationsModuleSpecsFragmentDoc } from '../fragments/ConsentsAndDeclarationsModuleSpecs';
import { SimpleVehicleManagementModuleSpecsFragmentDoc } from '../fragments/SimpleVehicleManagementModuleSpecs';
import { LocalCustomerManagementModuleSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import { KycPresetsSpecFragmentDoc } from '../fragments/KYCPresetsSpec';
import { BankModuleSpecsFragmentDoc } from '../fragments/BankModuleSpecs';
import { BasicSigningModuleSpecsFragmentDoc } from '../fragments/BasicSigningModuleSpecs';
import { StandardApplicationModuleSpecsFragmentDoc } from '../fragments/StandardApplicationModuleSpecs';
import { EventApplicationModuleSpecsFragmentDoc } from '../fragments/EventApplicationModuleSpecs';
import { AdyenPaymentModuleSpecsFragmentDoc } from '../fragments/AdyenPaymentModuleSpecs';
import { AdyenPaymentSettingsSpecFragmentDoc } from '../fragments/AdyenPaymentSettingsSpec';
import { PorschePaymentModuleSpecsFragmentDoc } from '../fragments/PorschePaymentModuleSpecs';
import { PorschePaymentSettingsSpecFragmentDoc } from '../fragments/PorschePaymentSettingsSpec';
import { FiservPaymentModuleSpecsFragmentDoc } from '../fragments/FiservPaymentModuleSpecs';
import { FiservPaymentSettingsSpecFragmentDoc } from '../fragments/FiservPaymentSettingsSpec';
import { PayGatePaymentModuleSpecsFragmentDoc } from '../fragments/PayGatePaymentModuleSpecs';
import { PayGatePaymentSettingsSpecFragmentDoc } from '../fragments/PayGatePaymentSettingsSpec';
import { TtbPaymentModuleSpecsFragmentDoc } from '../fragments/TtbPaymentModuleSpecs';
import { TtbPaymentSettingsSpecFragmentDoc } from '../fragments/TtbPaymentSettingsSpec';
import { MyInfoModuleSpecsFragmentDoc } from '../fragments/MyInfoModuleSpecs';
import { ConfiguratorModuleSpecsFragmentDoc } from '../fragments/ConfiguratorModuleSpecs';
import { WhatsappLiveChatModuleSpecsFragmentDoc } from '../fragments/WhatsappLiveChatModuleSpecs';
import { WhatsappLiveChatSettingsSpecFragmentDoc } from '../fragments/WhatsappLiveChatSettingsSpec';
import { UserlikeChatbotModuleSpecsFragmentDoc } from '../fragments/UserlikeChatbotModuleSpecs';
import { UserlikeChatbotSettingsSpecFragmentDoc } from '../fragments/UserlikeChatbotSettingsSpec';
import { PromoCodeModuleSpecsFragmentDoc } from '../fragments/PromoCodeModuleSpecs';
import { MaintenanceModuleSpecsFragmentDoc } from '../fragments/MaintenanceModuleSpecs';
import { WebsiteModuleSpecsFragmentDoc } from '../fragments/WebsiteModuleSpecs';
import { LabelsModuleSpecsFragmentDoc } from '../fragments/LabelsModuleSpecs';
import { FinderVehicleManagementModuleSpecsFragmentDoc } from '../fragments/FinderVehicleManagementModuleSpecs';
import { FinderApplicationPublicModuleSpecsFragmentDoc } from '../fragments/FinderApplicationPublicModuleSpecs';
import { FinderApplicationPrivateModuleSpecsFragmentDoc } from '../fragments/FinderApplicationPrivateModuleSpecs';
import { AutoplayModuleSpecsFragmentDoc } from '../fragments/AutoplayModuleSpecs';
import { AutoplaySettingSpecsFragmentDoc } from '../fragments/AutoplaySettingSpecs';
import { CtsModuleSpecsFragmentDoc } from '../fragments/CtsModuleSpecs';
import { CtsModuleSettingDataFragmentDoc } from '../fragments/CtsModuleSettingData';
import { InsuranceModuleSpecsFragmentDoc } from '../fragments/InsuranceModuleSpecs';
import { PorscheMasterDataModuleSpecsFragmentDoc } from '../fragments/PorscheMasterDataModuleSpecs';
import { GiftVoucherModuleSpecsFragmentDoc } from '../fragments/GiftVoucherModuleSpecs';
import { TradeInModuleSpecsFragmentDoc } from '../fragments/TradeInModuleSpecs';
import { TradeInSettingSpecFragmentDoc } from '../fragments/TradeInSetting';
import { CapModuleSpecsFragmentDoc } from '../fragments/CapModuleSpecs';
import { CapSettingSpecFragmentDoc } from '../fragments/CapSettingSpec';
import { PorscheIdModuleSpecsFragmentDoc } from '../fragments/PorscheIdModuleSpecs';
import { PorscheIdSettingSpecFragmentDoc } from '../fragments/PorscheIdSettingSpec';
import { PorscheRetainModuleSpecsFragmentDoc } from '../fragments/PorscheRetainModuleSpecs';
import { DocusignModuleSpecsFragmentDoc } from '../fragments/DocusignModuleSpecs';
import { DocusignSettingDataFragmentDoc } from '../fragments/DocusignSettingData';
import { OidcModuleSpecsFragmentDoc } from '../fragments/OIDCModuleSpecs';
import { MarketingModuleSpecsFragmentDoc } from '../fragments/MarketingModuleSpecs';
import { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import { SalesControlBoardModuleSpecsFragmentDoc } from '../fragments/SalesControlBoardModuleSpecs';
import { MobilityModuleGiftVoucherDataFragmentDoc } from '../fragments/MobilityModuleGiftVoucherData';
import { GiftVoucherDraftFlowDataFragmentDoc } from '../fragments/GiftVoucherDraftFlowData';
import { DealerJourneyDataFragmentDoc } from '../fragments/DealerJourneyData';
import { MobilityStockGiftVoucherDataFragmentDoc } from '../fragments/MobilityStockGiftVoucherData';
import { FinderApplicationDataFragmentDoc } from '../fragments/FinderApplicationData';
import { FinderConfigurationDataFragmentDoc } from '../fragments/FinderConfigurationData';
import { LaunchpadApplicationDataFragmentDoc } from '../fragments/LaunchpadApplicationData';
import { LaunchpadApplicationConfigurationDataFragmentDoc } from '../fragments/LaunchpadApplicationConfigurationData';
import { SalesOfferApplicationDataFragmentDoc } from '../fragments/SalesOfferApplicationData';
import { SalesOfferApplicationConfigurationDataFragmentDoc } from '../fragments/SalesOfferApplicationConfigurationData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type UploadApplicationDocumentMutationVariables = SchemaTypes.Exact<{
  applicationId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  token?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['String']['input']>;
  upload: SchemaTypes.Scalars['Upload']['input'];
  kind: SchemaTypes.ApplicationDocumentKind;
  isTemporary?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['Boolean']['input']>;
}>;


export type UploadApplicationDocumentMutation = (
  { __typename: 'Mutation' }
  & { result: (
    { __typename: 'UploadApplicationDocumentResult' }
    & { application: (
      { __typename: 'ConfiguratorApplication' }
      & ApplicationData_ConfiguratorApplication_Fragment
    ) | (
      { __typename: 'EventApplication' }
      & ApplicationData_EventApplication_Fragment
    ) | (
      { __typename: 'FinderApplication' }
      & ApplicationData_FinderApplication_Fragment
    ) | (
      { __typename: 'LaunchpadApplication' }
      & ApplicationData_LaunchpadApplication_Fragment
    ) | (
      { __typename: 'MobilityApplication' }
      & ApplicationData_MobilityApplication_Fragment
    ) | (
      { __typename: 'SalesOfferApplication' }
      & ApplicationData_SalesOfferApplication_Fragment
    ) | (
      { __typename: 'StandardApplication' }
      & ApplicationData_StandardApplication_Fragment
    ), uploadedFile: (
      { __typename: 'UploadedFile' }
      & UploadFileFormDataFragment
    ) }
  ) }
);


export const UploadApplicationDocumentDocument = /*#__PURE__*/ gql`
    mutation uploadApplicationDocument($applicationId: ObjectID, $token: String, $upload: Upload!, $kind: ApplicationDocumentKind!, $isTemporary: Boolean) {
  result: uploadApplicationDocument(
    applicationId: $applicationId
    token: $token
    upload: $upload
    kind: $kind
    isTemporary: $isTemporary
  ) {
    application {
      ...ApplicationData
    }
    uploadedFile {
      ...UploadFileFormData
    }
  }
}
    ${ApplicationDataFragmentDoc}
${ApplicationStageDataFragmentDoc}
${EventApplicationModuleSpecsForApplicationFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${KycExtraSettingsSpecsFragmentDoc}
${StandardApplicationModuleSpecsForApplicationFragmentDoc}
${DepositAmountDataFragmentDoc}
${ApplicationMarketTypeFragmentFragmentDoc}
${DealerMarketDataFragmentDoc}
${BankDealerMarketDataFragmentDoc}
${NzFeesDealerMarketDataFragmentDoc}
${DealerDisclaimersConfiguratorDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${DealershipSettingSpecDataFragmentDoc}
${FlexibleDiscountDataFragmentDoc}
${DealerPriceDisclaimerDataFragmentDoc}
${ConfiguratorModuleSpecsForApplicationFragmentDoc}
${FinderApplicationPublicModuleSpecsForApplicationFragmentDoc}
${KycPresetsOptionsDataFragmentDoc}
${FinderApplicationPrivateModuleSpecsForApplicationFragmentDoc}
${LaunchpadModuleSpecsForApplicationFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${LeadDataFragmentDoc}
${StandardLeadDataFragmentDoc}
${VehicleSpecsFragmentDoc}
${LocalVariantSpecsFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${LocalModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${FinderVehicleSpecsFragmentDoc}
${FullListingValueFragmentDoc}
${FormattedDateDataFragmentDoc}
${LocalizedStringDataFragmentDoc}
${LocalizedValueDataFragmentDoc}
${NumberUnitDataFragmentDoc}
${FinderLeadDataFragmentDoc}
${EventLeadDataFragmentDoc}
${ApplicationEventCustomizedFieldDataFragmentDoc}
${LaunchpadLeadDataFragmentDoc}
${ConfiguratorLeadDataFragmentDoc}
${ConfiguratorJourneyBlocksDataFragmentDoc}
${BlockDetailsFragmentDoc}
${OptionSettingDetailsFragmentDoc}
${MobilityLeadDataFragmentDoc}
${CustomerSpecsFragmentDoc}
${LocalCustomerDataFragmentDoc}
${LocalCustomerFieldDataFragmentDoc}
${CorporateCustomerDataFragmentDoc}
${GuarantorDataFragmentDoc}
${UsersOptionsDataFragmentDoc}
${KycFieldSpecsFragmentDoc}
${LaunchPadModuleSpecsFragmentDoc}
${CompanyInModuleOptionDataFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc}
${AppointmentModuleSpecsFragmentDoc}
${AppointmentTimeSlotDataFragmentDoc}
${NamirialSigningModuleSpecsFragmentDoc}
${NamirialSettingsSpecFragmentDoc}
${AppointmentModuleEmailContentsSpecsFragmentDoc}
${AppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${AppointmentModuleEmailContentSpecsFragmentDoc}
${DealerTranslatedStringSettingDataFragmentDoc}
${DealerBooleanSettingDataFragmentDoc}
${AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc}
${VisitAppointmentModuleSpecsFragmentDoc}
${TimeSlotDataFragmentDoc}
${VisitAppointmentModuleEmailContentsSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${CounterSettingsSpecsFragmentDoc}
${DealerVehiclesSpecsFragmentDoc}
${ApplicationAgreementDataFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${MarketingPlatformSpecsFragmentDoc}
${MarketingPlatformsAgreedSpecsFragmentDoc}
${TradeInVehicleDataFragmentDoc}
${DealerApplicationFragmentFragmentDoc}
${DealerContactFragmentFragmentDoc}
${DealerSocialMediaFragmentFragmentDoc}
${DealerDisclaimersFragmentFragmentDoc}
${DealerIntegrationDetailsFragmentFragmentDoc}
${ApplicationDocumentDataFragmentDoc}
${ReferenceApplicationDataFragmentDoc}
${ReferenceDepositDataFragmentDoc}
${ReferenceFinancingDataFragmentDoc}
${ReferenceInsuranceDataFragmentDoc}
${SalesOfferSpecsFragmentDoc}
${VehicleSalesOfferSpecsFragmentDoc}
${PorscheVehicleDataSpecsFragmentDoc}
${PorscheVehicleDataFeatureSpecsFragmentDoc}
${PorscheVehicleImagesSpecsFragmentDoc}
${LocalFittedOptionsSpecsFragmentDoc}
${SalesOfferDocumentDataFragmentDoc}
${MainDetailsSalesOfferSpecsFragmentDoc}
${TradeInSalesOfferSpecsFragmentDoc}
${FinanceSalesOfferSpecsFragmentDoc}
${ApplicationFinancingDataFragmentDoc}
${InsuranceSalesOfferSpecsFragmentDoc}
${ApplicationInsurancingDataFragmentDoc}
${DepositSalesOfferSpecsFragmentDoc}
${VsaSalesOfferSpecsFragmentDoc}
${SalesOfferModuleSpecsFragmentDoc}
${BankDetailsDataFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${BankIntegrationDataFragmentDoc}
${UploadFileFormDataFragmentDoc}
${FinanceProductDetailsDataFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${PeriodDataFragmentDoc}
${VehicleReferenceParametersDataFragmentDoc}
${PaymentSettingsDetailsFragmentDoc}
${LoanSettingsDetailsFragmentDoc}
${TermSettingsDetailsFragmentDoc}
${InterestRateSettingsDetailsFragmentDoc}
${DownPaymentSettingsDetailsFragmentDoc}
${BalloonSettingsDetailsFragmentDoc}
${BalloonGfvSettingsDetailsFragmentDoc}
${LeaseSettingsDetailsFragmentDoc}
${DepositSettingsDetailsFragmentDoc}
${ResidualValueSettingsDetailsFragmentDoc}
${LocalUcclLeasingOnlyDetailsFragmentDoc}
${DealerFinanceProductsSpecsFragmentDoc}
${FinanceProductListDataFragmentDoc}
${DealerInsuranceProductsSpecsFragmentDoc}
${InsuranceProductListDataFragmentDoc}
${ErgoLookupTableSettingsDetailsFragmentDoc}
${SalesOfferModuleEmailContentsSpecsFragmentDoc}
${SalesOfferEmailContentsSpecsFragmentDoc}
${SalesOfferKycPresetSpecsFragmentDoc}
${SalesOfferConsentsSpecsFragmentDoc}
${SalesOfferSigningsSpecsFragmentDoc}
${NamirialSigningDataFragmentDoc}
${StandardApplicationDataFragmentDoc}
${DraftFlowConfigurationSpecFragmentDoc}
${InsuranceProductDetailsDataFragmentDoc}
${ApplicationConfigurationDataFragmentDoc}
${FinanceProductDetailsFragmentDoc}
${LocalHirePurchaseDetailsFragmentDoc}
${LocalHirePurchaseWithBalloonDetailsFragmentDoc}
${LocalHirePurchaseWithBalloonGfvDetailsFragmentDoc}
${LocalLeaseDetailsFragmentDoc}
${LocalLeasePurchaseDetailsFragmentDoc}
${LocalDeferredPrincipalDetailsFragmentDoc}
${LocalUcclLeasingDetailsFragmentDoc}
${ApplicationAdyenDepositDataFragmentDoc}
${ApplicationPorscheDepositDataFragmentDoc}
${ApplicationFiservDepositDataFragmentDoc}
${ApplicationPayGateDepositDataFragmentDoc}
${ApplicationTtbDepositDataFragmentDoc}
${PromoCodeDataFragmentDoc}
${DealerFragmentFragmentDoc}
${CompanyContextDataFragmentDoc}
${LanguagePackContextDataFragmentDoc}
${AvailableModulesDataFragmentDoc}
${CompanyDealerDataFragmentDoc}
${MaintenanceUpdateFragmentDoc}
${UserAvatarSpecsFragmentDoc}
${EdmEmailFooterPublicDataFragmentDoc}
${EdmSocialMediaDataFragmentDoc}
${ModuleInDealerSpecsFragmentDoc}
${StandardApplicationModuleInDealerSpecsFragmentDoc}
${StandardApplicationModuleEmailContentsSpecsFragmentDoc}
${StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc}
${StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc}
${DealerUploadedFileWithPreviewDataFragmentDoc}
${StandardApplicationModuleEmailContentSpecsFragmentDoc}
${StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc}
${EventApplicationModuleInDealerSpecsFragmentDoc}
${EventApplicationModuleEmailContentSpecsFragmentDoc}
${TranslatedTextDataFragmentDoc}
${ConfiguratorModuleInDealerSpecsFragmentDoc}
${ConfiguratorModuleEmailContentSpecsFragmentDoc}
${MobilityModuleInDealerSpecsFragmentDoc}
${MobilityModuleEmailScenarioContentSpecsFragmentDoc}
${MobilityCustomerEmailContentDataFragmentDoc}
${MobilityEmailContentDataFragmentDoc}
${MobilityOperatorEmailContentDataFragmentDoc}
${DealerBookingCodeSpecsFragmentDoc}
${MobilityHomeDeliveryDataFragmentDoc}
${FinderApplicationPublicModuleInDealerSpecsFragmentDoc}
${FinderApplicationModuleEmailContentSpecsFragmentDoc}
${FinderApplicationPrivateModuleInDealerSpecsFragmentDoc}
${AppointmentModuleInDealerSpecsFragmentDoc}
${VisitAppointmentModuleInDealerSpecsFragmentDoc}
${GiftVoucherModuleInDealerSpecsFragmentDoc}
${GiftVoucherModuleEmailContentsSpecsFragmentDoc}
${GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc}
${GiftVoucherModuleEmailDataFragmentDoc}
${LaunchPadModuleInDealerSpecsFragmentDoc}
${SalesOfferModuleInDealerSpecsFragmentDoc}
${SalesControlBoardModuleInDealerSpecsFragmentDoc}
${DealerIntDataFragmentDoc}
${DealerFloatDataFragmentDoc}
${DealerObjectIdDataFragmentDoc}
${GiftPromoTypeDataFragmentDoc}
${DiscountPromoTypeDataFragmentDoc}
${EndpointContextDataFragmentDoc}
${DummyPrivatePageEndpointContextDataFragmentDoc}
${StandardApplicationEntrypointContextDataFragmentDoc}
${InsurerEntrypointContextDataFragmentDoc}
${ApplicationListEndpointContextDataFragmentDoc}
${LeadListEndpointContextDataFragmentDoc}
${EventApplicationEntrypointContextDataFragmentDoc}
${LaunchPadApplicationEntrypointContextDataFragmentDoc}
${ConfiguratorApplicationEntrypointContextDataFragmentDoc}
${MyInfoSettingSpecFragmentDoc}
${CustomerListEndpointContextDataFragmentDoc}
${MobilityApplicationEntrypointContextDataFragmentDoc}
${DealerBookingCodeDataFragmentDoc}
${DateUnitDataFragmentDoc}
${WebpageEndpointContextDataFragmentDoc}
${WebPageEndpointSpecsFragmentDoc}
${WebPagePathDataFragmentDoc}
${StandardApplicationPublicAccessEntrypointContextDataFragmentDoc}
${FinderApplicationPublicAccessEntrypointContextDataFragmentDoc}
${EntrypointFinderApplicationPublicModuleFragmentDoc}
${ModuleDisclaimersDataFragmentDoc}
${FinderApplicationEntrypointContextDataFragmentDoc}
${EntrypointFinderApplicationPrivateModuleFragmentDoc}
${ApplicationQuotationDataFragmentDoc}
${ApplicationQuotationOptionDataFragmentDoc}
${EventApplicationDataFragmentDoc}
${EventApplicationConfigurationDataFragmentDoc}
${CustomTestDriveBookingSlotsDataFragmentDoc}
${TestDriveFixedPeriodDataFragmentDoc}
${TestDriveBookingWindowSettingsDataFragmentDoc}
${ConfiguratorApplicationDataFragmentDoc}
${ConfiguratorApplicationConfigurationDataFragmentDoc}
${VariantConfiguratorJourneyDataFragmentDoc}
${LocalVariantPublicSpecsFragmentDoc}
${LocalModelPublicSpecsFragmentDoc}
${LocalMakePublicSpecsFragmentDoc}
${MatrixDataFragmentDoc}
${InventoryDetailsPublicDataFragmentDoc}
${StockInventorySpecsFragmentDoc}
${CompanyPublicSpecsFragmentDoc}
${StockBlockingPeriodDataFragmentDoc}
${ConfiguratorInventoryPublicSpecsFragmentDoc}
${MobilityInventoryPublicSpecsFragmentDoc}
${MobilityModuleSpecsFragmentDoc}
${MobilitySigningSettingSpecsFragmentDoc}
${MobilityApplicationDataFragmentDoc}
${MobilityApplicationModuleDataFragmentDoc}
${MobilitySnapshotDataFragmentDoc}
${MobilityBookingDetailsDataFragmentDoc}
${MobilityBookingLocationHomeDataFragmentDoc}
${MobilityBookingLocationPickupDataFragmentDoc}
${GiftVoucherDataFragmentDoc}
${ModuleSpecsFragmentDoc}
${ConsentsAndDeclarationsModuleSpecsFragmentDoc}
${SimpleVehicleManagementModuleSpecsFragmentDoc}
${LocalCustomerManagementModuleSpecsFragmentDoc}
${LocalCustomerManagementModuleKycFieldSpecsFragmentDoc}
${KycPresetsSpecFragmentDoc}
${BankModuleSpecsFragmentDoc}
${BasicSigningModuleSpecsFragmentDoc}
${StandardApplicationModuleSpecsFragmentDoc}
${EventApplicationModuleSpecsFragmentDoc}
${AdyenPaymentModuleSpecsFragmentDoc}
${AdyenPaymentSettingsSpecFragmentDoc}
${PorschePaymentModuleSpecsFragmentDoc}
${PorschePaymentSettingsSpecFragmentDoc}
${FiservPaymentModuleSpecsFragmentDoc}
${FiservPaymentSettingsSpecFragmentDoc}
${PayGatePaymentModuleSpecsFragmentDoc}
${PayGatePaymentSettingsSpecFragmentDoc}
${TtbPaymentModuleSpecsFragmentDoc}
${TtbPaymentSettingsSpecFragmentDoc}
${MyInfoModuleSpecsFragmentDoc}
${ConfiguratorModuleSpecsFragmentDoc}
${WhatsappLiveChatModuleSpecsFragmentDoc}
${WhatsappLiveChatSettingsSpecFragmentDoc}
${UserlikeChatbotModuleSpecsFragmentDoc}
${UserlikeChatbotSettingsSpecFragmentDoc}
${PromoCodeModuleSpecsFragmentDoc}
${MaintenanceModuleSpecsFragmentDoc}
${WebsiteModuleSpecsFragmentDoc}
${LabelsModuleSpecsFragmentDoc}
${FinderVehicleManagementModuleSpecsFragmentDoc}
${FinderApplicationPublicModuleSpecsFragmentDoc}
${FinderApplicationPrivateModuleSpecsFragmentDoc}
${AutoplayModuleSpecsFragmentDoc}
${AutoplaySettingSpecsFragmentDoc}
${CtsModuleSpecsFragmentDoc}
${CtsModuleSettingDataFragmentDoc}
${InsuranceModuleSpecsFragmentDoc}
${PorscheMasterDataModuleSpecsFragmentDoc}
${GiftVoucherModuleSpecsFragmentDoc}
${TradeInModuleSpecsFragmentDoc}
${TradeInSettingSpecFragmentDoc}
${CapModuleSpecsFragmentDoc}
${CapSettingSpecFragmentDoc}
${PorscheIdModuleSpecsFragmentDoc}
${PorscheIdSettingSpecFragmentDoc}
${PorscheRetainModuleSpecsFragmentDoc}
${DocusignModuleSpecsFragmentDoc}
${DocusignSettingDataFragmentDoc}
${OidcModuleSpecsFragmentDoc}
${MarketingModuleSpecsFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationModuleSpecsFragmentDoc}
${SalesControlBoardModuleSpecsFragmentDoc}
${MobilityModuleGiftVoucherDataFragmentDoc}
${GiftVoucherDraftFlowDataFragmentDoc}
${DealerJourneyDataFragmentDoc}
${MobilityStockGiftVoucherDataFragmentDoc}
${FinderApplicationDataFragmentDoc}
${FinderConfigurationDataFragmentDoc}
${LaunchpadApplicationDataFragmentDoc}
${LaunchpadApplicationConfigurationDataFragmentDoc}
${SalesOfferApplicationDataFragmentDoc}
${SalesOfferApplicationConfigurationDataFragmentDoc}`;
export type UploadApplicationDocumentMutationFn = Apollo.MutationFunction<UploadApplicationDocumentMutation, UploadApplicationDocumentMutationVariables>;

/**
 * __useUploadApplicationDocumentMutation__
 *
 * To run a mutation, you first call `useUploadApplicationDocumentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUploadApplicationDocumentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [uploadApplicationDocumentMutation, { data, loading, error }] = useUploadApplicationDocumentMutation({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *      token: // value for 'token'
 *      upload: // value for 'upload'
 *      kind: // value for 'kind'
 *      isTemporary: // value for 'isTemporary'
 *   },
 * });
 */
export function useUploadApplicationDocumentMutation(baseOptions?: Apollo.MutationHookOptions<UploadApplicationDocumentMutation, UploadApplicationDocumentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UploadApplicationDocumentMutation, UploadApplicationDocumentMutationVariables>(UploadApplicationDocumentDocument, options);
      }
export type UploadApplicationDocumentMutationHookResult = ReturnType<typeof useUploadApplicationDocumentMutation>;
export type UploadApplicationDocumentMutationResult = Apollo.MutationResult<UploadApplicationDocumentMutation>;
export type UploadApplicationDocumentMutationOptions = Apollo.BaseMutationOptions<UploadApplicationDocumentMutation, UploadApplicationDocumentMutationVariables>;