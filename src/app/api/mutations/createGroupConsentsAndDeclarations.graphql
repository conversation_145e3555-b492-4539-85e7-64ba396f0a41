mutation createGroupConsentsAndDeclarations(
    $moduleId: ObjectID!
    $settings: GroupConsentsAndDeclarationsWithChildrenInput!
    $conditionSettings: [ConditionSettings!]
    $eventId: ObjectID
) {
    consentsAndDeclarations: createGroupConsentsAndDeclarations(
        moduleId: $moduleId
        settings: $settings
        conditionSettings: $conditionSettings
        eventId: $eventId
    ) {
        ...ConsentsAndDeclarationsSpecs
    }
}