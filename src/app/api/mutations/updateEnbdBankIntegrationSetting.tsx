import type * as SchemaTypes from '../types';

import type { BankDetailsDataFragment } from '../fragments/BankDetailsData';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { DealerDisclaimersConfiguratorDataFragment } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from '../fragments/BankIntegrationData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { UploadFileFormDataFragment } from '../fragments/UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductDetailsData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { VehicleReferenceParametersDataFragment } from '../fragments/VehicleReferenceParametersData';
import type { PaymentSettingsDetailsFragment } from '../fragments/PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from '../fragments/LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from '../fragments/TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from '../fragments/InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from '../fragments/DownPaymentSettingsDetails';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from '../fragments/BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from '../fragments/BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from '../fragments/LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from '../fragments/DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from '../fragments/ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from '../fragments/LocalUcclLeasingOnlyDetails';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import { gql } from '@apollo/client';
import { BankDetailsDataFragmentDoc } from '../fragments/BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { BankIntegrationDataFragmentDoc } from '../fragments/BankIntegrationData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { UploadFileFormDataFragmentDoc } from '../fragments/UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from '../fragments/FinanceProductDetailsData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from '../fragments/VehicleReferenceParametersData';
import { PaymentSettingsDetailsFragmentDoc } from '../fragments/PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from '../fragments/LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from '../fragments/TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from '../fragments/InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from '../fragments/DownPaymentSettingsDetails';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { BalloonSettingsDetailsFragmentDoc } from '../fragments/BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from '../fragments/BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from '../fragments/LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from '../fragments/DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from '../fragments/ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from '../fragments/LocalUcclLeasingOnlyDetails';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type UpdateEnbdBankIntegrationSettingMutationVariables = SchemaTypes.Exact<{
  bankId: SchemaTypes.Scalars['ObjectID']['input'];
  settings: SchemaTypes.EnbdBankIntegrationSettings;
}>;


export type UpdateEnbdBankIntegrationSettingMutation = (
  { __typename: 'Mutation' }
  & { bank?: SchemaTypes.Maybe<{ __typename: 'ExternalBank' } | (
    { __typename: 'SystemBank' }
    & BankDetailsDataFragment
  )> }
);


export const UpdateEnbdBankIntegrationSettingDocument = /*#__PURE__*/ gql`
    mutation updateEnbdBankIntegrationSetting($bankId: ObjectID!, $settings: EnbdBankIntegrationSettings!) {
  bank: updateEnbdBankIntegrationSetting(bankId: $bankId, settings: $settings) {
    ...BankDetailsData
  }
}
    ${BankDetailsDataFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${DealerDisclaimersConfiguratorDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${BankIntegrationDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${UploadFileFormDataFragmentDoc}
${FinanceProductDetailsDataFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${PeriodDataFragmentDoc}
${VehicleReferenceParametersDataFragmentDoc}
${PaymentSettingsDetailsFragmentDoc}
${LoanSettingsDetailsFragmentDoc}
${TermSettingsDetailsFragmentDoc}
${InterestRateSettingsDetailsFragmentDoc}
${DownPaymentSettingsDetailsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${BalloonSettingsDetailsFragmentDoc}
${BalloonGfvSettingsDetailsFragmentDoc}
${LeaseSettingsDetailsFragmentDoc}
${DepositSettingsDetailsFragmentDoc}
${ResidualValueSettingsDetailsFragmentDoc}
${LocalUcclLeasingOnlyDetailsFragmentDoc}
${SimpleVersioningDataFragmentDoc}`;
export type UpdateEnbdBankIntegrationSettingMutationFn = Apollo.MutationFunction<UpdateEnbdBankIntegrationSettingMutation, UpdateEnbdBankIntegrationSettingMutationVariables>;

/**
 * __useUpdateEnbdBankIntegrationSettingMutation__
 *
 * To run a mutation, you first call `useUpdateEnbdBankIntegrationSettingMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateEnbdBankIntegrationSettingMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateEnbdBankIntegrationSettingMutation, { data, loading, error }] = useUpdateEnbdBankIntegrationSettingMutation({
 *   variables: {
 *      bankId: // value for 'bankId'
 *      settings: // value for 'settings'
 *   },
 * });
 */
export function useUpdateEnbdBankIntegrationSettingMutation(baseOptions?: Apollo.MutationHookOptions<UpdateEnbdBankIntegrationSettingMutation, UpdateEnbdBankIntegrationSettingMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateEnbdBankIntegrationSettingMutation, UpdateEnbdBankIntegrationSettingMutationVariables>(UpdateEnbdBankIntegrationSettingDocument, options);
      }
export type UpdateEnbdBankIntegrationSettingMutationHookResult = ReturnType<typeof useUpdateEnbdBankIntegrationSettingMutation>;
export type UpdateEnbdBankIntegrationSettingMutationResult = Apollo.MutationResult<UpdateEnbdBankIntegrationSettingMutation>;
export type UpdateEnbdBankIntegrationSettingMutationOptions = Apollo.BaseMutationOptions<UpdateEnbdBankIntegrationSettingMutation, UpdateEnbdBankIntegrationSettingMutationVariables>;