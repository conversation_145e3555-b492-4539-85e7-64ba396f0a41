import type * as SchemaTypes from '../types';

import type { DealerJourneyDataFragment } from './DealerJourneyData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { StandardApplicationModuleDebugJourneyFragment } from './StandardApplicationModuleDebugJourney';
import type { AppointmentModuleApplicationJourneyFragment } from './AppointmentModuleApplicationJourney';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { NamirialSigningModuleSpecsFragment } from './NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from './NamirialSettingsSpec';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { VisitAppointmentModuleApplicationJourneyFragment } from './VisitAppointmentModuleApplicationJourney';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { TradeInVehicleDataFragment } from './TradeInVehicleData';
import type { ApplicationAgreementData_CheckboxApplicationAgreement_Fragment, ApplicationAgreementData_MarketingApplicationAgreement_Fragment, ApplicationAgreementData_TextApplicationAgreement_Fragment } from './ApplicationAgreementData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './BaseConditionSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { MarketingPlatformSpecsFragment } from './MarketingPlatformSpecs';
import type { MarketingPlatformsAgreedSpecsFragment } from './MarketingPlatformsAgreedSpecs';
import type { UsersOptionsDataFragment } from './UsersOptionsData';
import type { KycFieldSpecsFragment } from './KYCFieldSpecs';
import type { ApplicationDocumentDataFragment } from './ApplicationDocumentData';
import type { CustomerSpecs_CorporateCustomer_Fragment, CustomerSpecs_Guarantor_Fragment, CustomerSpecs_LocalCustomer_Fragment } from './CustomerSpecs';
import type { LocalCustomerDataFragment } from './LocalCustomerData';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from './LocalCustomerFieldData';
import type { CorporateCustomerDataFragment } from './CorporateCustomerData';
import type { GuarantorDataFragment } from './GuarantorData';
import type { JourneyDraftFlowFragment } from './JourneyDraftFlow';
import type { ApplicationJourneyDeposit_ApplicationAdyenDeposit_Fragment, ApplicationJourneyDeposit_ApplicationFiservDeposit_Fragment, ApplicationJourneyDeposit_ApplicationPayGateDeposit_Fragment, ApplicationJourneyDeposit_ApplicationPorscheDeposit_Fragment, ApplicationJourneyDeposit_ApplicationTtbDeposit_Fragment } from './ApplicationJourneyDeposit';
import type { ApplicationFinancingData_DefaultApplicationFinancing_Fragment, ApplicationFinancingData_NewZealandApplicationFinancing_Fragment, ApplicationFinancingData_SingaporeApplicationFinancing_Fragment } from './ApplicationFinancingData';
import type { ApplicationInsurancingData_DefaultApplicationInsurancing_Fragment, ApplicationInsurancingData_NewZealandApplicationInsurancing_Fragment, ApplicationInsurancingData_SingaporeApplicationInsurancing_Fragment } from './ApplicationInsurancingData';
import type { NamirialSigningDataFragment } from './NamirialSigningData';
import type { ApplicationVariantSpecFragment } from './ApplicationVehicleSpec';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { ApplicationModelSpecsFragment } from './ApplicationModelSpec';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { ApplicationQuotationDataFragment, ApplicationQuotationOptionDataFragment } from './ApplicationQuotationData';
import type { LeadData_ConfiguratorLead_Fragment, LeadData_EventLead_Fragment, LeadData_FinderLead_Fragment, LeadData_LaunchpadLead_Fragment, LeadData_MobilityLead_Fragment, LeadData_StandardLead_Fragment } from './LeadData';
import type { StandardLeadDataFragment } from './StandardLeadData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { FinderLeadDataFragment } from './FinderLeadData';
import type { EventLeadDataFragment } from './EventLeadData';
import type { ApplicationEventCustomizedFieldDataFragment } from './ApplicationEventCustomizedFieldData';
import type { LaunchpadLeadDataFragment } from './LaunchpadLeadData';
import type { ConfiguratorLeadDataFragment } from './ConfiguratorLeadData';
import type { ConfiguratorJourneyBlocksData_ApplicationConfiguratorColorSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorOptionSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorPackageSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorTrimSetting_Fragment } from './ConfiguratorJourneyBlocksData';
import type { BlockDetails_ColorBlock_Fragment, BlockDetails_OptionsBlock_Fragment, BlockDetails_PackageBlock_Fragment, BlockDetails_TrimBlock_Fragment } from './BlockDetails';
import type { OptionSettingDetails_ComboOptionSettings_Fragment, OptionSettingDetails_DropdownOptionSettings_Fragment, OptionSettingDetails_MultiSelectOptionSettings_Fragment, OptionSettingDetails_SingleSelectOptionSettings_Fragment } from './OptionSettingDetails';
import type { MobilityLeadDataFragment } from './MobilityLeadData';
import type { LaunchPadModuleSpecsFragment } from './LaunchPadModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { AppointmentModuleSpecsFragment } from './AppointmentModuleSpecs';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { VisitAppointmentModuleSpecsFragment } from './VisitAppointmentModuleSpecs';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from './VisitAppointmentModuleEmailContentsSpecs';
import type { CounterSettingsSpecsFragment } from './CounterSettingsSpecs';
import type { DealerVehiclesSpecsFragment } from './DealerVehiclesSpecs';
import type { DealerApplicationFragmentFragment } from './DealerApplicationFragment';
import type { DealerContactFragmentFragment } from './DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from './DealerSocialMediaFragment';
import type { DealerDisclaimersFragmentFragment } from './DealerDisclaimersFragment';
import type { DealerIntegrationDetailsFragmentFragment } from './DealerIntegrationDetailsFragment';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from './ReferenceApplicationData';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from './ApplicationStageData';
import type { SalesOfferSpecsFragment } from './SalesOfferSpecs';
import type { VehicleSalesOfferSpecsFragment } from './VehicleSalesOfferSpecs';
import type { PorscheVehicleDataSpecsFragment, PorscheVehicleDataFeatureSpecsFragment, PorscheVehicleImagesSpecsFragment } from './PorscheVehicleDataSpecs';
import type { LocalFittedOptionsSpecsFragment } from './LocalFittedOptionsSpecs';
import type { SalesOfferDocumentDataFragment } from './SalesOfferDocumentData';
import type { MainDetailsSalesOfferSpecsFragment } from './MainDetailsSalesOfferSpecs';
import type { TradeInSalesOfferSpecsFragment } from './TradeInSalesOfferSpecs';
import type { FinanceSalesOfferSpecsFragment } from './FinanceSalesOfferSpecs';
import type { InsuranceSalesOfferSpecsFragment } from './InsuranceSalesOfferSpecs';
import type { DepositSalesOfferSpecsFragment } from './DepositSalesOfferSpecs';
import type { VsaSalesOfferSpecsFragment } from './VSASalesOfferSpecs';
import type { SalesOfferModuleSpecsFragment } from './SalesOfferModuleSpecs';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { BankDetailsDataFragment } from './BankDetailsData';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from './BankIntegrationData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from './FinanceProductDetailsData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from './PeriodData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import type { DealerFinanceProductsSpecsFragment } from './DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from './FinanceProductListData';
import type { DealerInsuranceProductsSpecsFragment } from './DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from './InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from './ErgoLookupTableSettingDetails';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from './SalesOfferModuleEmailContentsSpecs';
import type { SalesOfferKycPresetSpecsFragment } from './SalesOfferKYCPresetSpecs';
import type { SalesOfferConsentsSpecsFragment } from './SalesOfferConsentsSpecs';
import type { SalesOfferSigningsSpecsFragment } from './SalesOfferSigningsSpecs';
import { gql } from '@apollo/client';
import { DealerJourneyDataFragmentDoc } from './DealerJourneyData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { StandardApplicationModuleDebugJourneyFragmentDoc } from './StandardApplicationModuleDebugJourney';
import { AppointmentModuleApplicationJourneyFragmentDoc } from './AppointmentModuleApplicationJourney';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { NamirialSigningModuleSpecsFragmentDoc } from './NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from './NamirialSettingsSpec';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { VisitAppointmentModuleApplicationJourneyFragmentDoc } from './VisitAppointmentModuleApplicationJourney';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { TradeInVehicleDataFragmentDoc } from './TradeInVehicleData';
import { ApplicationAgreementDataFragmentDoc } from './ApplicationAgreementData';
import { ConditionSpecsFragmentDoc } from './ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from './BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { MarketingPlatformSpecsFragmentDoc } from './MarketingPlatformSpecs';
import { MarketingPlatformsAgreedSpecsFragmentDoc } from './MarketingPlatformsAgreedSpecs';
import { UsersOptionsDataFragmentDoc } from './UsersOptionsData';
import { KycFieldSpecsFragmentDoc } from './KYCFieldSpecs';
import { ApplicationDocumentDataFragmentDoc } from './ApplicationDocumentData';
import { CustomerSpecsFragmentDoc } from './CustomerSpecs';
import { LocalCustomerDataFragmentDoc } from './LocalCustomerData';
import { LocalCustomerFieldDataFragmentDoc } from './LocalCustomerFieldData';
import { CorporateCustomerDataFragmentDoc } from './CorporateCustomerData';
import { GuarantorDataFragmentDoc } from './GuarantorData';
import { JourneyDraftFlowFragmentDoc } from './JourneyDraftFlow';
import { ApplicationJourneyDepositFragmentDoc } from './ApplicationJourneyDeposit';
import { ApplicationFinancingDataFragmentDoc } from './ApplicationFinancingData';
import { ApplicationInsurancingDataFragmentDoc } from './ApplicationInsurancingData';
import { NamirialSigningDataFragmentDoc } from './NamirialSigningData';
import { ApplicationVariantSpecFragmentDoc } from './ApplicationVehicleSpec';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { ApplicationModelSpecsFragmentDoc } from './ApplicationModelSpec';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { ApplicationQuotationDataFragmentDoc, ApplicationQuotationOptionDataFragmentDoc } from './ApplicationQuotationData';
import { LeadDataFragmentDoc } from './LeadData';
import { StandardLeadDataFragmentDoc } from './StandardLeadData';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { FinderLeadDataFragmentDoc } from './FinderLeadData';
import { EventLeadDataFragmentDoc } from './EventLeadData';
import { ApplicationEventCustomizedFieldDataFragmentDoc } from './ApplicationEventCustomizedFieldData';
import { LaunchpadLeadDataFragmentDoc } from './LaunchpadLeadData';
import { ConfiguratorLeadDataFragmentDoc } from './ConfiguratorLeadData';
import { ConfiguratorJourneyBlocksDataFragmentDoc } from './ConfiguratorJourneyBlocksData';
import { BlockDetailsFragmentDoc } from './BlockDetails';
import { OptionSettingDetailsFragmentDoc } from './OptionSettingDetails';
import { MobilityLeadDataFragmentDoc } from './MobilityLeadData';
import { LaunchPadModuleSpecsFragmentDoc } from './LaunchPadModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { AppointmentModuleSpecsFragmentDoc } from './AppointmentModuleSpecs';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { VisitAppointmentModuleSpecsFragmentDoc } from './VisitAppointmentModuleSpecs';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from './VisitAppointmentModuleEmailContentsSpecs';
import { CounterSettingsSpecsFragmentDoc } from './CounterSettingsSpecs';
import { DealerVehiclesSpecsFragmentDoc } from './DealerVehiclesSpecs';
import { DealerApplicationFragmentFragmentDoc } from './DealerApplicationFragment';
import { DealerContactFragmentFragmentDoc } from './DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from './DealerSocialMediaFragment';
import { DealerDisclaimersFragmentFragmentDoc } from './DealerDisclaimersFragment';
import { DealerIntegrationDetailsFragmentFragmentDoc } from './DealerIntegrationDetailsFragment';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from './ReferenceApplicationData';
import { ApplicationStageDataFragmentDoc } from './ApplicationStageData';
import { SalesOfferSpecsFragmentDoc } from './SalesOfferSpecs';
import { VehicleSalesOfferSpecsFragmentDoc } from './VehicleSalesOfferSpecs';
import { PorscheVehicleDataSpecsFragmentDoc, PorscheVehicleDataFeatureSpecsFragmentDoc, PorscheVehicleImagesSpecsFragmentDoc } from './PorscheVehicleDataSpecs';
import { LocalFittedOptionsSpecsFragmentDoc } from './LocalFittedOptionsSpecs';
import { SalesOfferDocumentDataFragmentDoc } from './SalesOfferDocumentData';
import { MainDetailsSalesOfferSpecsFragmentDoc } from './MainDetailsSalesOfferSpecs';
import { TradeInSalesOfferSpecsFragmentDoc } from './TradeInSalesOfferSpecs';
import { FinanceSalesOfferSpecsFragmentDoc } from './FinanceSalesOfferSpecs';
import { InsuranceSalesOfferSpecsFragmentDoc } from './InsuranceSalesOfferSpecs';
import { DepositSalesOfferSpecsFragmentDoc } from './DepositSalesOfferSpecs';
import { VsaSalesOfferSpecsFragmentDoc } from './VSASalesOfferSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from './SalesOfferModuleSpecs';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { BankDetailsDataFragmentDoc } from './BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from './BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from './FinanceProductDetailsData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
import { DealerFinanceProductsSpecsFragmentDoc } from './DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from './FinanceProductListData';
import { DealerInsuranceProductsSpecsFragmentDoc } from './DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from './InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from './ErgoLookupTableSettingDetails';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from './SalesOfferModuleEmailContentsSpecs';
import { SalesOfferKycPresetSpecsFragmentDoc } from './SalesOfferKYCPresetSpecs';
import { SalesOfferConsentsSpecsFragmentDoc } from './SalesOfferConsentsSpecs';
import { SalesOfferSigningsSpecsFragmentDoc } from './SalesOfferSigningsSpecs';
export type StandardApplicationSpecFragment = (
  { __typename: 'StandardApplication' }
  & Pick<SchemaTypes.StandardApplication, 'dealerId' | 'withCustomerDevice' | 'bankId' | 'testDriveRedirectUrl' | 'journeySteps'>
  & { dealer: (
    { __typename: 'Dealer' }
    & Pick<SchemaTypes.Dealer, 'id' | 'displayName'>
    & { legalName: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ), contact: (
      { __typename: 'DealerContact' }
      & Pick<SchemaTypes.DealerContact, 'email'>
      & { telephone?: SchemaTypes.Maybe<(
        { __typename: 'OptionalPhone' }
        & Pick<SchemaTypes.OptionalPhone, 'value' | 'prefix'>
      )>, address?: SchemaTypes.Maybe<(
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      )> }
    ) }
    & DealerJourneyDataFragment
  ), module: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | (
    { __typename: 'StandardApplicationModule' }
    & StandardApplicationModuleDebugJourneyFragment
  ) | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, configuration: (
    { __typename: 'ApplicationConfiguration' }
    & Pick<SchemaTypes.ApplicationConfiguration, 'withFinancing' | 'withInsurance' | 'tradeIn' | 'testDrive' | 'isAffinAutoFinanceCentreRequired'>
  ), tradeInVehicle: Array<(
    { __typename: 'TradeInVehicle' }
    & TradeInVehicleDataFragment
  )>, applicantAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, availableAssignees: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )>, applicantKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, documents: Array<(
    { __typename: 'ApplicationDocument' }
    & ApplicationDocumentDataFragment
    & ApplicationDocumentDataFragment
  )>, testDriveKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, testDriveAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, guarantorAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, guarantorKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, corporateAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, corporateKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, applicant: (
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  ), guarantor?: SchemaTypes.Maybe<(
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  )>, draftFlow: (
    { __typename: 'StandardApplicationDraftFlow' }
    & JourneyDraftFlowFragment
  ), deposit?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationAdyenDeposit' }
    & ApplicationJourneyDeposit_ApplicationAdyenDeposit_Fragment
  ) | (
    { __typename: 'ApplicationFiservDeposit' }
    & ApplicationJourneyDeposit_ApplicationFiservDeposit_Fragment
  ) | (
    { __typename: 'ApplicationPayGateDeposit' }
    & ApplicationJourneyDeposit_ApplicationPayGateDeposit_Fragment
  ) | (
    { __typename: 'ApplicationPorscheDeposit' }
    & ApplicationJourneyDeposit_ApplicationPorscheDeposit_Fragment
  ) | (
    { __typename: 'ApplicationTtbDeposit' }
    & ApplicationJourneyDeposit_ApplicationTtbDeposit_Fragment
  )>, financing?: SchemaTypes.Maybe<(
    { __typename: 'DefaultApplicationFinancing' }
    & ApplicationFinancingData_DefaultApplicationFinancing_Fragment
  ) | (
    { __typename: 'NewZealandApplicationFinancing' }
    & ApplicationFinancingData_NewZealandApplicationFinancing_Fragment
  ) | (
    { __typename: 'SingaporeApplicationFinancing' }
    & ApplicationFinancingData_SingaporeApplicationFinancing_Fragment
  )>, insurancing?: SchemaTypes.Maybe<(
    { __typename: 'DefaultApplicationInsurancing' }
    & ApplicationInsurancingData_DefaultApplicationInsurancing_Fragment
  ) | (
    { __typename: 'NewZealandApplicationInsurancing' }
    & ApplicationInsurancingData_NewZealandApplicationInsurancing_Fragment
  ) | (
    { __typename: 'SingaporeApplicationInsurancing' }
    & ApplicationInsurancingData_SingaporeApplicationInsurancing_Fragment
  )>, signing?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationNamirialSigning' }
    & NamirialSigningDataFragment
  ) | { __typename: 'ApplicationOTPSigning' }>, guarantorSigning?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationNamirialSigning' }
    & NamirialSigningDataFragment
  ) | { __typename: 'ApplicationOTPSigning' }>, insuranceSigning?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationNamirialSigning' }
    & NamirialSigningDataFragment
  ) | { __typename: 'ApplicationOTPSigning' }>, testDriveSigning?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationNamirialSigning' }
    & NamirialSigningDataFragment
  ) | { __typename: 'ApplicationOTPSigning' }>, bank?: SchemaTypes.Maybe<(
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'remoteFlowAcknowledgmentInfo' | 'hasVSOUpload' | 'hasUploadDocuments' | 'showCommentsField'>
    & { integration: (
      { __typename: 'DbsBankIntegration' }
      & Pick<SchemaTypes.DbsBankIntegration, 'provider'>
    ) | (
      { __typename: 'EmailBankIntegration' }
      & Pick<SchemaTypes.EmailBankIntegration, 'provider'>
    ) | (
      { __typename: 'EnbdBankIntegration' }
      & Pick<SchemaTypes.EnbdBankIntegration, 'companyName' | 'provider'>
      & { financeManager: (
        { __typename: 'EnbdFinanceManager' }
        & Pick<SchemaTypes.EnbdFinanceManager, 'name'>
      ) }
    ) | (
      { __typename: 'HlfBankIntegration' }
      & Pick<SchemaTypes.HlfBankIntegration, 'provider'>
    ) | (
      { __typename: 'HlfBankV2Integration' }
      & Pick<SchemaTypes.HlfBankV2Integration, 'provider'>
    ) | (
      { __typename: 'MaybankIntegration' }
      & Pick<SchemaTypes.MaybankIntegration, 'provider'>
    ) | (
      { __typename: 'UobBankIntegration' }
      & Pick<SchemaTypes.UobBankIntegration, 'provider'>
    ) }
  )>, vehicle?: SchemaTypes.Maybe<{ __typename: 'FinderVehicle' } | { __typename: 'LocalMake' } | { __typename: 'LocalModel' } | (
    { __typename: 'LocalVariant' }
    & ApplicationVariantSpecFragment
  )>, quotation?: SchemaTypes.Maybe<(
    { __typename: 'EnbdApplicationQuotation' }
    & ApplicationQuotationDataFragment
  )>, lead: (
    { __typename: 'ConfiguratorLead' }
    & LeadData_ConfiguratorLead_Fragment
  ) | (
    { __typename: 'EventLead' }
    & LeadData_EventLead_Fragment
  ) | (
    { __typename: 'FinderLead' }
    & LeadData_FinderLead_Fragment
  ) | (
    { __typename: 'LaunchpadLead' }
    & LeadData_LaunchpadLead_Fragment
  ) | (
    { __typename: 'MobilityLead' }
    & LeadData_MobilityLead_Fragment
  ) | (
    { __typename: 'StandardLead' }
    & LeadData_StandardLead_Fragment
  ) }
);

export const StandardApplicationSpecFragmentDoc = /*#__PURE__*/ gql`
    fragment StandardApplicationSpec on StandardApplication {
  dealerId
  withCustomerDevice
  dealer {
    ...DealerJourneyData
  }
  module {
    ... on StandardApplicationModule {
      ...StandardApplicationModuleDebugJourney
    }
  }
  configuration {
    withFinancing
    withInsurance
    tradeIn
    testDrive
    isAffinAutoFinanceCentreRequired
  }
  tradeInVehicle {
    ...TradeInVehicleData
  }
  applicantAgreements {
    ...ApplicationAgreementData
  }
  availableAssignees {
    ...UsersOptionsData
  }
  applicantKYC {
    ...KYCFieldSpecs
  }
  documents {
    ...ApplicationDocumentData
  }
  testDriveKYC {
    ...KYCFieldSpecs
  }
  testDriveAgreements {
    ...ApplicationAgreementData
  }
  guarantorAgreements {
    ...ApplicationAgreementData
  }
  guarantorKYC {
    ...KYCFieldSpecs
  }
  corporateAgreements {
    ...ApplicationAgreementData
  }
  corporateKYC {
    ...KYCFieldSpecs
  }
  applicant {
    ...CustomerSpecs
  }
  guarantor {
    ...CustomerSpecs
  }
  draftFlow {
    ...JourneyDraftFlow
  }
  deposit {
    ...ApplicationJourneyDeposit
  }
  financing {
    ...ApplicationFinancingData
  }
  insurancing {
    ...ApplicationInsurancingData
  }
  signing {
    ... on ApplicationNamirialSigning {
      ...NamirialSigningData
    }
  }
  guarantorSigning {
    ... on ApplicationNamirialSigning {
      ...NamirialSigningData
    }
  }
  insuranceSigning {
    ... on ApplicationNamirialSigning {
      ...NamirialSigningData
    }
  }
  testDriveSigning {
    ... on ApplicationNamirialSigning {
      ...NamirialSigningData
    }
  }
  bankId
  bank {
    id
    remoteFlowAcknowledgmentInfo
    hasVSOUpload
    hasUploadDocuments
    integration {
      provider
      ... on EnbdBankIntegration {
        financeManager {
          name
        }
        companyName
      }
    }
    showCommentsField
  }
  dealer {
    id
    displayName
    legalName {
      ...TranslatedStringData
    }
    contact {
      telephone {
        value
        prefix
      }
      email
      address {
        ...TranslatedStringData
      }
    }
  }
  vehicle {
    ...ApplicationVariantSpec
  }
  quotation {
    ...ApplicationQuotationData
  }
  documents {
    ...ApplicationDocumentData
  }
  testDriveRedirectUrl
  journeySteps
  lead {
    ...LeadData
  }
}
    `;