import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type ModuleWithScenarios_AdyenPaymentModule_Fragment = (
  { __typename: 'AdyenPaymentModule' }
  & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_AppointmentModule_Fragment = (
  { __typename: 'AppointmentModule' }
  & Pick<SchemaTypes.AppointmentModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_AutoplayModule_Fragment = (
  { __typename: 'AutoplayModule' }
  & Pick<SchemaTypes.AutoplayModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_BankModule_Fragment = (
  { __typename: 'BankModule' }
  & Pick<SchemaTypes.BankModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_BasicSigningModule_Fragment = (
  { __typename: 'BasicSigningModule' }
  & Pick<SchemaTypes.BasicSigningModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_CapModule_Fragment = (
  { __typename: 'CapModule' }
  & Pick<SchemaTypes.CapModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_ConfiguratorModule_Fragment = (
  { __typename: 'ConfiguratorModule' }
  & Pick<SchemaTypes.ConfiguratorModule, 'scenarios' | 'id' | 'companyId'>
);

export type ModuleWithScenarios_ConsentsAndDeclarationsModule_Fragment = (
  { __typename: 'ConsentsAndDeclarationsModule' }
  & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_CtsModule_Fragment = (
  { __typename: 'CtsModule' }
  & Pick<SchemaTypes.CtsModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_DocusignModule_Fragment = (
  { __typename: 'DocusignModule' }
  & Pick<SchemaTypes.DocusignModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_EventApplicationModule_Fragment = (
  { __typename: 'EventApplicationModule' }
  & Pick<SchemaTypes.EventApplicationModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_FinderApplicationPrivateModule_Fragment = (
  { __typename: 'FinderApplicationPrivateModule' }
  & Pick<SchemaTypes.FinderApplicationPrivateModule, 'scenarios' | 'id' | 'companyId'>
);

export type ModuleWithScenarios_FinderApplicationPublicModule_Fragment = (
  { __typename: 'FinderApplicationPublicModule' }
  & Pick<SchemaTypes.FinderApplicationPublicModule, 'scenarios' | 'id' | 'companyId'>
);

export type ModuleWithScenarios_FinderVehicleManagementModule_Fragment = (
  { __typename: 'FinderVehicleManagementModule' }
  & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_FiservPaymentModule_Fragment = (
  { __typename: 'FiservPaymentModule' }
  & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_GiftVoucherModule_Fragment = (
  { __typename: 'GiftVoucherModule' }
  & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_InsuranceModule_Fragment = (
  { __typename: 'InsuranceModule' }
  & Pick<SchemaTypes.InsuranceModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_LabelsModule_Fragment = (
  { __typename: 'LabelsModule' }
  & Pick<SchemaTypes.LabelsModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_LaunchPadModule_Fragment = (
  { __typename: 'LaunchPadModule' }
  & Pick<SchemaTypes.LaunchPadModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_LocalCustomerManagementModule_Fragment = (
  { __typename: 'LocalCustomerManagementModule' }
  & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_MaintenanceModule_Fragment = (
  { __typename: 'MaintenanceModule' }
  & Pick<SchemaTypes.MaintenanceModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_MarketingModule_Fragment = (
  { __typename: 'MarketingModule' }
  & Pick<SchemaTypes.MarketingModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_MobilityModule_Fragment = (
  { __typename: 'MobilityModule' }
  & Pick<SchemaTypes.MobilityModule, 'scenarios' | 'id' | 'companyId'>
);

export type ModuleWithScenarios_MyInfoModule_Fragment = (
  { __typename: 'MyInfoModule' }
  & Pick<SchemaTypes.MyInfoModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_NamirialSigningModule_Fragment = (
  { __typename: 'NamirialSigningModule' }
  & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_OidcModule_Fragment = (
  { __typename: 'OIDCModule' }
  & Pick<SchemaTypes.OidcModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_PayGatePaymentModule_Fragment = (
  { __typename: 'PayGatePaymentModule' }
  & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_PorscheIdModule_Fragment = (
  { __typename: 'PorscheIdModule' }
  & Pick<SchemaTypes.PorscheIdModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_PorscheMasterDataModule_Fragment = (
  { __typename: 'PorscheMasterDataModule' }
  & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_PorschePaymentModule_Fragment = (
  { __typename: 'PorschePaymentModule' }
  & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_PorscheRetainModule_Fragment = (
  { __typename: 'PorscheRetainModule' }
  & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_PromoCodeModule_Fragment = (
  { __typename: 'PromoCodeModule' }
  & Pick<SchemaTypes.PromoCodeModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_SalesControlBoardModule_Fragment = (
  { __typename: 'SalesControlBoardModule' }
  & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_SalesOfferModule_Fragment = (
  { __typename: 'SalesOfferModule' }
  & Pick<SchemaTypes.SalesOfferModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_SimpleVehicleManagementModule_Fragment = (
  { __typename: 'SimpleVehicleManagementModule' }
  & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_StandardApplicationModule_Fragment = (
  { __typename: 'StandardApplicationModule' }
  & Pick<SchemaTypes.StandardApplicationModule, 'scenarios' | 'id' | 'companyId'>
);

export type ModuleWithScenarios_TradeInModule_Fragment = (
  { __typename: 'TradeInModule' }
  & Pick<SchemaTypes.TradeInModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_TtbPaymentModule_Fragment = (
  { __typename: 'TtbPaymentModule' }
  & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_UserlikeChatbotModule_Fragment = (
  { __typename: 'UserlikeChatbotModule' }
  & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_VehicleDataWithPorscheCodeIntegrationModule_Fragment = (
  { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
  & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_VisitAppointmentModule_Fragment = (
  { __typename: 'VisitAppointmentModule' }
  & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_WebsiteModule_Fragment = (
  { __typename: 'WebsiteModule' }
  & Pick<SchemaTypes.WebsiteModule, 'id' | 'companyId'>
);

export type ModuleWithScenarios_WhatsappLiveChatModule_Fragment = (
  { __typename: 'WhatsappLiveChatModule' }
  & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'companyId'>
);

export type ModuleWithScenariosFragment = ModuleWithScenarios_AdyenPaymentModule_Fragment | ModuleWithScenarios_AppointmentModule_Fragment | ModuleWithScenarios_AutoplayModule_Fragment | ModuleWithScenarios_BankModule_Fragment | ModuleWithScenarios_BasicSigningModule_Fragment | ModuleWithScenarios_CapModule_Fragment | ModuleWithScenarios_ConfiguratorModule_Fragment | ModuleWithScenarios_ConsentsAndDeclarationsModule_Fragment | ModuleWithScenarios_CtsModule_Fragment | ModuleWithScenarios_DocusignModule_Fragment | ModuleWithScenarios_EventApplicationModule_Fragment | ModuleWithScenarios_FinderApplicationPrivateModule_Fragment | ModuleWithScenarios_FinderApplicationPublicModule_Fragment | ModuleWithScenarios_FinderVehicleManagementModule_Fragment | ModuleWithScenarios_FiservPaymentModule_Fragment | ModuleWithScenarios_GiftVoucherModule_Fragment | ModuleWithScenarios_InsuranceModule_Fragment | ModuleWithScenarios_LabelsModule_Fragment | ModuleWithScenarios_LaunchPadModule_Fragment | ModuleWithScenarios_LocalCustomerManagementModule_Fragment | ModuleWithScenarios_MaintenanceModule_Fragment | ModuleWithScenarios_MarketingModule_Fragment | ModuleWithScenarios_MobilityModule_Fragment | ModuleWithScenarios_MyInfoModule_Fragment | ModuleWithScenarios_NamirialSigningModule_Fragment | ModuleWithScenarios_OidcModule_Fragment | ModuleWithScenarios_PayGatePaymentModule_Fragment | ModuleWithScenarios_PorscheIdModule_Fragment | ModuleWithScenarios_PorscheMasterDataModule_Fragment | ModuleWithScenarios_PorschePaymentModule_Fragment | ModuleWithScenarios_PorscheRetainModule_Fragment | ModuleWithScenarios_PromoCodeModule_Fragment | ModuleWithScenarios_SalesControlBoardModule_Fragment | ModuleWithScenarios_SalesOfferModule_Fragment | ModuleWithScenarios_SimpleVehicleManagementModule_Fragment | ModuleWithScenarios_StandardApplicationModule_Fragment | ModuleWithScenarios_TradeInModule_Fragment | ModuleWithScenarios_TtbPaymentModule_Fragment | ModuleWithScenarios_UserlikeChatbotModule_Fragment | ModuleWithScenarios_VehicleDataWithPorscheCodeIntegrationModule_Fragment | ModuleWithScenarios_VisitAppointmentModule_Fragment | ModuleWithScenarios_WebsiteModule_Fragment | ModuleWithScenarios_WhatsappLiveChatModule_Fragment;

export const ModuleWithScenariosFragmentDoc = /*#__PURE__*/ gql`
    fragment ModuleWithScenarios on Module {
  __typename
  id
  companyId
  ... on StandardApplicationModule {
    scenarios
  }
  ... on ConfiguratorModule {
    scenarios
  }
  ... on MobilityModule {
    scenarios
  }
  ... on FinderApplicationPublicModule {
    scenarios
  }
  ... on FinderApplicationPrivateModule {
    scenarios
  }
}
    `;