import type * as SchemaTypes from '../types';

import type { BasicRouterLayoutSpecsFragment } from './BasicRouterLayoutSpecs';
import type { BasicProLayoutSpecsFragment } from './BasicProLayoutSpecs';
import type { EndpointListSpecs_ApplicationListEndpoint_Fragment, EndpointListSpecs_ConfiguratorApplicationEntrypoint_Fragment, EndpointListSpecs_CustomerListEndpoint_Fragment, EndpointListSpecs_DummyPrivatePageEndpoint_Fragment, EndpointListSpecs_DummyWelcomePageEndpoint_Fragment, EndpointListSpecs_EventApplicationEntrypoint_Fragment, EndpointListSpecs_FinderApplicationEntrypoint_Fragment, EndpointListSpecs_FinderApplicationPublicAccessEntrypoint_Fragment, EndpointListSpecs_LaunchPadApplicationEntrypoint_Fragment, EndpointListSpecs_LeadListEndpoint_Fragment, EndpointListSpecs_MobilityApplicationEntrypoint_Fragment, EndpointListSpecs_StandardApplicationEntrypoint_Fragment, EndpointListSpecs_StandardApplicationPublicAccessEntrypoint_Fragment, EndpointListSpecs_WebPageEndpoint_Fragment } from './EndpointListSpecs';
import type { DummyPrivatePageEndpointSpecsFragment } from './DummyPrivatePageEndpointSpecs';
import type { DummyWelcomePageEndpointSpecsFragment } from './DummyWelcomePageEndpointSpecs';
import type { StandardApplicationEntrypointSpecsFragment } from './StandardApplicationEntrypointSpecs';
import type { StandardApplicationPublicAccessEntrypointSpecsFragment } from './StandardApplicationPublicAccessEntrypointSpecs';
import type { DealerFragmentFragment } from './DealerFragment';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { CompanyContextDataFragment } from './CompanyContextData';
import type { LanguagePackContextDataFragment } from './LanguagePackContextData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { AvailableModulesDataFragment } from './AvailableModulesData';
import type { CompanyDealerDataFragment } from './CompanyDealerData';
import type { MaintenanceUpdateFragment } from './MaintenanceUpdate';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { UserAvatarSpecsFragment } from './UserAvatarSpecs';
import type { EdmEmailFooterPublicDataFragment } from './EdmEmailFooterPublicData';
import type { EdmSocialMediaDataFragment } from './EdmSocialMediaData';
import type { DealerContactFragmentFragment } from './DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from './DealerSocialMediaFragment';
import type { DealerDisclaimersFragmentFragment } from './DealerDisclaimersFragment';
import type { DealerIntegrationDetailsFragmentFragment } from './DealerIntegrationDetailsFragment';
import type { ModuleInDealerSpecs_AdyenPaymentModule_Fragment, ModuleInDealerSpecs_AppointmentModule_Fragment, ModuleInDealerSpecs_AutoplayModule_Fragment, ModuleInDealerSpecs_BankModule_Fragment, ModuleInDealerSpecs_BasicSigningModule_Fragment, ModuleInDealerSpecs_CapModule_Fragment, ModuleInDealerSpecs_ConfiguratorModule_Fragment, ModuleInDealerSpecs_ConsentsAndDeclarationsModule_Fragment, ModuleInDealerSpecs_CtsModule_Fragment, ModuleInDealerSpecs_DocusignModule_Fragment, ModuleInDealerSpecs_EventApplicationModule_Fragment, ModuleInDealerSpecs_FinderApplicationPrivateModule_Fragment, ModuleInDealerSpecs_FinderApplicationPublicModule_Fragment, ModuleInDealerSpecs_FinderVehicleManagementModule_Fragment, ModuleInDealerSpecs_FiservPaymentModule_Fragment, ModuleInDealerSpecs_GiftVoucherModule_Fragment, ModuleInDealerSpecs_InsuranceModule_Fragment, ModuleInDealerSpecs_LabelsModule_Fragment, ModuleInDealerSpecs_LaunchPadModule_Fragment, ModuleInDealerSpecs_LocalCustomerManagementModule_Fragment, ModuleInDealerSpecs_MaintenanceModule_Fragment, ModuleInDealerSpecs_MarketingModule_Fragment, ModuleInDealerSpecs_MobilityModule_Fragment, ModuleInDealerSpecs_MyInfoModule_Fragment, ModuleInDealerSpecs_NamirialSigningModule_Fragment, ModuleInDealerSpecs_OidcModule_Fragment, ModuleInDealerSpecs_PayGatePaymentModule_Fragment, ModuleInDealerSpecs_PorscheIdModule_Fragment, ModuleInDealerSpecs_PorscheMasterDataModule_Fragment, ModuleInDealerSpecs_PorschePaymentModule_Fragment, ModuleInDealerSpecs_PorscheRetainModule_Fragment, ModuleInDealerSpecs_PromoCodeModule_Fragment, ModuleInDealerSpecs_SalesControlBoardModule_Fragment, ModuleInDealerSpecs_SalesOfferModule_Fragment, ModuleInDealerSpecs_SimpleVehicleManagementModule_Fragment, ModuleInDealerSpecs_StandardApplicationModule_Fragment, ModuleInDealerSpecs_TradeInModule_Fragment, ModuleInDealerSpecs_TtbPaymentModule_Fragment, ModuleInDealerSpecs_UserlikeChatbotModule_Fragment, ModuleInDealerSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleInDealerSpecs_VisitAppointmentModule_Fragment, ModuleInDealerSpecs_WebsiteModule_Fragment, ModuleInDealerSpecs_WhatsappLiveChatModule_Fragment } from './ModuleInDealerSpecs';
import type { StandardApplicationModuleInDealerSpecsFragment } from './StandardApplicationModuleInDealerSpecs';
import type { DealerPriceDisclaimerDataFragment } from './DealerPriceDisclaimerData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { DealerVehiclesSpecsFragment } from './DealerVehiclesSpecs';
import type { DealerFinanceProductsSpecsFragment } from './DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from './FinanceProductListData';
import type { PeriodDataFragment } from './PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { DealerInsuranceProductsSpecsFragment } from './DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from './InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from './ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { StandardApplicationModuleEmailContentsSpecsFragment, StandardApplicationModuleEmailContentCustomerSpecsFragment, StandardApplicationModuleEmailContentShareSubmissionSpecsFragment, StandardApplicationModuleEmailContentSpecsFragment, StandardApplicationModuleEmailContentSalesPersonSpecsFragment } from './StandardApplicationModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerUploadedFileWithPreviewDataFragment } from './DealerUploadedFileWithPreview';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { EventApplicationModuleInDealerSpecsFragment } from './EventApplicationModuleInDealerSpecs';
import type { EventApplicationModuleEmailContentSpecsFragment } from './EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { ConfiguratorModuleInDealerSpecsFragment } from './ConfiguratorModuleInDealerSpecs';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { ConfiguratorModuleEmailContentSpecsFragment } from './ConfiguratorModuleEmailContentSpecs';
import type { MobilityModuleInDealerSpecsFragment } from './MobilityModuleInDealerSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from './MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from './MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from './MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from './MobilityOperatorEmailContentData';
import type { DealerBookingCodeSpecsFragment } from './DealerBookingCodeSpecs';
import type { MobilityHomeDeliveryDataFragment } from './MobilityHomeDeliveryData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { FinderApplicationPublicModuleInDealerSpecsFragment } from './FinderApplicationPublicModuleInDealerSpecs';
import type { FinderApplicationModuleEmailContentSpecsFragment } from './FinderApplicationModuleEmailContentSpecs';
import type { FinderApplicationPrivateModuleInDealerSpecsFragment } from './FinderApplicationPrivateModuleInDealerSpecs';
import type { AppointmentModuleInDealerSpecsFragment } from './AppointmentModuleInDealerSpecs';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { VisitAppointmentModuleInDealerSpecsFragment } from './VisitAppointmentModuleInDealerSpecs';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from './VisitAppointmentModuleEmailContentsSpecs';
import type { GiftVoucherModuleInDealerSpecsFragment } from './GiftVoucherModuleInDealerSpecs';
import type { GiftVoucherModuleEmailContentsSpecsFragment, GiftVoucherModuleEmailContentCustomerSpecsFragment, GiftVoucherModuleEmailDataFragment } from './GiftVoucherModuleEmailContentsSpecs';
import type { LaunchPadModuleInDealerSpecsFragment } from './LaunchPadModuleInDealerSpecs';
import type { SalesOfferModuleInDealerSpecsFragment } from './SalesOfferModuleInDealerSpecs';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from './SalesOfferModuleEmailContentsSpecs';
import type { SalesControlBoardModuleInDealerSpecsFragment } from './SalesControlBoardModuleInDealerSpecs';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from './DealerIntData';
import type { ApplicationListEndpointSpecsFragment } from './ApplicationListEndpointSpecs';
import type { LeadListEndpointSpecsFragment } from './LeadListEndpointSpecs';
import type { EventApplicationEntrypointSpecsFragment } from './EventApplicationEntrypointSpecs';
import type { PathScriptSpecsFragment } from './PathScriptSpecs';
import type { LaunchPadApplicationEntrypointSpecsFragment } from './LaunchPadApplicationEntrypointSpecs';
import type { ConfiguratorApplicationEntrypointSpecsFragment } from './ConfiguratorApplicationEntrypointSpecs';
import type { MobilityApplicationEntrypointSpecsFragment } from './MobilityApplicationEntrypointSpecs';
import type { CustomerListEndpointSpecsFragment } from './CustomerListEndpointSpecs';
import type { WebPageEndpointSpecsFragment } from './WebPageEndpointSpecs';
import type { WebPagePathDataFragment } from './WebPagePathData';
import type { FinderApplicationPublicAccessEntrypointSpecsFragment } from './FinderApplicationPublicAccessEntrypointSpecs';
import type { FinderApplicationEntrypointSpecsFragment } from './FinderApplicationEntrypointSpecs';
import type { MenuItemSpecs_MenuCustomPathItem_Fragment, MenuItemSpecs_MenuEndpointItem_Fragment, MenuItemSpecs_MenuLogoutActionItem_Fragment } from './MenuItemSpecs';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import { gql } from '@apollo/client';
import { BasicRouterLayoutSpecsFragmentDoc } from './BasicRouterLayoutSpecs';
import { BasicProLayoutSpecsFragmentDoc } from './BasicProLayoutSpecs';
import { EndpointListSpecsFragmentDoc } from './EndpointListSpecs';
import { DummyPrivatePageEndpointSpecsFragmentDoc } from './DummyPrivatePageEndpointSpecs';
import { DummyWelcomePageEndpointSpecsFragmentDoc } from './DummyWelcomePageEndpointSpecs';
import { StandardApplicationEntrypointSpecsFragmentDoc } from './StandardApplicationEntrypointSpecs';
import { StandardApplicationPublicAccessEntrypointSpecsFragmentDoc } from './StandardApplicationPublicAccessEntrypointSpecs';
import { DealerFragmentFragmentDoc } from './DealerFragment';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { CompanyContextDataFragmentDoc } from './CompanyContextData';
import { LanguagePackContextDataFragmentDoc } from './LanguagePackContextData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { AvailableModulesDataFragmentDoc } from './AvailableModulesData';
import { CompanyDealerDataFragmentDoc } from './CompanyDealerData';
import { MaintenanceUpdateFragmentDoc } from './MaintenanceUpdate';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { UserAvatarSpecsFragmentDoc } from './UserAvatarSpecs';
import { EdmEmailFooterPublicDataFragmentDoc } from './EdmEmailFooterPublicData';
import { EdmSocialMediaDataFragmentDoc } from './EdmSocialMediaData';
import { DealerContactFragmentFragmentDoc } from './DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from './DealerSocialMediaFragment';
import { DealerDisclaimersFragmentFragmentDoc } from './DealerDisclaimersFragment';
import { DealerIntegrationDetailsFragmentFragmentDoc } from './DealerIntegrationDetailsFragment';
import { ModuleInDealerSpecsFragmentDoc } from './ModuleInDealerSpecs';
import { StandardApplicationModuleInDealerSpecsFragmentDoc } from './StandardApplicationModuleInDealerSpecs';
import { DealerPriceDisclaimerDataFragmentDoc } from './DealerPriceDisclaimerData';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { DealerVehiclesSpecsFragmentDoc } from './DealerVehiclesSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from './DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from './FinanceProductListData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { DealerInsuranceProductsSpecsFragmentDoc } from './DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from './InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from './ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { StandardApplicationModuleEmailContentsSpecsFragmentDoc, StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc, StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc, StandardApplicationModuleEmailContentSpecsFragmentDoc, StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc } from './StandardApplicationModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from './DealerUploadedFileWithPreview';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { EventApplicationModuleInDealerSpecsFragmentDoc } from './EventApplicationModuleInDealerSpecs';
import { EventApplicationModuleEmailContentSpecsFragmentDoc } from './EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { ConfiguratorModuleInDealerSpecsFragmentDoc } from './ConfiguratorModuleInDealerSpecs';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from './ConfiguratorModuleEmailContentSpecs';
import { MobilityModuleInDealerSpecsFragmentDoc } from './MobilityModuleInDealerSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from './MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from './MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from './MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from './MobilityOperatorEmailContentData';
import { DealerBookingCodeSpecsFragmentDoc } from './DealerBookingCodeSpecs';
import { MobilityHomeDeliveryDataFragmentDoc } from './MobilityHomeDeliveryData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { FinderApplicationPublicModuleInDealerSpecsFragmentDoc } from './FinderApplicationPublicModuleInDealerSpecs';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from './FinderApplicationModuleEmailContentSpecs';
import { FinderApplicationPrivateModuleInDealerSpecsFragmentDoc } from './FinderApplicationPrivateModuleInDealerSpecs';
import { AppointmentModuleInDealerSpecsFragmentDoc } from './AppointmentModuleInDealerSpecs';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { VisitAppointmentModuleInDealerSpecsFragmentDoc } from './VisitAppointmentModuleInDealerSpecs';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from './VisitAppointmentModuleEmailContentsSpecs';
import { GiftVoucherModuleInDealerSpecsFragmentDoc } from './GiftVoucherModuleInDealerSpecs';
import { GiftVoucherModuleEmailContentsSpecsFragmentDoc, GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc, GiftVoucherModuleEmailDataFragmentDoc } from './GiftVoucherModuleEmailContentsSpecs';
import { LaunchPadModuleInDealerSpecsFragmentDoc } from './LaunchPadModuleInDealerSpecs';
import { SalesOfferModuleInDealerSpecsFragmentDoc } from './SalesOfferModuleInDealerSpecs';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from './SalesOfferModuleEmailContentsSpecs';
import { SalesControlBoardModuleInDealerSpecsFragmentDoc } from './SalesControlBoardModuleInDealerSpecs';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from './DealerIntData';
import { ApplicationListEndpointSpecsFragmentDoc } from './ApplicationListEndpointSpecs';
import { LeadListEndpointSpecsFragmentDoc } from './LeadListEndpointSpecs';
import { EventApplicationEntrypointSpecsFragmentDoc } from './EventApplicationEntrypointSpecs';
import { PathScriptSpecsFragmentDoc } from './PathScriptSpecs';
import { LaunchPadApplicationEntrypointSpecsFragmentDoc } from './LaunchPadApplicationEntrypointSpecs';
import { ConfiguratorApplicationEntrypointSpecsFragmentDoc } from './ConfiguratorApplicationEntrypointSpecs';
import { MobilityApplicationEntrypointSpecsFragmentDoc } from './MobilityApplicationEntrypointSpecs';
import { CustomerListEndpointSpecsFragmentDoc } from './CustomerListEndpointSpecs';
import { WebPageEndpointSpecsFragmentDoc } from './WebPageEndpointSpecs';
import { WebPagePathDataFragmentDoc } from './WebPagePathData';
import { FinderApplicationPublicAccessEntrypointSpecsFragmentDoc } from './FinderApplicationPublicAccessEntrypointSpecs';
import { FinderApplicationEntrypointSpecsFragmentDoc } from './FinderApplicationEntrypointSpecs';
import { MenuItemSpecsFragmentDoc } from './MenuItemSpecs';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
export type RouterFinderApplicationModuleFragment = (
  { __typename: 'FinderApplicationPublicModule' }
  & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName' | 'vehicleModuleId' | 'bankModuleId' | 'finderVehicleConditions'>
);

export type RouterFinderApplicationPrivateModuleFragment = (
  { __typename: 'FinderApplicationPrivateModule' }
  & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName' | 'vehicleModuleId' | 'bankModuleId' | 'finderVehicleConditions'>
);

export type RouterSpecsFragment = (
  { __typename: 'Router' }
  & Pick<SchemaTypes.Router, 'id' | 'withAdmin' | 'pathname' | 'hostname' | 'googleTagManagerId' | 'pixelId' | 'linkedInInsightTagId' | 'blueKaiId' | 'tikTokId' | 'snapChatId' | 'companyId' | 'permissions'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'timeZone'>
    & { modules: Array<(
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
      & RouterFinderApplicationPrivateModuleFragment
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
      & RouterFinderApplicationModuleFragment
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    )> }
  ), layout?: SchemaTypes.Maybe<(
    { __typename: 'BasicLayout' }
    & BasicRouterLayoutSpecsFragment
  ) | (
    { __typename: 'BasicProLayout' }
    & BasicProLayoutSpecsFragment
  ) | { __typename: 'PorscheV3Layout' }>, endpoints: Array<(
    { __typename: 'ApplicationListEndpoint' }
    & EndpointListSpecs_ApplicationListEndpoint_Fragment
  ) | (
    { __typename: 'ConfiguratorApplicationEntrypoint' }
    & EndpointListSpecs_ConfiguratorApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'CustomerListEndpoint' }
    & EndpointListSpecs_CustomerListEndpoint_Fragment
  ) | (
    { __typename: 'DummyPrivatePageEndpoint' }
    & EndpointListSpecs_DummyPrivatePageEndpoint_Fragment
  ) | (
    { __typename: 'DummyWelcomePageEndpoint' }
    & EndpointListSpecs_DummyWelcomePageEndpoint_Fragment
  ) | (
    { __typename: 'EventApplicationEntrypoint' }
    & EndpointListSpecs_EventApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'FinderApplicationEntrypoint' }
    & EndpointListSpecs_FinderApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicAccessEntrypoint' }
    & EndpointListSpecs_FinderApplicationPublicAccessEntrypoint_Fragment
  ) | (
    { __typename: 'LaunchPadApplicationEntrypoint' }
    & EndpointListSpecs_LaunchPadApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'LeadListEndpoint' }
    & EndpointListSpecs_LeadListEndpoint_Fragment
  ) | (
    { __typename: 'MobilityApplicationEntrypoint' }
    & EndpointListSpecs_MobilityApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'StandardApplicationEntrypoint' }
    & EndpointListSpecs_StandardApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'StandardApplicationPublicAccessEntrypoint' }
    & EndpointListSpecs_StandardApplicationPublicAccessEntrypoint_Fragment
  ) | (
    { __typename: 'WebPageEndpoint' }
    & EndpointListSpecs_WebPageEndpoint_Fragment
  )>, menuItems: Array<(
    { __typename: 'MenuCustomPathItem' }
    & MenuItemSpecs_MenuCustomPathItem_Fragment
  ) | (
    { __typename: 'MenuEndpointItem' }
    & MenuItemSpecs_MenuEndpointItem_Fragment
  ) | (
    { __typename: 'MenuLogoutActionItem' }
    & MenuItemSpecs_MenuLogoutActionItem_Fragment
  )>, languages: Array<(
    { __typename: 'LanguagePack' }
    & Pick<SchemaTypes.LanguagePack, 'id' | 'code' | 'referenceName' | 'displayName'>
  )>, pathScripts: Array<(
    { __typename: 'PathScript' }
    & PathScriptSpecsFragment
  )>, versioning: (
    { __typename: 'SimpleVersioning' }
    & SimpleVersioningDataFragment
  ) }
);

export const RouterFinderApplicationModuleFragmentDoc = /*#__PURE__*/ gql`
    fragment RouterFinderApplicationModule on FinderApplicationPublicModule {
  __typename
  id
  displayName
  vehicleModuleId
  bankModuleId
  finderVehicleConditions
}
    `;
export const RouterFinderApplicationPrivateModuleFragmentDoc = /*#__PURE__*/ gql`
    fragment RouterFinderApplicationPrivateModule on FinderApplicationPrivateModule {
  __typename
  id
  displayName
  vehicleModuleId
  bankModuleId
  finderVehicleConditions
}
    `;
export const RouterSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment RouterSpecs on Router {
  id
  withAdmin
  pathname
  hostname
  googleTagManagerId
  pixelId
  linkedInInsightTagId
  blueKaiId
  tikTokId
  snapChatId
  companyId
  company {
    id
    displayName
    timeZone
    modules {
      __typename
      id
      displayName
      ... on FinderApplicationPublicModule {
        ...RouterFinderApplicationModule
      }
      ... on FinderApplicationPrivateModule {
        ...RouterFinderApplicationPrivateModule
      }
    }
  }
  layout {
    ... on BasicLayout {
      ...BasicRouterLayoutSpecs
    }
    ... on BasicProLayout {
      ...BasicProLayoutSpecs
    }
  }
  endpoints {
    ...EndpointListSpecs
  }
  menuItems {
    ...MenuItemSpecs
  }
  languages {
    id
    code
    referenceName
    displayName
  }
  pathScripts {
    ...PathScriptSpecs
  }
  permissions
  versioning {
    ...SimpleVersioningData
  }
}
    `;