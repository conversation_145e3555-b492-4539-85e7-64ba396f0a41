import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type ModulesOptionsData_AdyenPaymentModule_Fragment = (
  { __typename: 'AdyenPaymentModule' }
  & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_AppointmentModule_Fragment = (
  { __typename: 'AppointmentModule' }
  & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_AutoplayModule_Fragment = (
  { __typename: 'AutoplayModule' }
  & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_BankModule_Fragment = (
  { __typename: 'BankModule' }
  & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_BasicSigningModule_Fragment = (
  { __typename: 'BasicSigningModule' }
  & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_CapModule_Fragment = (
  { __typename: 'CapModule' }
  & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_ConfiguratorModule_Fragment = (
  { __typename: 'ConfiguratorModule' }
  & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_ConsentsAndDeclarationsModule_Fragment = (
  { __typename: 'ConsentsAndDeclarationsModule' }
  & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_CtsModule_Fragment = (
  { __typename: 'CtsModule' }
  & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_DocusignModule_Fragment = (
  { __typename: 'DocusignModule' }
  & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_EventApplicationModule_Fragment = (
  { __typename: 'EventApplicationModule' }
  & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_FinderApplicationPrivateModule_Fragment = (
  { __typename: 'FinderApplicationPrivateModule' }
  & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_FinderApplicationPublicModule_Fragment = (
  { __typename: 'FinderApplicationPublicModule' }
  & Pick<SchemaTypes.FinderApplicationPublicModule, 'bankModuleId' | 'vehicleModuleId' | 'finderVehicleConditions' | 'id' | 'displayName'>
);

export type ModulesOptionsData_FinderVehicleManagementModule_Fragment = (
  { __typename: 'FinderVehicleManagementModule' }
  & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_FiservPaymentModule_Fragment = (
  { __typename: 'FiservPaymentModule' }
  & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_GiftVoucherModule_Fragment = (
  { __typename: 'GiftVoucherModule' }
  & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_InsuranceModule_Fragment = (
  { __typename: 'InsuranceModule' }
  & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_LabelsModule_Fragment = (
  { __typename: 'LabelsModule' }
  & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_LaunchPadModule_Fragment = (
  { __typename: 'LaunchPadModule' }
  & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_LocalCustomerManagementModule_Fragment = (
  { __typename: 'LocalCustomerManagementModule' }
  & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_MaintenanceModule_Fragment = (
  { __typename: 'MaintenanceModule' }
  & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_MarketingModule_Fragment = (
  { __typename: 'MarketingModule' }
  & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_MobilityModule_Fragment = (
  { __typename: 'MobilityModule' }
  & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_MyInfoModule_Fragment = (
  { __typename: 'MyInfoModule' }
  & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_NamirialSigningModule_Fragment = (
  { __typename: 'NamirialSigningModule' }
  & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_OidcModule_Fragment = (
  { __typename: 'OIDCModule' }
  & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_PayGatePaymentModule_Fragment = (
  { __typename: 'PayGatePaymentModule' }
  & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_PorscheIdModule_Fragment = (
  { __typename: 'PorscheIdModule' }
  & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_PorscheMasterDataModule_Fragment = (
  { __typename: 'PorscheMasterDataModule' }
  & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_PorschePaymentModule_Fragment = (
  { __typename: 'PorschePaymentModule' }
  & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_PorscheRetainModule_Fragment = (
  { __typename: 'PorscheRetainModule' }
  & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_PromoCodeModule_Fragment = (
  { __typename: 'PromoCodeModule' }
  & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_SalesControlBoardModule_Fragment = (
  { __typename: 'SalesControlBoardModule' }
  & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_SalesOfferModule_Fragment = (
  { __typename: 'SalesOfferModule' }
  & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_SimpleVehicleManagementModule_Fragment = (
  { __typename: 'SimpleVehicleManagementModule' }
  & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_StandardApplicationModule_Fragment = (
  { __typename: 'StandardApplicationModule' }
  & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_TradeInModule_Fragment = (
  { __typename: 'TradeInModule' }
  & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_TtbPaymentModule_Fragment = (
  { __typename: 'TtbPaymentModule' }
  & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_UserlikeChatbotModule_Fragment = (
  { __typename: 'UserlikeChatbotModule' }
  & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_VehicleDataWithPorscheCodeIntegrationModule_Fragment = (
  { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
  & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_VisitAppointmentModule_Fragment = (
  { __typename: 'VisitAppointmentModule' }
  & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_WebsiteModule_Fragment = (
  { __typename: 'WebsiteModule' }
  & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
);

export type ModulesOptionsData_WhatsappLiveChatModule_Fragment = (
  { __typename: 'WhatsappLiveChatModule' }
  & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
);

export type ModulesOptionsDataFragment = ModulesOptionsData_AdyenPaymentModule_Fragment | ModulesOptionsData_AppointmentModule_Fragment | ModulesOptionsData_AutoplayModule_Fragment | ModulesOptionsData_BankModule_Fragment | ModulesOptionsData_BasicSigningModule_Fragment | ModulesOptionsData_CapModule_Fragment | ModulesOptionsData_ConfiguratorModule_Fragment | ModulesOptionsData_ConsentsAndDeclarationsModule_Fragment | ModulesOptionsData_CtsModule_Fragment | ModulesOptionsData_DocusignModule_Fragment | ModulesOptionsData_EventApplicationModule_Fragment | ModulesOptionsData_FinderApplicationPrivateModule_Fragment | ModulesOptionsData_FinderApplicationPublicModule_Fragment | ModulesOptionsData_FinderVehicleManagementModule_Fragment | ModulesOptionsData_FiservPaymentModule_Fragment | ModulesOptionsData_GiftVoucherModule_Fragment | ModulesOptionsData_InsuranceModule_Fragment | ModulesOptionsData_LabelsModule_Fragment | ModulesOptionsData_LaunchPadModule_Fragment | ModulesOptionsData_LocalCustomerManagementModule_Fragment | ModulesOptionsData_MaintenanceModule_Fragment | ModulesOptionsData_MarketingModule_Fragment | ModulesOptionsData_MobilityModule_Fragment | ModulesOptionsData_MyInfoModule_Fragment | ModulesOptionsData_NamirialSigningModule_Fragment | ModulesOptionsData_OidcModule_Fragment | ModulesOptionsData_PayGatePaymentModule_Fragment | ModulesOptionsData_PorscheIdModule_Fragment | ModulesOptionsData_PorscheMasterDataModule_Fragment | ModulesOptionsData_PorschePaymentModule_Fragment | ModulesOptionsData_PorscheRetainModule_Fragment | ModulesOptionsData_PromoCodeModule_Fragment | ModulesOptionsData_SalesControlBoardModule_Fragment | ModulesOptionsData_SalesOfferModule_Fragment | ModulesOptionsData_SimpleVehicleManagementModule_Fragment | ModulesOptionsData_StandardApplicationModule_Fragment | ModulesOptionsData_TradeInModule_Fragment | ModulesOptionsData_TtbPaymentModule_Fragment | ModulesOptionsData_UserlikeChatbotModule_Fragment | ModulesOptionsData_VehicleDataWithPorscheCodeIntegrationModule_Fragment | ModulesOptionsData_VisitAppointmentModule_Fragment | ModulesOptionsData_WebsiteModule_Fragment | ModulesOptionsData_WhatsappLiveChatModule_Fragment;

export const ModulesOptionsDataFragmentDoc = /*#__PURE__*/ gql`
    fragment ModulesOptionsData on Module {
  id
  displayName
  ... on FinderApplicationPublicModule {
    bankModuleId
    vehicleModuleId
    finderVehicleConditions
  }
}
    `;