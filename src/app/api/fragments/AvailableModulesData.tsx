import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type AvailableModulesDataFragment = (
  { __typename: 'AvailableModules' }
  & Pick<SchemaTypes.AvailableModules, 'hasApplicationModules' | 'hasConfiguratorModules' | 'hasCustomerModules' | 'hasVehicleManagementModules' | 'hasFinderVehicleManagementModules' | 'hasPromotionCodeModules' | 'hasInventories' | 'hasEventModules' | 'hasAgreementModules' | 'hasFinanceProducts' | 'hasPaymentModules' | 'hasBanks' | 'hasSystemBanks' | 'hasInsuranceModules' | 'hasLabelsModules' | 'hasMobilityModules' | 'hasWebsiteModules' | 'hasCtsModules' | 'hasPorscheMasterDataModules' | 'hasConsentModules' | 'hasCapModules' | 'hasPorscheRetainModules' | 'hasAppointmentModules' | 'hasAutoplayModules' | 'hasTradeInModules' | 'hasGiftVoucherModules' | 'hasVisitAppointmentModules' | 'hasMarketingModules' | 'hasLaunchPadModules' | 'hasSalesControlBoardModule'>
);

export const AvailableModulesDataFragmentDoc = /*#__PURE__*/ gql`
    fragment AvailableModulesData on AvailableModules {
  hasApplicationModules
  hasConfiguratorModules
  hasCustomerModules
  hasVehicleManagementModules
  hasFinderVehicleManagementModules
  hasPromotionCodeModules
  hasInventories
  hasEventModules
  hasAgreementModules
  hasFinanceProducts
  hasPaymentModules
  hasBanks
  hasSystemBanks
  hasInsuranceModules
  hasLabelsModules
  hasMobilityModules
  hasWebsiteModules
  hasCtsModules
  hasPorscheMasterDataModules
  hasConsentModules
  hasCapModules
  hasPorscheRetainModules
  hasAppointmentModules
  hasAutoplayModules
  hasTradeInModules
  hasGiftVoucherModules
  hasVisitAppointmentModules
  hasMarketingModules
  hasLaunchPadModules
  hasSalesControlBoardModule
}
    `;