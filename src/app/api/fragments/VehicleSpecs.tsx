import type * as SchemaTypes from '../types';

import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import { gql } from '@apollo/client';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
export type VehicleSpecs_FinderVehicle_Fragment = (
  { __typename: 'FinderVehicle' }
  & Pick<SchemaTypes.FinderVehicle, 'id' | 'moduleId'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
  & FinderVehicleSpecsFragment
);

export type VehicleSpecs_LocalMake_Fragment = (
  { __typename: 'LocalMake' }
  & Pick<SchemaTypes.LocalMake, 'id' | 'moduleId'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
  & LocalMakeSpecsFragment
);

export type VehicleSpecs_LocalModel_Fragment = (
  { __typename: 'LocalModel' }
  & Pick<SchemaTypes.LocalModel, 'id' | 'moduleId'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
  & LocalModelSpecsFragment
);

export type VehicleSpecs_LocalVariant_Fragment = (
  { __typename: 'LocalVariant' }
  & Pick<SchemaTypes.LocalVariant, 'id' | 'moduleId'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
  & LocalVariantSpecsFragment
);

export type VehicleSpecsFragment = VehicleSpecs_FinderVehicle_Fragment | VehicleSpecs_LocalMake_Fragment | VehicleSpecs_LocalModel_Fragment | VehicleSpecs_LocalVariant_Fragment;

export const VehicleSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment VehicleSpecs on Vehicle {
  __typename
  id
  moduleId
  module {
    company {
      timeZone
    }
  }
  ... on LocalVariant {
    ...LocalVariantSpecs
  }
  ... on LocalModel {
    ...LocalModelSpecs
  }
  ... on LocalMake {
    ...LocalMakeSpecs
  }
  ... on FinderVehicle {
    ...FinderVehicleSpecs
  }
  versioning {
    ...AdvancedVersioningData
  }
}
    `;