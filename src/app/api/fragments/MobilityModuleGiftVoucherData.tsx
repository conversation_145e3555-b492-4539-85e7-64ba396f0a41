import type * as SchemaTypes from '../types';

import type { DateUnitDataFragment } from './DateUnitData';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import { gql } from '@apollo/client';
import { DateUnitDataFragmentDoc } from './DateUnitData';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
export type MobilityModuleGiftVoucherDataFragment = (
  { __typename: 'MobilityModule' }
  & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName' | 'vehicleModuleId'>
  & { availableNumberOfBookingRange: (
    { __typename: 'DateUnit' }
    & DateUnitDataFragment
  ), company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
  ), customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
    { __typename: 'LocalCustomerManagementModule' }
    & { extraSettings: (
      { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
      & KycExtraSettingsSpecsFragment
    ) }
  ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' } }
);

export const MobilityModuleGiftVoucherDataFragmentDoc = /*#__PURE__*/ gql`
    fragment MobilityModuleGiftVoucherData on MobilityModule {
  id
  displayName
  vehicleModuleId
  availableNumberOfBookingRange {
    ...DateUnitData
  }
  company {
    id
    displayName
    currency
  }
  customerModule {
    ... on LocalCustomerManagementModule {
      extraSettings {
        ...KYCExtraSettingsSpecs
      }
    }
  }
}
    `;