import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type PorscheVehicleDataSpecsFragment = (
  { __typename: 'PorscheVehicleData' }
  & Pick<SchemaTypes.PorscheVehicleData, 'orderCodeType' | 'basePrice' | 'basePriceString' | 'totalPrice' | 'totalPriceString' | 'name' | 'vehicleId'>
  & { exteriorColor?: SchemaTypes.Maybe<(
    { __typename: 'PorscheVehicleDataFeature' }
    & PorscheVehicleDataFeatureSpecsFragment
  )>, interiorColor?: SchemaTypes.Maybe<(
    { __typename: 'PorscheVehicleDataFeature' }
    & PorscheVehicleDataFeatureSpecsFragment
  )>, wheels?: SchemaTypes.Maybe<(
    { __typename: 'PorscheVehicleDataFeature' }
    & PorscheVehicleDataFeatureSpecsFragment
  )>, specialEquipments: Array<(
    { __typename: 'PorscheVehicleDataFeature' }
    & PorscheVehicleDataFeatureSpecsFragment
  )>, images: Array<(
    { __typename: 'PorscheVehicleImages' }
    & PorscheVehicleImagesSpecsFragment
  )>, others?: SchemaTypes.Maybe<(
    { __typename: 'PorscheVehicleDataFeature' }
    & PorscheVehicleDataFeatureSpecsFragment
  )> }
);

export type PorscheVehicleDataFeatureSpecsFragment = (
  { __typename: 'PorscheVehicleDataFeature' }
  & Pick<SchemaTypes.PorscheVehicleDataFeature, 'key' | 'featureName' | 'featurePriceString' | 'featurePriceNumber' | 'featureOptionCode' | 'featureOptionType'>
);

export type PorscheVehicleImagesSpecsFragment = (
  { __typename: 'PorscheVehicleImages' }
  & Pick<SchemaTypes.PorscheVehicleImages, 'url' | 'key'>
);

export const PorscheVehicleDataFeatureSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment PorscheVehicleDataFeatureSpecs on PorscheVehicleDataFeature {
  key
  featureName
  featurePriceString
  featurePriceNumber
  featureOptionCode
  featureOptionType
}
    `;
export const PorscheVehicleImagesSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment PorscheVehicleImagesSpecs on PorscheVehicleImages {
  url
  key
}
    `;
export const PorscheVehicleDataSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment PorscheVehicleDataSpecs on PorscheVehicleData {
  orderCodeType
  exteriorColor {
    ...PorscheVehicleDataFeatureSpecs
  }
  interiorColor {
    ...PorscheVehicleDataFeatureSpecs
  }
  wheels {
    ...PorscheVehicleDataFeatureSpecs
  }
  specialEquipments {
    ...PorscheVehicleDataFeatureSpecs
  }
  images {
    ...PorscheVehicleImagesSpecs
  }
  others {
    ...PorscheVehicleDataFeatureSpecs
  }
  basePrice
  basePriceString
  totalPrice
  totalPriceString
  name
  vehicleId
}
    `;