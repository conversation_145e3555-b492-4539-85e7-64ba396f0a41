fragment ModuleSpecs on Module {
    __typename
    id
    companyId

    company {
        id
        currency
        displayName
        timeZone
        countryCode
    }

    ... on ConsentsAndDeclarationsModule {
        ...ConsentsAndDeclarationsModuleSpecs
    }

    ... on SimpleVehicleManagementModule {
        ...SimpleVehicleManagementModuleSpecs
    }

    ... on LocalCustomerManagementModule {
        ...LocalCustomerManagementModuleSpecs
    }

    ... on BankModule {
        ...BankModuleSpecs
    }

    ... on BasicSigningModule {
        ...BasicSigningModuleSpecs
    }

    ... on NamirialSigningModule {
        ...NamirialSigningModuleSpecs
    }

    ... on StandardApplicationModule {
        ...StandardApplicationModuleSpecs
    }

    ... on EventApplicationModule {
        ...EventApplicationModuleSpecs
    }

    ... on AdyenPaymentModule {
        ...AdyenPaymentModuleSpecs
    }

    ... on PorschePaymentModule {
        ...PorschePaymentModuleSpecs
    }

    ... on FiservPaymentModule {
        ...FiservPaymentModuleSpecs
    }

    ... on PayGatePaymentModule {
        ...PayGatePaymentModuleSpecs
    }

    ... on TtbPaymentModule {
        ...TtbPaymentModuleSpecs
    }

    ... on MyInfoModule {
        ...MyInfoModuleSpecs
    }

    ... on ConfiguratorModule {
        ...ConfiguratorModuleSpecs
    }

    ... on WhatsappLiveChatModule {
        ...WhatsappLiveChatModuleSpecs
    }

    ... on UserlikeChatbotModule {
        ...UserlikeChatbotModuleSpecs
    }

    ... on PromoCodeModule {
        ...PromoCodeModuleSpecs
    }

    ... on MaintenanceModule {
        ...MaintenanceModuleSpecs
    }

    ... on WebsiteModule {
        ...WebsiteModuleSpecs
    }

    ... on MobilityModule {
        ...MobilityModuleSpecs
    }

    ... on LabelsModule {
        ...LabelsModuleSpecs
    }

    ... on FinderVehicleManagementModule {
        ...FinderVehicleManagementModuleSpecs
    }

    ... on FinderApplicationPublicModule {
        ...FinderApplicationPublicModuleSpecs
    }

    ... on FinderApplicationPrivateModule {
        ...FinderApplicationPrivateModuleSpecs
    }

    ... on AutoplayModule {
        ...AutoplayModuleSpecs
    }

    ... on CtsModule {
        ...CtsModuleSpecs
    }

    ... on AppointmentModule {
        ...AppointmentModuleSpecs
    }

    ... on InsuranceModule {
        ...InsuranceModuleSpecs
    }

    ... on PorscheMasterDataModule {
        ...PorscheMasterDataModuleSpecs
    }

    ... on GiftVoucherModule {
        ...GiftVoucherModuleSpecs
    }

    ... on TradeInModule {
        ...TradeInModuleSpecs
    }

    ... on CapModule {
        ...CapModuleSpecs
    }

    ... on PorscheIdModule {
        ...PorscheIdModuleSpecs
    }

    ... on PorscheRetainModule {
        ...PorscheRetainModuleSpecs
    }

    ... on DocusignModule {
        ...DocusignModuleSpecs
    }

    ... on LaunchPadModule {
        ...LaunchPadModuleSpecs
    }

    ... on VisitAppointmentModule {
        ...VisitAppointmentModuleSpecs
    }

    ... on OIDCModule {
        ...OIDCModuleSpecs
    }

    ... on MarketingModule {
        ...MarketingModuleSpecs
    }

    ... on SalesOfferModule {
        ...SalesOfferModuleSpecs
    }

    ... on VehicleDataWithPorscheCodeIntegrationModule {
        ...VehicleDataWithPorscheCodeIntegrationModuleSpecs
    }

    ... on SalesControlBoardModule {
        ...SalesControlBoardModuleSpecs
    }
}
