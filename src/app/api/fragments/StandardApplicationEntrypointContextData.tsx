import type * as SchemaTypes from '../types';

import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { AppointmentModuleSpecsFragment } from './AppointmentModuleSpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { NamirialSigningModuleSpecsFragment } from './NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from './NamirialSettingsSpec';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { VisitAppointmentModuleSpecsFragment } from './VisitAppointmentModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from './VisitAppointmentModuleEmailContentsSpecs';
import type { InsurerEntrypointContextDataFragment } from './InsurerEntrypointContextData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { FlexibleDiscountDataFragment } from './FlexibleDiscountData';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import { gql } from '@apollo/client';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { AppointmentModuleSpecsFragmentDoc } from './AppointmentModuleSpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { NamirialSigningModuleSpecsFragmentDoc } from './NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from './NamirialSettingsSpec';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { VisitAppointmentModuleSpecsFragmentDoc } from './VisitAppointmentModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from './VisitAppointmentModuleEmailContentsSpecs';
import { InsurerEntrypointContextDataFragmentDoc } from './InsurerEntrypointContextData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { FlexibleDiscountDataFragmentDoc } from './FlexibleDiscountData';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
export type StandardApplicationEntrypointContextDataFragment = (
  { __typename: 'StandardApplicationEntrypoint' }
  & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname' | 'displayName'>
  & { applicationModule: (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'market' | 'tradeIn' | 'isTradeInAmountVisible' | 'testDrive' | 'isFinancingOptional' | 'isInsuranceOptional' | 'showFinanceCalculator' | 'showInsuranceCalculator' | 'vehicleModuleId' | 'bankModuleId' | 'insuranceModuleId' | 'scenarios' | 'myInfoSettingId' | 'isOcrEnabled' | 'hasDealerOptions' | 'includeDealerOptionsForFinancing' | 'permissions' | 'showFromValueOnVehicleDetails' | 'bankDisplayPreference' | 'displayAppointmentDatepicker' | 'insurerDisplayPreference' | 'insurerIds'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ), marketType: (
      { __typename: 'DefaultApplicationMarket' }
      & ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment
    ) | (
      { __typename: 'NewZealandApplicationMarket' }
      & ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment
    ) | (
      { __typename: 'SingaporeApplicationMarket' }
      & ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment
    ), dealerPriceDisclaimer?: SchemaTypes.Maybe<(
      { __typename: 'DealerPriceDisclaimer' }
      & { defaultValue: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), overrides: Array<(
        { __typename: 'DealerPriceDisclaimerOverride' }
        & Pick<SchemaTypes.DealerPriceDisclaimerOverride, 'dealerId'>
        & { value: (
          { __typename: 'TranslatedString' }
          & TranslatedStringDataFragment
        ) }
      )> }
    )>, promoCodeModule?: SchemaTypes.Maybe<(
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    )>, paymentSetting?: SchemaTypes.Maybe<(
      { __typename: 'DealershipMyInfoSetting' }
      & DealershipSettingSpecData_DealershipMyInfoSetting_Fragment
    ) | (
      { __typename: 'DealershipPaymentSetting' }
      & DealershipSettingSpecData_DealershipPaymentSetting_Fragment
    ) | (
      { __typename: 'DealershipPublicSalesPerson' }
      & DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment
    )>, appointmentModule?: SchemaTypes.Maybe<{ __typename: 'AdyenPaymentModule' } | (
      { __typename: 'AppointmentModule' }
      & AppointmentModuleSpecsFragment
    ) | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }>, visitAppointmentModule?: SchemaTypes.Maybe<{ __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | (
      { __typename: 'VisitAppointmentModule' }
      & VisitAppointmentModuleSpecsFragment
    ) | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }>, insurers: Array<(
      { __typename: 'Insurer' }
      & InsurerEntrypointContextDataFragment
    )>, flexibleDiscount: (
      { __typename: 'FlexibleDiscount' }
      & FlexibleDiscountDataFragment
    ), customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' } }
  ) }
);

export const StandardApplicationEntrypointContextDataFragmentDoc = /*#__PURE__*/ gql`
    fragment StandardApplicationEntrypointContextData on StandardApplicationEntrypoint {
  id
  pathname
  displayName
  applicationModule {
    id
    company {
      id
      timeZone
      countryCode
    }
    market
    marketType {
      ...ApplicationMarketTypeFragment
    }
    dealerPriceDisclaimer {
      defaultValue {
        ...TranslatedStringData
      }
      overrides {
        dealerId
        value {
          ...TranslatedStringData
        }
      }
    }
    tradeIn
    isTradeInAmountVisible
    testDrive
    isFinancingOptional
    isInsuranceOptional
    showFinanceCalculator
    showInsuranceCalculator
    vehicleModuleId
    bankModuleId
    insuranceModuleId
    scenarios
    myInfoSettingId
    isOcrEnabled
    hasDealerOptions
    includeDealerOptionsForFinancing
    permissions
    promoCodeModule {
      id
      displayName
    }
    showFromValueOnVehicleDetails
    bankDisplayPreference
    paymentSetting {
      ...DealershipSettingSpecData
    }
    appointmentModule {
      ...AppointmentModuleSpecs
    }
    visitAppointmentModule {
      ...VisitAppointmentModuleSpecs
    }
    displayAppointmentDatepicker
    insurerDisplayPreference
    insurerIds
    insurers {
      ...InsurerEntrypointContextData
    }
    flexibleDiscount {
      ...FlexibleDiscountData
    }
    customerModule {
      ... on LocalCustomerManagementModule {
        extraSettings {
          ...KYCExtraSettingsSpecs
        }
      }
    }
  }
}
    `;