import type * as SchemaTypes from '../types';

import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { NamirialSigningModuleSpecsFragment } from './NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from './NamirialSettingsSpec';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import { gql } from '@apollo/client';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { NamirialSigningModuleSpecsFragmentDoc } from './NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from './NamirialSettingsSpec';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
export type AppointmentModuleSpecsFragment = (
  { __typename: 'AppointmentModule' }
  & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName' | 'unavailableDayOfWeek' | 'advancedBookingLimit' | 'maxAdvancedBookingLimit' | 'showRemoteFlowButtonInKYCPage' | 'hasTestDriveProcess' | 'hasTestDriveSigning' | 'isReminderTimeEnabled' | 'timeToSendReminder' | 'signingModuleId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'timeZone'>
  ), versioning: (
    { __typename: 'SimpleVersioning' }
    & SimpleVersioningDataFragment
  ), bookingTimeSlot: Array<(
    { __typename: 'AppointmentTimeSlot' }
    & AppointmentTimeSlotDataFragment
  )>, bookingInformation?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, signingModule?: SchemaTypes.Maybe<{ __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | (
    { __typename: 'NamirialSigningModule' }
    & NamirialSigningModuleSpecsFragment
  ) | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }>, emailContents: (
    { __typename: 'AppointmentModuleEmailContents' }
    & AppointmentModuleEmailContentsSpecsFragment
  ) }
);

export const AppointmentModuleSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment AppointmentModuleSpecs on AppointmentModule {
  id
  company {
    id
    displayName
    timeZone
  }
  displayName
  versioning {
    ...SimpleVersioningData
  }
  unavailableDayOfWeek
  bookingTimeSlot {
    ...AppointmentTimeSlotData
  }
  advancedBookingLimit
  maxAdvancedBookingLimit
  bookingInformation {
    ...TranslatedStringData
  }
  showRemoteFlowButtonInKYCPage
  hasTestDriveProcess
  hasTestDriveSigning
  isReminderTimeEnabled
  timeToSendReminder
  signingModuleId
  signingModule {
    ...NamirialSigningModuleSpecs
  }
  emailContents {
    ...AppointmentModuleEmailContentsSpecs
  }
}
    `;