import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { InventoryDetailsPublicData_ConfiguratorInventory_Fragment, InventoryDetailsPublicData_MobilityInventory_Fragment } from './InventoryDetailsPublicData';
import type { StockInventorySpecs_ConfiguratorStockInventory_Fragment, StockInventorySpecs_MobilityStockInventory_Fragment } from './StockInventorySpecs';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from './ApplicationStageData';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from './ReferenceApplicationData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { TradeInVehicleDataFragment } from './TradeInVehicleData';
import type { CompanyPublicSpecsFragment } from './CompanyPublicSpecs';
import type { PeriodDataFragment } from './PeriodData';
import type { StockBlockingPeriodDataFragment } from './StockBlockingPeriod';
import type { ConfiguratorInventoryPublicSpecsFragment } from './ConfiguratorInventoryPublicSpecs';
import type { MobilityInventoryPublicSpecsFragment } from './MobilityInventoryPublicSpecs';
import type { MobilityModuleSpecsFragment } from './MobilityModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerBookingCodeSpecsFragment } from './DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from './MobilitySigningSettingSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { KycPresetsOptionsDataFragment } from './KYCPresetsOptionsData';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from './MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from './MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from './MobilityEmailContentData';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { MobilityOperatorEmailContentDataFragment } from './MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from './MobilityHomeDeliveryData';
import type { CounterSettingsSpecsFragment } from './CounterSettingsSpecs';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { InventoryDetailsPublicDataFragmentDoc } from './InventoryDetailsPublicData';
import { StockInventorySpecsFragmentDoc } from './StockInventorySpecs';
import { ApplicationStageDataFragmentDoc } from './ApplicationStageData';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from './ReferenceApplicationData';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { TradeInVehicleDataFragmentDoc } from './TradeInVehicleData';
import { CompanyPublicSpecsFragmentDoc } from './CompanyPublicSpecs';
import { PeriodDataFragmentDoc } from './PeriodData';
import { StockBlockingPeriodDataFragmentDoc } from './StockBlockingPeriod';
import { ConfiguratorInventoryPublicSpecsFragmentDoc } from './ConfiguratorInventoryPublicSpecs';
import { MobilityInventoryPublicSpecsFragmentDoc } from './MobilityInventoryPublicSpecs';
import { MobilityModuleSpecsFragmentDoc } from './MobilityModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerBookingCodeSpecsFragmentDoc } from './DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from './MobilitySigningSettingSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { KycPresetsOptionsDataFragmentDoc } from './KYCPresetsOptionsData';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from './MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from './MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from './MobilityEmailContentData';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { MobilityOperatorEmailContentDataFragmentDoc } from './MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from './MobilityHomeDeliveryData';
import { CounterSettingsSpecsFragmentDoc } from './CounterSettingsSpecs';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
export type ModelConfiguratorListDataFragment = (
  { __typename: 'ModelConfigurator' }
  & Pick<SchemaTypes.ModelConfigurator, 'id' | 'displayName' | 'externalUrl' | 'isActive' | 'urlIdentifier' | 'variantConfiguratorIds'>
  & { model: (
    { __typename: 'FinderVehicle' }
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ), module: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName' | 'isInventoryEnabled' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName'>
    ) }
  ) | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, vehicleModule: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
  ), variantConfigurators: Array<(
    { __typename: 'VariantConfigurator' }
    & Pick<SchemaTypes.VariantConfigurator, 'id' | 'displayName' | 'vehiclePrice'>
    & { variant: (
      { __typename: 'FinderVehicle' }
      & Pick<SchemaTypes.FinderVehicle, 'id'>
    ) | (
      { __typename: 'LocalMake' }
      & Pick<SchemaTypes.LocalMake, 'id'>
    ) | (
      { __typename: 'LocalModel' }
      & Pick<SchemaTypes.LocalModel, 'id'>
    ) | (
      { __typename: 'LocalVariant' }
      & Pick<SchemaTypes.LocalVariant, 'order' | 'vehiclePrice' | 'id'>
      & { images?: SchemaTypes.Maybe<Array<SchemaTypes.Maybe<(
        { __typename: 'UploadedFileWithPreview' }
        & UploadFileWithPreviewFormDataFragment
      )>>>, versioning: (
        { __typename: 'AdvancedVersioning' }
        & AdvancedVersioningDataFragment
      ) }
    ), inventories: Array<(
      { __typename: 'ConfiguratorInventory' }
      & InventoryDetailsPublicData_ConfiguratorInventory_Fragment
    ) | (
      { __typename: 'MobilityInventory' }
      & InventoryDetailsPublicData_MobilityInventory_Fragment
    )> }
  )>, banner?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFile' }
    & UploadFileFormDataFragment
  )> }
);

export const ModelConfiguratorListDataFragmentDoc = /*#__PURE__*/ gql`
    fragment ModelConfiguratorListData on ModelConfigurator {
  id
  displayName
  externalUrl
  isActive
  model {
    name {
      ...TranslatedStringData
    }
  }
  module {
    ... on ConfiguratorModule {
      id
      displayName
      isInventoryEnabled
      companyId
      company {
        id
        displayName
      }
    }
  }
  vehicleModule {
    id
    displayName
  }
  urlIdentifier
  variantConfiguratorIds
  variantConfigurators {
    id
    displayName
    variant {
      id
      ... on LocalVariant {
        order
        images {
          ...UploadFileWithPreviewFormData
        }
        vehiclePrice
        versioning {
          ...AdvancedVersioningData
        }
      }
    }
    inventories {
      ...InventoryDetailsPublicData
    }
    vehiclePrice
  }
  banner {
    ...UploadFileFormData
  }
}
    `;