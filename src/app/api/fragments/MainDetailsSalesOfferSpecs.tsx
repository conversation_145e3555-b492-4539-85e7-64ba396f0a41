import type * as SchemaTypes from '../types';

import type { SalesOfferDocumentDataFragment } from './SalesOfferDocumentData';
import { gql } from '@apollo/client';
import { SalesOfferDocumentDataFragmentDoc } from './SalesOfferDocumentData';
export type MainDetailsSalesOfferSpecsFragment = (
  { __typename: 'MainDetailsSalesOffer' }
  & Pick<SchemaTypes.MainDetailsSalesOffer, 'lastUpdatedAt' | 'kind' | 'status' | 'isEnabled' | 'optionsSubsidy' | 'estimatedDeliveryDate' | 'coeAmount' | 'numberConsecutiveBids' | 'remarks' | 'coeCategory' | 'tradeInPreOwned'>
  & { documents: Array<(
    { __typename: 'SalesOfferDocument' }
    & SalesOfferDocumentDataFragment
  )> }
);

export const MainDetailsSalesOfferSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment MainDetailsSalesOfferSpecs on MainDetailsSalesOffer {
  lastUpdatedAt
  kind
  status
  isEnabled
  optionsSubsidy
  estimatedDeliveryDate
  coeAmount
  numberConsecutiveBids
  remarks
  coeCategory
  documents {
    ...SalesOfferDocumentData
  }
  tradeInPreOwned
}
    `;