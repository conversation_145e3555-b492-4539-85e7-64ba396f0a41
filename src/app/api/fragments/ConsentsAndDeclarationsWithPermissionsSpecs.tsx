import type * as SchemaTypes from '../types';

import type { ConsentsAndDeclarationsSpecs_CheckboxConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsSpecs_GroupConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsSpecs_MarketingConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsSpecs_TextConsentsAndDeclarations_Fragment } from './ConsentsAndDeclarationsSpecs';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './BaseConditionSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { MarketingPlatformSpecsFragment } from './MarketingPlatformSpecs';
import { gql } from '@apollo/client';
import { ConsentsAndDeclarationsSpecsFragmentDoc } from './ConsentsAndDeclarationsSpecs';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { ConditionSpecsFragmentDoc } from './ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from './BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { MarketingPlatformSpecsFragmentDoc } from './MarketingPlatformSpecs';
export type ConsentsAndDeclarationsWithPermissionsSpecs_CheckboxConsentsAndDeclarations_Fragment = (
  { __typename: 'CheckboxConsentsAndDeclarations' }
  & Pick<SchemaTypes.CheckboxConsentsAndDeclarations, 'permissions'>
  & ConsentsAndDeclarationsSpecs_CheckboxConsentsAndDeclarations_Fragment
);

export type ConsentsAndDeclarationsWithPermissionsSpecs_GroupConsentsAndDeclarations_Fragment = (
  { __typename: 'GroupConsentsAndDeclarations' }
  & Pick<SchemaTypes.GroupConsentsAndDeclarations, 'permissions'>
  & ConsentsAndDeclarationsSpecs_GroupConsentsAndDeclarations_Fragment
);

export type ConsentsAndDeclarationsWithPermissionsSpecs_MarketingConsentsAndDeclarations_Fragment = (
  { __typename: 'MarketingConsentsAndDeclarations' }
  & Pick<SchemaTypes.MarketingConsentsAndDeclarations, 'permissions'>
  & ConsentsAndDeclarationsSpecs_MarketingConsentsAndDeclarations_Fragment
);

export type ConsentsAndDeclarationsWithPermissionsSpecs_TextConsentsAndDeclarations_Fragment = (
  { __typename: 'TextConsentsAndDeclarations' }
  & Pick<SchemaTypes.TextConsentsAndDeclarations, 'permissions'>
  & ConsentsAndDeclarationsSpecs_TextConsentsAndDeclarations_Fragment
);

export type ConsentsAndDeclarationsWithPermissionsSpecsFragment = ConsentsAndDeclarationsWithPermissionsSpecs_CheckboxConsentsAndDeclarations_Fragment | ConsentsAndDeclarationsWithPermissionsSpecs_GroupConsentsAndDeclarations_Fragment | ConsentsAndDeclarationsWithPermissionsSpecs_MarketingConsentsAndDeclarations_Fragment | ConsentsAndDeclarationsWithPermissionsSpecs_TextConsentsAndDeclarations_Fragment;

export const ConsentsAndDeclarationsWithPermissionsSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment ConsentsAndDeclarationsWithPermissionsSpecs on ConsentsAndDeclarations {
  ...ConsentsAndDeclarationsSpecs
  permissions
}
    `;