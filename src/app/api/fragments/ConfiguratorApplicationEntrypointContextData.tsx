import type * as SchemaTypes from '../types';

import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { MyInfoSettingSpecFragment } from './MyInfoSettingSpec';
import type { DepositAmountDataFragment } from './DepositAmountData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { AppointmentModuleSpecsFragment } from './AppointmentModuleSpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { NamirialSigningModuleSpecsFragment } from './NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from './NamirialSettingsSpec';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { InsurerEntrypointContextDataFragment } from './InsurerEntrypointContextData';
import type { VisitAppointmentModuleSpecsFragment } from './VisitAppointmentModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from './VisitAppointmentModuleEmailContentsSpecs';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import { gql } from '@apollo/client';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { MyInfoSettingSpecFragmentDoc } from './MyInfoSettingSpec';
import { DepositAmountDataFragmentDoc } from './DepositAmountData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { AppointmentModuleSpecsFragmentDoc } from './AppointmentModuleSpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { NamirialSigningModuleSpecsFragmentDoc } from './NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from './NamirialSettingsSpec';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { InsurerEntrypointContextDataFragmentDoc } from './InsurerEntrypointContextData';
import { VisitAppointmentModuleSpecsFragmentDoc } from './VisitAppointmentModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from './VisitAppointmentModuleEmailContentsSpecs';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
export type ConfiguratorApplicationEntrypointContextDataFragment = (
  { __typename: 'ConfiguratorApplicationEntrypoint' }
  & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname' | 'displayName'>
  & { configuratorApplicationModule: (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'vehicleModuleId' | 'bankModuleId' | 'insuranceModuleId' | 'customerModuleId' | 'agreementsModuleId' | 'scenarios' | 'tradeIn' | 'isTradeInAmountVisible' | 'testDrive' | 'financingPreference' | 'isInsuranceOptional' | 'showFinanceCalculator' | 'showInsuranceCalculator' | 'market' | 'myInfoSettingId' | 'isOcrEnabled' | 'showFromValueOnVehicleDetails' | 'bankDisplayPreference' | 'externalUrl' | 'displayAppointmentDatepicker' | 'insurerDisplayPreference' | 'insurerIds' | 'porscheIdModuleId' | 'isCustomerDataRetreivalByPorscheId' | 'isPorscheIdLoginMandatory'>
    & { marketType: (
      { __typename: 'DefaultApplicationMarket' }
      & ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment
      & ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment
    ) | (
      { __typename: 'NewZealandApplicationMarket' }
      & ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment
      & ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment
    ) | (
      { __typename: 'SingaporeApplicationMarket' }
      & ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment
      & ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment
    ), paymentSetting?: SchemaTypes.Maybe<(
      { __typename: 'DealershipMyInfoSetting' }
      & DealershipSettingSpecData_DealershipMyInfoSetting_Fragment
    ) | (
      { __typename: 'DealershipPaymentSetting' }
      & DealershipSettingSpecData_DealershipPaymentSetting_Fragment
    ) | (
      { __typename: 'DealershipPublicSalesPerson' }
      & DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment
    )>, myInfoSetting?: SchemaTypes.Maybe<(
      { __typename: 'MyInfoSetting' }
      & MyInfoSettingSpecFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ), depositAmount?: SchemaTypes.Maybe<(
      { __typename: 'DepositAmount' }
      & DepositAmountDataFragment
    )>, liveChatSetting?: SchemaTypes.Maybe<(
      { __typename: 'UserlikeChatbotSetting' }
      & Pick<SchemaTypes.UserlikeChatbotSetting, 'script' | 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatSetting' }
      & Pick<SchemaTypes.WhatsappLiveChatSetting, 'link' | 'id'>
    )>, promoCodeModule?: SchemaTypes.Maybe<(
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    )>, priceDisclaimer: (
      { __typename: 'DealerDisclaimersConfigurator' }
      & DealerDisclaimersConfiguratorDataFragment
    ), termsTitle: (
      { __typename: 'DealerTranslatedStringSetting' }
      & DealerTranslatedStringSettingDataFragment
    ), termsText: (
      { __typename: 'DealerTranslatedStringSetting' }
      & DealerTranslatedStringSettingDataFragment
    ), appointmentModule?: SchemaTypes.Maybe<{ __typename: 'AdyenPaymentModule' } | (
      { __typename: 'AppointmentModule' }
      & AppointmentModuleSpecsFragment
    ) | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }>, insurers: Array<(
      { __typename: 'Insurer' }
      & InsurerEntrypointContextDataFragment
    )>, visitAppointmentModule?: SchemaTypes.Maybe<{ __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | (
      { __typename: 'VisitAppointmentModule' }
      & VisitAppointmentModuleSpecsFragment
    ) | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }>, customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' } }
  ) }
);

export const ConfiguratorApplicationEntrypointContextDataFragmentDoc = /*#__PURE__*/ gql`
    fragment ConfiguratorApplicationEntrypointContextData on ConfiguratorApplicationEntrypoint {
  id
  pathname
  displayName
  configuratorApplicationModule {
    id
    vehicleModuleId
    bankModuleId
    insuranceModuleId
    customerModuleId
    agreementsModuleId
    scenarios
    tradeIn
    isTradeInAmountVisible
    testDrive
    financingPreference
    isInsuranceOptional
    showFinanceCalculator
    showInsuranceCalculator
    market
    marketType {
      ...ApplicationMarketTypeFragment
    }
    paymentSetting {
      ...DealershipSettingSpecData
    }
    myInfoSettingId
    myInfoSetting {
      ...MyInfoSettingSpec
    }
    company {
      id
      timeZone
      countryCode
    }
    isOcrEnabled
    tradeIn
    testDrive
    depositAmount {
      ...DepositAmountData
    }
    marketType {
      ...ApplicationMarketTypeFragment
    }
    liveChatSetting {
      id
      ... on WhatsappLiveChatSetting {
        link
      }
      ... on UserlikeChatbotSetting {
        script
      }
    }
    promoCodeModule {
      id
      displayName
    }
    priceDisclaimer {
      ...DealerDisclaimersConfiguratorData
    }
    termsTitle {
      ...DealerTranslatedStringSettingData
    }
    termsText {
      ...DealerTranslatedStringSettingData
    }
    showFromValueOnVehicleDetails
    bankDisplayPreference
    externalUrl
    appointmentModule {
      ...AppointmentModuleSpecs
    }
    displayAppointmentDatepicker
    insurerDisplayPreference
    insurerIds
    insurers {
      ...InsurerEntrypointContextData
    }
    porscheIdModuleId
    isCustomerDataRetreivalByPorscheId
    isPorscheIdLoginMandatory
    scenarios
    visitAppointmentModule {
      ...VisitAppointmentModuleSpecs
    }
    customerModule {
      ... on LocalCustomerManagementModule {
        extraSettings {
          ...KYCExtraSettingsSpecs
        }
      }
    }
  }
}
    `;