import type * as SchemaTypes from '../types';

import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from './FinanceProductListData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { PeriodDataFragment } from './PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import { gql } from '@apollo/client';
import { FinanceProductListDataFragmentDoc } from './FinanceProductListData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
export type DealerFinanceProductsSpecsFragment = (
  { __typename: 'DealerFinanceProducts' }
  & Pick<SchemaTypes.DealerFinanceProducts, 'dealerId' | 'financeProductSuiteIds'>
  & { financeProducts: Array<(
    { __typename: 'LocalDeferredPrincipal' }
    & FinanceProductListData_LocalDeferredPrincipal_Fragment
  ) | (
    { __typename: 'LocalHirePurchase' }
    & FinanceProductListData_LocalHirePurchase_Fragment
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloon' }
    & FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloonGFV' }
    & FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment
  ) | (
    { __typename: 'LocalLease' }
    & FinanceProductListData_LocalLease_Fragment
  ) | (
    { __typename: 'LocalLeasePurchase' }
    & FinanceProductListData_LocalLeasePurchase_Fragment
  ) | (
    { __typename: 'LocalUcclLeasing' }
    & FinanceProductListData_LocalUcclLeasing_Fragment
  )>, dealer: (
    { __typename: 'Dealer' }
    & Pick<SchemaTypes.Dealer, 'id'>
    & { legalName: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) }
);

export const DealerFinanceProductsSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment DealerFinanceProductsSpecs on DealerFinanceProducts {
  dealerId
  financeProductSuiteIds
  financeProducts {
    ...FinanceProductListData
  }
  dealer {
    id
    legalName {
      ...TranslatedStringData
    }
  }
}
    `;