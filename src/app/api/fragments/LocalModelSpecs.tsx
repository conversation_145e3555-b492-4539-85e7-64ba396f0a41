import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
export type LocalModelSpecsFragment = (
  { __typename: 'LocalModel' }
  & Pick<SchemaTypes.LocalModel, 'id' | 'identifier' | 'order' | 'isActive' | 'moduleId' | 'makeId' | 'parentModelId' | 'bodyType'>
  & { name: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ), parentModel?: SchemaTypes.Maybe<(
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'order'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ), make: (
      { __typename: 'LocalMake' }
      & LocalMakeSpecsFragment
    ) }
  )>, make: (
    { __typename: 'LocalMake' }
    & LocalMakeSpecsFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ), submodels: (
    { __typename: 'PaginatedLocalModels' }
    & Pick<SchemaTypes.PaginatedLocalModels, 'count'>
    & { items: Array<(
      { __typename: 'LocalModel' }
      & Pick<SchemaTypes.LocalModel, 'id'>
    )> }
  ) }
);

export const LocalModelSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment LocalModelSpecs on LocalModel {
  id
  identifier
  order
  isActive
  name {
    ...TranslatedStringData
  }
  moduleId
  module {
    companyId
    company {
      timeZone
    }
  }
  makeId
  parentModelId
  parentModel {
    order
    name {
      ...TranslatedStringData
    }
    make {
      ...LocalMakeSpecs
    }
  }
  make {
    ...LocalMakeSpecs
  }
  versioning {
    ...AdvancedVersioningData
  }
  submodels {
    count
    items {
      id
    }
  }
  bodyType
}
    `;