import type * as SchemaTypes from '../types';

import type { StandardApplicationModuleInDealerSpecsFragment } from './StandardApplicationModuleInDealerSpecs';
import type { DealerPriceDisclaimerDataFragment } from './DealerPriceDisclaimerData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { DealerVehiclesSpecsFragment } from './DealerVehiclesSpecs';
import type { DealerFinanceProductsSpecsFragment } from './DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from './FinanceProductListData';
import type { PeriodDataFragment } from './PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { DealerInsuranceProductsSpecsFragment } from './DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from './InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from './ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { StandardApplicationModuleEmailContentsSpecsFragment, StandardApplicationModuleEmailContentCustomerSpecsFragment, StandardApplicationModuleEmailContentShareSubmissionSpecsFragment, StandardApplicationModuleEmailContentSpecsFragment, StandardApplicationModuleEmailContentSalesPersonSpecsFragment } from './StandardApplicationModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerUploadedFileWithPreviewDataFragment } from './DealerUploadedFileWithPreview';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { EventApplicationModuleInDealerSpecsFragment } from './EventApplicationModuleInDealerSpecs';
import type { EventApplicationModuleEmailContentSpecsFragment } from './EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { ConfiguratorModuleInDealerSpecsFragment } from './ConfiguratorModuleInDealerSpecs';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { ConfiguratorModuleEmailContentSpecsFragment } from './ConfiguratorModuleEmailContentSpecs';
import type { MobilityModuleInDealerSpecsFragment } from './MobilityModuleInDealerSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from './MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from './MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from './MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from './MobilityOperatorEmailContentData';
import type { DealerBookingCodeSpecsFragment } from './DealerBookingCodeSpecs';
import type { MobilityHomeDeliveryDataFragment } from './MobilityHomeDeliveryData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { FinderApplicationPublicModuleInDealerSpecsFragment } from './FinderApplicationPublicModuleInDealerSpecs';
import type { FinderApplicationModuleEmailContentSpecsFragment } from './FinderApplicationModuleEmailContentSpecs';
import type { FinderApplicationPrivateModuleInDealerSpecsFragment } from './FinderApplicationPrivateModuleInDealerSpecs';
import type { AppointmentModuleInDealerSpecsFragment } from './AppointmentModuleInDealerSpecs';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { VisitAppointmentModuleInDealerSpecsFragment } from './VisitAppointmentModuleInDealerSpecs';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from './VisitAppointmentModuleEmailContentsSpecs';
import type { GiftVoucherModuleInDealerSpecsFragment } from './GiftVoucherModuleInDealerSpecs';
import type { GiftVoucherModuleEmailContentsSpecsFragment, GiftVoucherModuleEmailContentCustomerSpecsFragment, GiftVoucherModuleEmailDataFragment } from './GiftVoucherModuleEmailContentsSpecs';
import type { LaunchPadModuleInDealerSpecsFragment } from './LaunchPadModuleInDealerSpecs';
import type { SalesOfferModuleInDealerSpecsFragment } from './SalesOfferModuleInDealerSpecs';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from './SalesOfferModuleEmailContentsSpecs';
import type { SalesControlBoardModuleInDealerSpecsFragment } from './SalesControlBoardModuleInDealerSpecs';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from './DealerIntData';
import { gql } from '@apollo/client';
import { StandardApplicationModuleInDealerSpecsFragmentDoc } from './StandardApplicationModuleInDealerSpecs';
import { DealerPriceDisclaimerDataFragmentDoc } from './DealerPriceDisclaimerData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { DealerVehiclesSpecsFragmentDoc } from './DealerVehiclesSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from './DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from './FinanceProductListData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { DealerInsuranceProductsSpecsFragmentDoc } from './DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from './InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from './ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { StandardApplicationModuleEmailContentsSpecsFragmentDoc, StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc, StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc, StandardApplicationModuleEmailContentSpecsFragmentDoc, StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc } from './StandardApplicationModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from './DealerUploadedFileWithPreview';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { EventApplicationModuleInDealerSpecsFragmentDoc } from './EventApplicationModuleInDealerSpecs';
import { EventApplicationModuleEmailContentSpecsFragmentDoc } from './EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { ConfiguratorModuleInDealerSpecsFragmentDoc } from './ConfiguratorModuleInDealerSpecs';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from './ConfiguratorModuleEmailContentSpecs';
import { MobilityModuleInDealerSpecsFragmentDoc } from './MobilityModuleInDealerSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from './MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from './MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from './MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from './MobilityOperatorEmailContentData';
import { DealerBookingCodeSpecsFragmentDoc } from './DealerBookingCodeSpecs';
import { MobilityHomeDeliveryDataFragmentDoc } from './MobilityHomeDeliveryData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { FinderApplicationPublicModuleInDealerSpecsFragmentDoc } from './FinderApplicationPublicModuleInDealerSpecs';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from './FinderApplicationModuleEmailContentSpecs';
import { FinderApplicationPrivateModuleInDealerSpecsFragmentDoc } from './FinderApplicationPrivateModuleInDealerSpecs';
import { AppointmentModuleInDealerSpecsFragmentDoc } from './AppointmentModuleInDealerSpecs';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { VisitAppointmentModuleInDealerSpecsFragmentDoc } from './VisitAppointmentModuleInDealerSpecs';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from './VisitAppointmentModuleEmailContentsSpecs';
import { GiftVoucherModuleInDealerSpecsFragmentDoc } from './GiftVoucherModuleInDealerSpecs';
import { GiftVoucherModuleEmailContentsSpecsFragmentDoc, GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc, GiftVoucherModuleEmailDataFragmentDoc } from './GiftVoucherModuleEmailContentsSpecs';
import { LaunchPadModuleInDealerSpecsFragmentDoc } from './LaunchPadModuleInDealerSpecs';
import { SalesOfferModuleInDealerSpecsFragmentDoc } from './SalesOfferModuleInDealerSpecs';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from './SalesOfferModuleEmailContentsSpecs';
import { SalesControlBoardModuleInDealerSpecsFragmentDoc } from './SalesControlBoardModuleInDealerSpecs';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from './DealerIntData';
export type ModuleInDealerSpecs_AdyenPaymentModule_Fragment = { __typename: 'AdyenPaymentModule' };

export type ModuleInDealerSpecs_AppointmentModule_Fragment = (
  { __typename: 'AppointmentModule' }
  & AppointmentModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_AutoplayModule_Fragment = { __typename: 'AutoplayModule' };

export type ModuleInDealerSpecs_BankModule_Fragment = { __typename: 'BankModule' };

export type ModuleInDealerSpecs_BasicSigningModule_Fragment = { __typename: 'BasicSigningModule' };

export type ModuleInDealerSpecs_CapModule_Fragment = { __typename: 'CapModule' };

export type ModuleInDealerSpecs_ConfiguratorModule_Fragment = (
  { __typename: 'ConfiguratorModule' }
  & ConfiguratorModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_ConsentsAndDeclarationsModule_Fragment = { __typename: 'ConsentsAndDeclarationsModule' };

export type ModuleInDealerSpecs_CtsModule_Fragment = { __typename: 'CtsModule' };

export type ModuleInDealerSpecs_DocusignModule_Fragment = { __typename: 'DocusignModule' };

export type ModuleInDealerSpecs_EventApplicationModule_Fragment = (
  { __typename: 'EventApplicationModule' }
  & EventApplicationModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_FinderApplicationPrivateModule_Fragment = (
  { __typename: 'FinderApplicationPrivateModule' }
  & FinderApplicationPrivateModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_FinderApplicationPublicModule_Fragment = (
  { __typename: 'FinderApplicationPublicModule' }
  & FinderApplicationPublicModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_FinderVehicleManagementModule_Fragment = { __typename: 'FinderVehicleManagementModule' };

export type ModuleInDealerSpecs_FiservPaymentModule_Fragment = { __typename: 'FiservPaymentModule' };

export type ModuleInDealerSpecs_GiftVoucherModule_Fragment = (
  { __typename: 'GiftVoucherModule' }
  & GiftVoucherModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_InsuranceModule_Fragment = { __typename: 'InsuranceModule' };

export type ModuleInDealerSpecs_LabelsModule_Fragment = { __typename: 'LabelsModule' };

export type ModuleInDealerSpecs_LaunchPadModule_Fragment = (
  { __typename: 'LaunchPadModule' }
  & LaunchPadModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_LocalCustomerManagementModule_Fragment = { __typename: 'LocalCustomerManagementModule' };

export type ModuleInDealerSpecs_MaintenanceModule_Fragment = { __typename: 'MaintenanceModule' };

export type ModuleInDealerSpecs_MarketingModule_Fragment = { __typename: 'MarketingModule' };

export type ModuleInDealerSpecs_MobilityModule_Fragment = (
  { __typename: 'MobilityModule' }
  & MobilityModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_MyInfoModule_Fragment = { __typename: 'MyInfoModule' };

export type ModuleInDealerSpecs_NamirialSigningModule_Fragment = { __typename: 'NamirialSigningModule' };

export type ModuleInDealerSpecs_OidcModule_Fragment = { __typename: 'OIDCModule' };

export type ModuleInDealerSpecs_PayGatePaymentModule_Fragment = { __typename: 'PayGatePaymentModule' };

export type ModuleInDealerSpecs_PorscheIdModule_Fragment = { __typename: 'PorscheIdModule' };

export type ModuleInDealerSpecs_PorscheMasterDataModule_Fragment = { __typename: 'PorscheMasterDataModule' };

export type ModuleInDealerSpecs_PorschePaymentModule_Fragment = { __typename: 'PorschePaymentModule' };

export type ModuleInDealerSpecs_PorscheRetainModule_Fragment = { __typename: 'PorscheRetainModule' };

export type ModuleInDealerSpecs_PromoCodeModule_Fragment = { __typename: 'PromoCodeModule' };

export type ModuleInDealerSpecs_SalesControlBoardModule_Fragment = (
  { __typename: 'SalesControlBoardModule' }
  & SalesControlBoardModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_SalesOfferModule_Fragment = (
  { __typename: 'SalesOfferModule' }
  & SalesOfferModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_SimpleVehicleManagementModule_Fragment = { __typename: 'SimpleVehicleManagementModule' };

export type ModuleInDealerSpecs_StandardApplicationModule_Fragment = (
  { __typename: 'StandardApplicationModule' }
  & StandardApplicationModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_TradeInModule_Fragment = { __typename: 'TradeInModule' };

export type ModuleInDealerSpecs_TtbPaymentModule_Fragment = { __typename: 'TtbPaymentModule' };

export type ModuleInDealerSpecs_UserlikeChatbotModule_Fragment = { __typename: 'UserlikeChatbotModule' };

export type ModuleInDealerSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment = { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' };

export type ModuleInDealerSpecs_VisitAppointmentModule_Fragment = (
  { __typename: 'VisitAppointmentModule' }
  & VisitAppointmentModuleInDealerSpecsFragment
);

export type ModuleInDealerSpecs_WebsiteModule_Fragment = { __typename: 'WebsiteModule' };

export type ModuleInDealerSpecs_WhatsappLiveChatModule_Fragment = { __typename: 'WhatsappLiveChatModule' };

export type ModuleInDealerSpecsFragment = ModuleInDealerSpecs_AdyenPaymentModule_Fragment | ModuleInDealerSpecs_AppointmentModule_Fragment | ModuleInDealerSpecs_AutoplayModule_Fragment | ModuleInDealerSpecs_BankModule_Fragment | ModuleInDealerSpecs_BasicSigningModule_Fragment | ModuleInDealerSpecs_CapModule_Fragment | ModuleInDealerSpecs_ConfiguratorModule_Fragment | ModuleInDealerSpecs_ConsentsAndDeclarationsModule_Fragment | ModuleInDealerSpecs_CtsModule_Fragment | ModuleInDealerSpecs_DocusignModule_Fragment | ModuleInDealerSpecs_EventApplicationModule_Fragment | ModuleInDealerSpecs_FinderApplicationPrivateModule_Fragment | ModuleInDealerSpecs_FinderApplicationPublicModule_Fragment | ModuleInDealerSpecs_FinderVehicleManagementModule_Fragment | ModuleInDealerSpecs_FiservPaymentModule_Fragment | ModuleInDealerSpecs_GiftVoucherModule_Fragment | ModuleInDealerSpecs_InsuranceModule_Fragment | ModuleInDealerSpecs_LabelsModule_Fragment | ModuleInDealerSpecs_LaunchPadModule_Fragment | ModuleInDealerSpecs_LocalCustomerManagementModule_Fragment | ModuleInDealerSpecs_MaintenanceModule_Fragment | ModuleInDealerSpecs_MarketingModule_Fragment | ModuleInDealerSpecs_MobilityModule_Fragment | ModuleInDealerSpecs_MyInfoModule_Fragment | ModuleInDealerSpecs_NamirialSigningModule_Fragment | ModuleInDealerSpecs_OidcModule_Fragment | ModuleInDealerSpecs_PayGatePaymentModule_Fragment | ModuleInDealerSpecs_PorscheIdModule_Fragment | ModuleInDealerSpecs_PorscheMasterDataModule_Fragment | ModuleInDealerSpecs_PorschePaymentModule_Fragment | ModuleInDealerSpecs_PorscheRetainModule_Fragment | ModuleInDealerSpecs_PromoCodeModule_Fragment | ModuleInDealerSpecs_SalesControlBoardModule_Fragment | ModuleInDealerSpecs_SalesOfferModule_Fragment | ModuleInDealerSpecs_SimpleVehicleManagementModule_Fragment | ModuleInDealerSpecs_StandardApplicationModule_Fragment | ModuleInDealerSpecs_TradeInModule_Fragment | ModuleInDealerSpecs_TtbPaymentModule_Fragment | ModuleInDealerSpecs_UserlikeChatbotModule_Fragment | ModuleInDealerSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment | ModuleInDealerSpecs_VisitAppointmentModule_Fragment | ModuleInDealerSpecs_WebsiteModule_Fragment | ModuleInDealerSpecs_WhatsappLiveChatModule_Fragment;

export const ModuleInDealerSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment ModuleInDealerSpecs on Module {
  ... on StandardApplicationModule {
    ...StandardApplicationModuleInDealerSpecs
  }
  ... on EventApplicationModule {
    ...EventApplicationModuleInDealerSpecs
  }
  ... on ConfiguratorModule {
    ...ConfiguratorModuleInDealerSpecs
  }
  ... on MobilityModule {
    ...MobilityModuleInDealerSpecs
  }
  ... on FinderApplicationPublicModule {
    ...FinderApplicationPublicModuleInDealerSpecs
  }
  ... on FinderApplicationPrivateModule {
    ...FinderApplicationPrivateModuleInDealerSpecs
  }
  ... on AppointmentModule {
    ...AppointmentModuleInDealerSpecs
  }
  ... on VisitAppointmentModule {
    ...VisitAppointmentModuleInDealerSpecs
  }
  ... on GiftVoucherModule {
    ...GiftVoucherModuleInDealerSpecs
  }
  ... on LaunchPadModule {
    ...LaunchPadModuleInDealerSpecs
  }
  ... on SalesOfferModule {
    ...SalesOfferModuleInDealerSpecs
  }
  ... on SalesControlBoardModule {
    ...SalesControlBoardModuleInDealerSpecs
  }
}
    `;