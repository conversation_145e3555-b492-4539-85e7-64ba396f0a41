fragment LaunchpadLeadData on LaunchpadLead {
    vehicle {
        ...VehicleSpecs
    }

    retainInfo {
        vehicleDetails {
            modelType
            modelLine
            vin
        }
        financingDetails {
            maturityDate
            rv
            customerRate
            firstPayment
            firstPaymentDate
            remainingLoan
            financingStatus
            contractTerm
            termRealized
            termRemaining
            netAmountFinanced
            deposit
            purchasePrice
            latestImportDate
        }
    }

    contactedDate
    qualifiedDate

    mergedToLeadSuiteId
    mergedToLeadIdentifier
}
