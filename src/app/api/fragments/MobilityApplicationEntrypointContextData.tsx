import type * as SchemaTypes from '../types';

import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { DealerBookingCodeDataFragment } from './DealerBookingCodeData';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { DateUnitDataFragment } from './DateUnitData';
import { gql } from '@apollo/client';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { DealerBookingCodeDataFragmentDoc } from './DealerBookingCodeData';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { DateUnitDataFragmentDoc } from './DateUnitData';
export type MobilityApplicationEntrypointContextDataFragment = (
  { __typename: 'MobilityApplicationEntrypoint' }
  & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname' | 'displayName'>
  & { mobilityApplicationModule: (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName' | 'vehicleModuleId' | 'customerModuleId' | 'agreementsModuleId' | 'giftVoucherModuleId' | 'minimumAdvancedBooking' | 'durationBeforeNextBooking' | 'scenarios' | 'paymentSettingsId' | 'liveChatSettingId' | 'externalModelInfo' | 'baseUrl' | 'unavailableDayOfWeek' | 'persistKYCData' | 'promoCodeModuleId'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, liveChatSetting?: SchemaTypes.Maybe<(
      { __typename: 'UserlikeChatbotSetting' }
      & Pick<SchemaTypes.UserlikeChatbotSetting, 'script' | 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatSetting' }
      & Pick<SchemaTypes.WhatsappLiveChatSetting, 'link' | 'id'>
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'countryCode' | 'currency'>
    ), paymentSettings?: SchemaTypes.Maybe<(
      { __typename: 'AdyenPaymentSetting' }
      & Pick<SchemaTypes.AdyenPaymentSetting, 'id' | 'displayName' | 'paymentModuleId'>
    ) | (
      { __typename: 'FiservPaymentSetting' }
      & Pick<SchemaTypes.FiservPaymentSetting, 'id' | 'displayName' | 'paymentModuleId'>
    ) | (
      { __typename: 'PayGatePaymentSetting' }
      & Pick<SchemaTypes.PayGatePaymentSetting, 'id' | 'displayName' | 'paymentModuleId'>
    ) | (
      { __typename: 'PorschePaymentSetting' }
      & Pick<SchemaTypes.PorschePaymentSetting, 'id' | 'displayName' | 'paymentModuleId'>
    ) | (
      { __typename: 'TtbPaymentSetting' }
      & Pick<SchemaTypes.TtbPaymentSetting, 'id' | 'displayName' | 'paymentModuleId'>
    )>, rentalDisclaimer: (
      { __typename: 'DealerTranslatedStringSetting' }
      & DealerTranslatedStringSettingDataFragment
    ), rentalRequirement: (
      { __typename: 'DealerTranslatedStringSetting' }
      & DealerTranslatedStringSettingDataFragment
    ), bookingCode: (
      { __typename: 'DealerBookingCodeSetting' }
      & DealerBookingCodeDataFragment
    ), locations: Array<(
      { __typename: 'MobilityLocation' }
      & MobilityLocationDataFragment
    )>, unavailableTimeRange: Array<(
      { __typename: 'Period' }
      & Pick<SchemaTypes.Period, 'start' | 'end'>
    )>, availableNumberOfBookingRange: (
      { __typename: 'DateUnit' }
      & DateUnitDataFragment
    ), homeDelivery: (
      { __typename: 'MobilityHomeDelivery' }
      & Pick<SchemaTypes.MobilityHomeDelivery, 'id' | 'isEnable' | 'assigneeId'>
    ) }
  ) }
);

export const MobilityApplicationEntrypointContextDataFragmentDoc = /*#__PURE__*/ gql`
    fragment MobilityApplicationEntrypointContextData on MobilityApplicationEntrypoint {
  id
  pathname
  displayName
  mobilityApplicationModule {
    id
    displayName
    vehicleModuleId
    customerModuleId
    customerModule {
      ... on LocalCustomerManagementModule {
        extraSettings {
          ...KYCExtraSettingsSpecs
        }
      }
    }
    agreementsModuleId
    giftVoucherModuleId
    minimumAdvancedBooking
    durationBeforeNextBooking
    scenarios
    paymentSettingsId
    liveChatSettingId
    liveChatSetting {
      id
      ... on WhatsappLiveChatSetting {
        link
      }
      ... on UserlikeChatbotSetting {
        script
      }
    }
    company {
      id
      countryCode
      currency
    }
    paymentSettings {
      id
      displayName
      paymentModuleId
    }
    rentalDisclaimer {
      ...DealerTranslatedStringSettingData
    }
    rentalRequirement {
      ...DealerTranslatedStringSettingData
    }
    bookingCode {
      ...DealerBookingCodeData
    }
    locations {
      ...MobilityLocationData
    }
    externalModelInfo
    baseUrl
    unavailableTimeRange {
      start
      end
    }
    unavailableDayOfWeek
    availableNumberOfBookingRange {
      ...DateUnitData
    }
    persistKYCData
    promoCodeModuleId
    homeDelivery {
      id
      isEnable
      assigneeId
    }
  }
}
    `;