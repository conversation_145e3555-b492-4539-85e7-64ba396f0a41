import type * as SchemaTypes from '../types';

import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { ApplicationAdyenDepositDataFragment } from './ApplicationAdyenDepositData';
import type { ApplicationPorscheDepositDataFragment } from './ApplicationPorscheDepositData';
import type { ApplicationFiservDepositDataFragment } from './ApplicationFiservDepositData';
import type { ApplicationPayGateDepositDataFragment } from './ApplicationPayGateDepositData';
import type { ApplicationTtbDepositDataFragment } from './ApplicationTtbDepositData';
import type { ApplicationEventCustomizedFieldDataFragment } from './ApplicationEventCustomizedFieldData';
import { gql } from '@apollo/client';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { ApplicationAdyenDepositDataFragmentDoc } from './ApplicationAdyenDepositData';
import { ApplicationPorscheDepositDataFragmentDoc } from './ApplicationPorscheDepositData';
import { ApplicationFiservDepositDataFragmentDoc } from './ApplicationFiservDepositData';
import { ApplicationPayGateDepositDataFragmentDoc } from './ApplicationPayGateDepositData';
import { ApplicationTtbDepositDataFragmentDoc } from './ApplicationTtbDepositData';
import { ApplicationEventCustomizedFieldDataFragmentDoc } from './ApplicationEventCustomizedFieldData';
export type CustomerDetailsEventApplicationFragment = (
  { __typename: 'EventApplication' }
  & Pick<SchemaTypes.EventApplication, 'id' | 'stages'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), dealer: (
    { __typename: 'Dealer' }
    & Pick<SchemaTypes.Dealer, 'displayName'>
  ), vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & VehicleSpecs_FinderVehicle_Fragment
  ) | (
    { __typename: 'LocalMake' }
    & VehicleSpecs_LocalMake_Fragment
  ) | (
    { __typename: 'LocalModel' }
    & VehicleSpecs_LocalModel_Fragment
  ) | (
    { __typename: 'LocalVariant' }
    & VehicleSpecs_LocalVariant_Fragment
  )>, deposit?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationAdyenDeposit' }
    & ApplicationAdyenDepositDataFragment
  ) | (
    { __typename: 'ApplicationFiservDeposit' }
    & ApplicationFiservDepositDataFragment
  ) | (
    { __typename: 'ApplicationPayGateDeposit' }
    & ApplicationPayGateDepositDataFragment
  ) | (
    { __typename: 'ApplicationPorscheDeposit' }
    & ApplicationPorscheDepositDataFragment
  ) | (
    { __typename: 'ApplicationTtbDeposit' }
    & ApplicationTtbDepositDataFragment
  )>, draftFlow: (
    { __typename: 'StandardApplicationDraftFlow' }
    & Pick<SchemaTypes.StandardApplicationDraftFlow, 'isReceived' | 'isDepositCompleted' | 'isSubmittedToBank' | 'isAppointmentCompleted' | 'isSubmittedToInsurer' | 'applicationSubmissionDate' | 'applicationDraftedDate'>
  ), customizedFields: Array<(
    { __typename: 'EventCustomizedField' }
    & ApplicationEventCustomizedFieldDataFragment
  )>, endpoint?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationListEndpoint' }
    & Pick<SchemaTypes.ApplicationListEndpoint, 'id'>
  ) | (
    { __typename: 'ConfiguratorApplicationEntrypoint' }
    & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id'>
  ) | (
    { __typename: 'CustomerListEndpoint' }
    & Pick<SchemaTypes.CustomerListEndpoint, 'id'>
  ) | (
    { __typename: 'DummyPrivatePageEndpoint' }
    & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id'>
  ) | (
    { __typename: 'DummyWelcomePageEndpoint' }
    & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id'>
  ) | (
    { __typename: 'EventApplicationEntrypoint' }
    & Pick<SchemaTypes.EventApplicationEntrypoint, 'id'>
  ) | (
    { __typename: 'FinderApplicationEntrypoint' }
    & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id'>
  ) | (
    { __typename: 'FinderApplicationPublicAccessEntrypoint' }
    & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id'>
  ) | (
    { __typename: 'LaunchPadApplicationEntrypoint' }
    & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id'>
  ) | (
    { __typename: 'LeadListEndpoint' }
    & Pick<SchemaTypes.LeadListEndpoint, 'id'>
  ) | (
    { __typename: 'MobilityApplicationEntrypoint' }
    & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id'>
  ) | (
    { __typename: 'StandardApplicationEntrypoint' }
    & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id'>
  ) | (
    { __typename: 'StandardApplicationPublicAccessEntrypoint' }
    & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id'>
  ) | (
    { __typename: 'WebPageEndpoint' }
    & Pick<SchemaTypes.WebPageEndpoint, 'id'>
  )>, event: (
    { __typename: 'Event' }
    & Pick<SchemaTypes.Event, 'id' | 'isDeleted' | 'urlSlug'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ), lead: (
    { __typename: 'ConfiguratorLead' }
    & Pick<SchemaTypes.ConfiguratorLead, 'id' | 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'EventLead' }
    & Pick<SchemaTypes.EventLead, 'id' | 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'FinderLead' }
    & Pick<SchemaTypes.FinderLead, 'id' | 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'LaunchpadLead' }
    & Pick<SchemaTypes.LaunchpadLead, 'id' | 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'MobilityLead' }
    & Pick<SchemaTypes.MobilityLead, 'id' | 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'StandardLead' }
    & Pick<SchemaTypes.StandardLead, 'id' | 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId'>
    )> }
  ) }
);

export const CustomerDetailsEventApplicationFragmentDoc = /*#__PURE__*/ gql`
    fragment CustomerDetailsEventApplication on EventApplication {
  id
  stages
  module {
    displayName
    company {
      displayName
      timeZone
      currency
      roundings {
        amount {
          decimals
        }
        percentage {
          decimals
        }
      }
    }
  }
  dealer {
    displayName
  }
  vehicle {
    ...VehicleSpecs
  }
  deposit {
    ... on ApplicationAdyenDeposit {
      ...ApplicationAdyenDepositData
    }
    ... on ApplicationPorscheDeposit {
      ...ApplicationPorscheDepositData
    }
    ... on ApplicationFiservDeposit {
      ...ApplicationFiservDepositData
    }
    ... on ApplicationPayGateDeposit {
      ...ApplicationPayGateDepositData
    }
    ... on ApplicationTtbDeposit {
      ...ApplicationTtbDepositData
    }
  }
  draftFlow {
    isReceived
    isDepositCompleted
    isSubmittedToBank
    isAppointmentCompleted
    isSubmittedToInsurer
    applicationSubmissionDate
    applicationDraftedDate
  }
  customizedFields {
    ...ApplicationEventCustomizedFieldData
  }
  endpoint {
    id
  }
  event {
    id
    isDeleted
    urlSlug
    name {
      ...TranslatedStringData
    }
  }
  lead {
    id
    identifier
    status
    capValues {
      businessPartnerId
    }
  }
}
    `;