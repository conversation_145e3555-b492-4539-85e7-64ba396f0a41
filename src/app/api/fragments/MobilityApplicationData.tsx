import type * as SchemaTypes from '../types';

import type { CustomerSpecs_CorporateCustomer_Fragment, CustomerSpecs_Guarantor_Fragment, CustomerSpecs_LocalCustomer_Fragment } from './CustomerSpecs';
import type { LocalCustomerDataFragment } from './LocalCustomerData';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from './LocalCustomerFieldData';
import type { CorporateCustomerDataFragment } from './CorporateCustomerData';
import type { GuarantorDataFragment } from './GuarantorData';
import type { DraftFlowConfigurationSpecFragment } from './DraftFlowConfigurationSpec';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { MobilityApplicationModuleDataFragment } from './MobilityApplicationModuleData';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerBookingCodeDataFragment } from './DealerBookingCodeData';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { KycPresetsOptionsDataFragment } from './KYCPresetsOptionsData';
import type { MobilityCustomerEmailContentDataFragment } from './MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from './MobilityEmailContentData';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { MobilityOperatorEmailContentDataFragment } from './MobilityOperatorEmailContentData';
import type { DealerApplicationFragmentFragment } from './DealerApplicationFragment';
import type { DealerContactFragmentFragment } from './DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from './DealerSocialMediaFragment';
import type { DealerDisclaimersFragmentFragment } from './DealerDisclaimersFragment';
import type { DealerIntegrationDetailsFragmentFragment } from './DealerIntegrationDetailsFragment';
import type { UsersOptionsDataFragment } from './UsersOptionsData';
import type { ApplicationDocumentDataFragment } from './ApplicationDocumentData';
import type { KycFieldSpecsFragment } from './KYCFieldSpecs';
import type { EndpointContextData_ApplicationListEndpoint_Fragment, EndpointContextData_ConfiguratorApplicationEntrypoint_Fragment, EndpointContextData_CustomerListEndpoint_Fragment, EndpointContextData_DummyPrivatePageEndpoint_Fragment, EndpointContextData_DummyWelcomePageEndpoint_Fragment, EndpointContextData_EventApplicationEntrypoint_Fragment, EndpointContextData_FinderApplicationEntrypoint_Fragment, EndpointContextData_FinderApplicationPublicAccessEntrypoint_Fragment, EndpointContextData_LaunchPadApplicationEntrypoint_Fragment, EndpointContextData_LeadListEndpoint_Fragment, EndpointContextData_MobilityApplicationEntrypoint_Fragment, EndpointContextData_StandardApplicationEntrypoint_Fragment, EndpointContextData_StandardApplicationPublicAccessEntrypoint_Fragment, EndpointContextData_WebPageEndpoint_Fragment } from './EndpointContextData';
import type { DummyPrivatePageEndpointContextDataFragment } from './DummyPrivatePageEndpointContextData';
import type { StandardApplicationEntrypointContextDataFragment } from './StandardApplicationEntrypointContextData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { AppointmentModuleSpecsFragment } from './AppointmentModuleSpecs';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { NamirialSigningModuleSpecsFragment } from './NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from './NamirialSettingsSpec';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { VisitAppointmentModuleSpecsFragment } from './VisitAppointmentModuleSpecs';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from './VisitAppointmentModuleEmailContentsSpecs';
import type { InsurerEntrypointContextDataFragment } from './InsurerEntrypointContextData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { FlexibleDiscountDataFragment } from './FlexibleDiscountData';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { ApplicationListEndpointContextDataFragment } from './ApplicationListEndpointContextData';
import type { LeadListEndpointContextDataFragment } from './LeadListEndpointContextData';
import type { EventApplicationEntrypointContextDataFragment } from './EventApplicationEntrypointContextData';
import type { LaunchPadApplicationEntrypointContextDataFragment } from './LaunchPadApplicationEntrypointContextData';
import type { LaunchPadModuleSpecsFragment } from './LaunchPadModuleSpecs';
import type { CounterSettingsSpecsFragment } from './CounterSettingsSpecs';
import type { ConfiguratorApplicationEntrypointContextDataFragment } from './ConfiguratorApplicationEntrypointContextData';
import type { MyInfoSettingSpecFragment } from './MyInfoSettingSpec';
import type { DepositAmountDataFragment } from './DepositAmountData';
import type { CustomerListEndpointContextDataFragment } from './CustomerListEndpointContextData';
import type { MobilityApplicationEntrypointContextDataFragment } from './MobilityApplicationEntrypointContextData';
import type { DateUnitDataFragment } from './DateUnitData';
import type { WebpageEndpointContextDataFragment } from './WebpageEndpointContextData';
import type { WebPageEndpointSpecsFragment } from './WebPageEndpointSpecs';
import type { WebPagePathDataFragment } from './WebPagePathData';
import type { StandardApplicationPublicAccessEntrypointContextDataFragment } from './StandardApplicationPublicAccessEntrypointContextData';
import type { FinderApplicationPublicAccessEntrypointContextDataFragment } from './FinderApplicationPublicAccessEntrypointContextData';
import type { EntrypointFinderApplicationPublicModuleFragment } from './EntrypointFinderApplicationPublicModule';
import type { FinderApplicationModuleEmailContentSpecsFragment } from './FinderApplicationModuleEmailContentSpecs';
import type { ModuleDisclaimersDataFragment } from './ModuleDisclaimersData';
import type { FinderApplicationEntrypointContextDataFragment } from './FinderApplicationEntrypointContextData';
import type { EntrypointFinderApplicationPrivateModuleFragment } from './EntrypointFinderApplicationPrivateModule';
import type { MobilitySnapshotData_MobilityAdditionalInfoSnapshot_Fragment, MobilitySnapshotData_MobilityAddonSnapshot_Fragment } from './MobilitySnapshotData';
import type { MobilityBookingDetailsDataFragment } from './MobilityBookingDetailsData';
import type { MobilityBookingLocationHomeDataFragment } from './MobilityBookingLocationHomeData';
import type { MobilityBookingLocationPickupDataFragment } from './MobilityBookingLocationPickupData';
import type { PeriodDataFragment } from './PeriodData';
import type { ApplicationAgreementData_CheckboxApplicationAgreement_Fragment, ApplicationAgreementData_MarketingApplicationAgreement_Fragment, ApplicationAgreementData_TextApplicationAgreement_Fragment } from './ApplicationAgreementData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './BaseConditionSpecs';
import type { MarketingPlatformSpecsFragment } from './MarketingPlatformSpecs';
import type { MarketingPlatformsAgreedSpecsFragment } from './MarketingPlatformsAgreedSpecs';
import type { ApplicationAdyenDepositDataFragment } from './ApplicationAdyenDepositData';
import type { ApplicationPorscheDepositDataFragment } from './ApplicationPorscheDepositData';
import type { ApplicationFiservDepositDataFragment } from './ApplicationFiservDepositData';
import type { ApplicationPayGateDepositDataFragment } from './ApplicationPayGateDepositData';
import type { ApplicationTtbDepositDataFragment } from './ApplicationTtbDepositData';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from './ApplicationStageData';
import type { PromoCodeDataFragment } from './PromoCodeData';
import type { DealerFragmentFragment } from './DealerFragment';
import type { CompanyContextDataFragment } from './CompanyContextData';
import type { LanguagePackContextDataFragment } from './LanguagePackContextData';
import type { AvailableModulesDataFragment } from './AvailableModulesData';
import type { CompanyDealerDataFragment } from './CompanyDealerData';
import type { MaintenanceUpdateFragment } from './MaintenanceUpdate';
import type { UserAvatarSpecsFragment } from './UserAvatarSpecs';
import type { EdmEmailFooterPublicDataFragment } from './EdmEmailFooterPublicData';
import type { EdmSocialMediaDataFragment } from './EdmSocialMediaData';
import type { ModuleInDealerSpecs_AdyenPaymentModule_Fragment, ModuleInDealerSpecs_AppointmentModule_Fragment, ModuleInDealerSpecs_AutoplayModule_Fragment, ModuleInDealerSpecs_BankModule_Fragment, ModuleInDealerSpecs_BasicSigningModule_Fragment, ModuleInDealerSpecs_CapModule_Fragment, ModuleInDealerSpecs_ConfiguratorModule_Fragment, ModuleInDealerSpecs_ConsentsAndDeclarationsModule_Fragment, ModuleInDealerSpecs_CtsModule_Fragment, ModuleInDealerSpecs_DocusignModule_Fragment, ModuleInDealerSpecs_EventApplicationModule_Fragment, ModuleInDealerSpecs_FinderApplicationPrivateModule_Fragment, ModuleInDealerSpecs_FinderApplicationPublicModule_Fragment, ModuleInDealerSpecs_FinderVehicleManagementModule_Fragment, ModuleInDealerSpecs_FiservPaymentModule_Fragment, ModuleInDealerSpecs_GiftVoucherModule_Fragment, ModuleInDealerSpecs_InsuranceModule_Fragment, ModuleInDealerSpecs_LabelsModule_Fragment, ModuleInDealerSpecs_LaunchPadModule_Fragment, ModuleInDealerSpecs_LocalCustomerManagementModule_Fragment, ModuleInDealerSpecs_MaintenanceModule_Fragment, ModuleInDealerSpecs_MarketingModule_Fragment, ModuleInDealerSpecs_MobilityModule_Fragment, ModuleInDealerSpecs_MyInfoModule_Fragment, ModuleInDealerSpecs_NamirialSigningModule_Fragment, ModuleInDealerSpecs_OidcModule_Fragment, ModuleInDealerSpecs_PayGatePaymentModule_Fragment, ModuleInDealerSpecs_PorscheIdModule_Fragment, ModuleInDealerSpecs_PorscheMasterDataModule_Fragment, ModuleInDealerSpecs_PorschePaymentModule_Fragment, ModuleInDealerSpecs_PorscheRetainModule_Fragment, ModuleInDealerSpecs_PromoCodeModule_Fragment, ModuleInDealerSpecs_SalesControlBoardModule_Fragment, ModuleInDealerSpecs_SalesOfferModule_Fragment, ModuleInDealerSpecs_SimpleVehicleManagementModule_Fragment, ModuleInDealerSpecs_StandardApplicationModule_Fragment, ModuleInDealerSpecs_TradeInModule_Fragment, ModuleInDealerSpecs_TtbPaymentModule_Fragment, ModuleInDealerSpecs_UserlikeChatbotModule_Fragment, ModuleInDealerSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleInDealerSpecs_VisitAppointmentModule_Fragment, ModuleInDealerSpecs_WebsiteModule_Fragment, ModuleInDealerSpecs_WhatsappLiveChatModule_Fragment } from './ModuleInDealerSpecs';
import type { StandardApplicationModuleInDealerSpecsFragment } from './StandardApplicationModuleInDealerSpecs';
import type { DealerPriceDisclaimerDataFragment } from './DealerPriceDisclaimerData';
import type { DealerVehiclesSpecsFragment } from './DealerVehiclesSpecs';
import type { DealerFinanceProductsSpecsFragment } from './DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from './FinanceProductListData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { DealerInsuranceProductsSpecsFragment } from './DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from './InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from './ErgoLookupTableSettingDetails';
import type { StandardApplicationModuleEmailContentsSpecsFragment, StandardApplicationModuleEmailContentCustomerSpecsFragment, StandardApplicationModuleEmailContentShareSubmissionSpecsFragment, StandardApplicationModuleEmailContentSpecsFragment, StandardApplicationModuleEmailContentSalesPersonSpecsFragment } from './StandardApplicationModuleEmailContentsSpecs';
import type { DealerUploadedFileWithPreviewDataFragment } from './DealerUploadedFileWithPreview';
import type { EventApplicationModuleInDealerSpecsFragment } from './EventApplicationModuleInDealerSpecs';
import type { EventApplicationModuleEmailContentSpecsFragment } from './EventApplicationModuleEmailContentSpecs';
import type { ConfiguratorModuleInDealerSpecsFragment } from './ConfiguratorModuleInDealerSpecs';
import type { ConfiguratorModuleEmailContentSpecsFragment } from './ConfiguratorModuleEmailContentSpecs';
import type { MobilityModuleInDealerSpecsFragment } from './MobilityModuleInDealerSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from './MobilityModuleEmailScenarioContentSpecs';
import type { DealerBookingCodeSpecsFragment } from './DealerBookingCodeSpecs';
import type { MobilityHomeDeliveryDataFragment } from './MobilityHomeDeliveryData';
import type { FinderApplicationPublicModuleInDealerSpecsFragment } from './FinderApplicationPublicModuleInDealerSpecs';
import type { FinderApplicationPrivateModuleInDealerSpecsFragment } from './FinderApplicationPrivateModuleInDealerSpecs';
import type { AppointmentModuleInDealerSpecsFragment } from './AppointmentModuleInDealerSpecs';
import type { VisitAppointmentModuleInDealerSpecsFragment } from './VisitAppointmentModuleInDealerSpecs';
import type { GiftVoucherModuleInDealerSpecsFragment } from './GiftVoucherModuleInDealerSpecs';
import type { GiftVoucherModuleEmailContentsSpecsFragment, GiftVoucherModuleEmailContentCustomerSpecsFragment, GiftVoucherModuleEmailDataFragment } from './GiftVoucherModuleEmailContentsSpecs';
import type { LaunchPadModuleInDealerSpecsFragment } from './LaunchPadModuleInDealerSpecs';
import type { SalesOfferModuleInDealerSpecsFragment } from './SalesOfferModuleInDealerSpecs';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from './SalesOfferModuleEmailContentsSpecs';
import type { SalesControlBoardModuleInDealerSpecsFragment } from './SalesControlBoardModuleInDealerSpecs';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from './DealerIntData';
import type { GiftPromoTypeDataFragment } from './GiftPromoTypeData';
import type { DiscountPromoTypeDataFragment } from './DiscountPromoTypeData';
import type { GiftVoucherDataFragment } from './GiftVoucherData';
import type { ModuleSpecs_AdyenPaymentModule_Fragment, ModuleSpecs_AppointmentModule_Fragment, ModuleSpecs_AutoplayModule_Fragment, ModuleSpecs_BankModule_Fragment, ModuleSpecs_BasicSigningModule_Fragment, ModuleSpecs_CapModule_Fragment, ModuleSpecs_ConfiguratorModule_Fragment, ModuleSpecs_ConsentsAndDeclarationsModule_Fragment, ModuleSpecs_CtsModule_Fragment, ModuleSpecs_DocusignModule_Fragment, ModuleSpecs_EventApplicationModule_Fragment, ModuleSpecs_FinderApplicationPrivateModule_Fragment, ModuleSpecs_FinderApplicationPublicModule_Fragment, ModuleSpecs_FinderVehicleManagementModule_Fragment, ModuleSpecs_FiservPaymentModule_Fragment, ModuleSpecs_GiftVoucherModule_Fragment, ModuleSpecs_InsuranceModule_Fragment, ModuleSpecs_LabelsModule_Fragment, ModuleSpecs_LaunchPadModule_Fragment, ModuleSpecs_LocalCustomerManagementModule_Fragment, ModuleSpecs_MaintenanceModule_Fragment, ModuleSpecs_MarketingModule_Fragment, ModuleSpecs_MobilityModule_Fragment, ModuleSpecs_MyInfoModule_Fragment, ModuleSpecs_NamirialSigningModule_Fragment, ModuleSpecs_OidcModule_Fragment, ModuleSpecs_PayGatePaymentModule_Fragment, ModuleSpecs_PorscheIdModule_Fragment, ModuleSpecs_PorscheMasterDataModule_Fragment, ModuleSpecs_PorschePaymentModule_Fragment, ModuleSpecs_PorscheRetainModule_Fragment, ModuleSpecs_PromoCodeModule_Fragment, ModuleSpecs_SalesControlBoardModule_Fragment, ModuleSpecs_SalesOfferModule_Fragment, ModuleSpecs_SimpleVehicleManagementModule_Fragment, ModuleSpecs_StandardApplicationModule_Fragment, ModuleSpecs_TradeInModule_Fragment, ModuleSpecs_TtbPaymentModule_Fragment, ModuleSpecs_UserlikeChatbotModule_Fragment, ModuleSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleSpecs_VisitAppointmentModule_Fragment, ModuleSpecs_WebsiteModule_Fragment, ModuleSpecs_WhatsappLiveChatModule_Fragment } from './ModuleSpecs';
import type { ConsentsAndDeclarationsModuleSpecsFragment } from './ConsentsAndDeclarationsModuleSpecs';
import type { SimpleVehicleManagementModuleSpecsFragment } from './SimpleVehicleManagementModuleSpecs';
import type { LocalCustomerManagementModuleSpecsFragment } from './LocalCustomerManagementModuleSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from './LocalCustomerManagementModuleKycFieldSpecs';
import type { KycPresetsSpecFragment } from './KYCPresetsSpec';
import type { BankModuleSpecsFragment } from './BankModuleSpecs';
import type { BasicSigningModuleSpecsFragment } from './BasicSigningModuleSpecs';
import type { StandardApplicationModuleSpecsFragment } from './StandardApplicationModuleSpecs';
import type { EventApplicationModuleSpecsFragment } from './EventApplicationModuleSpecs';
import type { AdyenPaymentModuleSpecsFragment } from './AdyenPaymentModuleSpecs';
import type { AdyenPaymentSettingsSpecFragment } from './AdyenPaymentSettingsSpec';
import type { PorschePaymentModuleSpecsFragment } from './PorschePaymentModuleSpecs';
import type { PorschePaymentSettingsSpecFragment } from './PorschePaymentSettingsSpec';
import type { FiservPaymentModuleSpecsFragment } from './FiservPaymentModuleSpecs';
import type { FiservPaymentSettingsSpecFragment } from './FiservPaymentSettingsSpec';
import type { PayGatePaymentModuleSpecsFragment } from './PayGatePaymentModuleSpecs';
import type { PayGatePaymentSettingsSpecFragment } from './PayGatePaymentSettingsSpec';
import type { TtbPaymentModuleSpecsFragment } from './TtbPaymentModuleSpecs';
import type { TtbPaymentSettingsSpecFragment } from './TtbPaymentSettingsSpec';
import type { MyInfoModuleSpecsFragment } from './MyInfoModuleSpecs';
import type { ConfiguratorModuleSpecsFragment } from './ConfiguratorModuleSpecs';
import type { WhatsappLiveChatModuleSpecsFragment } from './WhatsappLiveChatModuleSpecs';
import type { WhatsappLiveChatSettingsSpecFragment } from './WhatsappLiveChatSettingsSpec';
import type { UserlikeChatbotModuleSpecsFragment } from './UserlikeChatbotModuleSpecs';
import type { UserlikeChatbotSettingsSpecFragment } from './UserlikeChatbotSettingsSpec';
import type { PromoCodeModuleSpecsFragment } from './PromoCodeModuleSpecs';
import type { MaintenanceModuleSpecsFragment } from './MaintenanceModuleSpecs';
import type { WebsiteModuleSpecsFragment } from './WebsiteModuleSpecs';
import type { MobilityModuleSpecsFragment } from './MobilityModuleSpecs';
import type { MobilitySigningSettingSpecsFragment } from './MobilitySigningSettingSpecs';
import type { LabelsModuleSpecsFragment } from './LabelsModuleSpecs';
import type { FinderVehicleManagementModuleSpecsFragment } from './FinderVehicleManagementModuleSpecs';
import type { FinderApplicationPublicModuleSpecsFragment } from './FinderApplicationPublicModuleSpecs';
import type { FinderApplicationPrivateModuleSpecsFragment } from './FinderApplicationPrivateModuleSpecs';
import type { AutoplayModuleSpecsFragment } from './AutoplayModuleSpecs';
import type { AutoplaySettingSpecsFragment } from './AutoplaySettingSpecs';
import type { CtsModuleSpecsFragment } from './CtsModuleSpecs';
import type { CtsModuleSettingDataFragment } from './CtsModuleSettingData';
import type { InsuranceModuleSpecsFragment } from './InsuranceModuleSpecs';
import type { PorscheMasterDataModuleSpecsFragment } from './PorscheMasterDataModuleSpecs';
import type { GiftVoucherModuleSpecsFragment } from './GiftVoucherModuleSpecs';
import type { TradeInModuleSpecsFragment } from './TradeInModuleSpecs';
import type { TradeInSettingSpecFragment } from './TradeInSetting';
import type { CapModuleSpecsFragment } from './CapModuleSpecs';
import type { CapSettingSpecFragment } from './CapSettingSpec';
import type { PorscheIdModuleSpecsFragment } from './PorscheIdModuleSpecs';
import type { PorscheIdSettingSpecFragment } from './PorscheIdSettingSpec';
import type { PorscheRetainModuleSpecsFragment } from './PorscheRetainModuleSpecs';
import type { DocusignModuleSpecsFragment } from './DocusignModuleSpecs';
import type { DocusignSettingDataFragment } from './DocusignSettingData';
import type { OidcModuleSpecsFragment } from './OIDCModuleSpecs';
import type { MarketingModuleSpecsFragment } from './MarketingModuleSpecs';
import type { SalesOfferModuleSpecsFragment } from './SalesOfferModuleSpecs';
import type { BankDetailsDataFragment } from './BankDetailsData';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from './BankIntegrationData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from './FinanceProductDetailsData';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import type { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import type { SalesControlBoardModuleSpecsFragment } from './SalesControlBoardModuleSpecs';
import type { MobilityModuleGiftVoucherDataFragment } from './MobilityModuleGiftVoucherData';
import type { GiftVoucherDraftFlowDataFragment } from './GiftVoucherDraftFlowData';
import type { LocalVariantPublicSpecsFragment } from './LocalVariantPublicSpecs';
import type { LocalModelPublicSpecsFragment } from './LocalModelPublicSpecs';
import type { LocalMakePublicSpecsFragment } from './LocalMakePublicSpecs';
import type { DealerJourneyDataFragment } from './DealerJourneyData';
import type { MobilityStockGiftVoucherDataFragment } from './MobilityStockGiftVoucherData';
import type { CompanyPublicSpecsFragment } from './CompanyPublicSpecs';
import { gql } from '@apollo/client';
import { CustomerSpecsFragmentDoc } from './CustomerSpecs';
import { LocalCustomerDataFragmentDoc } from './LocalCustomerData';
import { LocalCustomerFieldDataFragmentDoc } from './LocalCustomerFieldData';
import { CorporateCustomerDataFragmentDoc } from './CorporateCustomerData';
import { GuarantorDataFragmentDoc } from './GuarantorData';
import { DraftFlowConfigurationSpecFragmentDoc } from './DraftFlowConfigurationSpec';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { MobilityApplicationModuleDataFragmentDoc } from './MobilityApplicationModuleData';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerBookingCodeDataFragmentDoc } from './DealerBookingCodeData';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { KycPresetsOptionsDataFragmentDoc } from './KYCPresetsOptionsData';
import { MobilityCustomerEmailContentDataFragmentDoc } from './MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from './MobilityEmailContentData';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { MobilityOperatorEmailContentDataFragmentDoc } from './MobilityOperatorEmailContentData';
import { DealerApplicationFragmentFragmentDoc } from './DealerApplicationFragment';
import { DealerContactFragmentFragmentDoc } from './DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from './DealerSocialMediaFragment';
import { DealerDisclaimersFragmentFragmentDoc } from './DealerDisclaimersFragment';
import { DealerIntegrationDetailsFragmentFragmentDoc } from './DealerIntegrationDetailsFragment';
import { UsersOptionsDataFragmentDoc } from './UsersOptionsData';
import { ApplicationDocumentDataFragmentDoc } from './ApplicationDocumentData';
import { KycFieldSpecsFragmentDoc } from './KYCFieldSpecs';
import { EndpointContextDataFragmentDoc } from './EndpointContextData';
import { DummyPrivatePageEndpointContextDataFragmentDoc } from './DummyPrivatePageEndpointContextData';
import { StandardApplicationEntrypointContextDataFragmentDoc } from './StandardApplicationEntrypointContextData';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { AppointmentModuleSpecsFragmentDoc } from './AppointmentModuleSpecs';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { NamirialSigningModuleSpecsFragmentDoc } from './NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from './NamirialSettingsSpec';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { VisitAppointmentModuleSpecsFragmentDoc } from './VisitAppointmentModuleSpecs';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from './VisitAppointmentModuleEmailContentsSpecs';
import { InsurerEntrypointContextDataFragmentDoc } from './InsurerEntrypointContextData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { FlexibleDiscountDataFragmentDoc } from './FlexibleDiscountData';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { ApplicationListEndpointContextDataFragmentDoc } from './ApplicationListEndpointContextData';
import { LeadListEndpointContextDataFragmentDoc } from './LeadListEndpointContextData';
import { EventApplicationEntrypointContextDataFragmentDoc } from './EventApplicationEntrypointContextData';
import { LaunchPadApplicationEntrypointContextDataFragmentDoc } from './LaunchPadApplicationEntrypointContextData';
import { LaunchPadModuleSpecsFragmentDoc } from './LaunchPadModuleSpecs';
import { CounterSettingsSpecsFragmentDoc } from './CounterSettingsSpecs';
import { ConfiguratorApplicationEntrypointContextDataFragmentDoc } from './ConfiguratorApplicationEntrypointContextData';
import { MyInfoSettingSpecFragmentDoc } from './MyInfoSettingSpec';
import { DepositAmountDataFragmentDoc } from './DepositAmountData';
import { CustomerListEndpointContextDataFragmentDoc } from './CustomerListEndpointContextData';
import { MobilityApplicationEntrypointContextDataFragmentDoc } from './MobilityApplicationEntrypointContextData';
import { DateUnitDataFragmentDoc } from './DateUnitData';
import { WebpageEndpointContextDataFragmentDoc } from './WebpageEndpointContextData';
import { WebPageEndpointSpecsFragmentDoc } from './WebPageEndpointSpecs';
import { WebPagePathDataFragmentDoc } from './WebPagePathData';
import { StandardApplicationPublicAccessEntrypointContextDataFragmentDoc } from './StandardApplicationPublicAccessEntrypointContextData';
import { FinderApplicationPublicAccessEntrypointContextDataFragmentDoc } from './FinderApplicationPublicAccessEntrypointContextData';
import { EntrypointFinderApplicationPublicModuleFragmentDoc } from './EntrypointFinderApplicationPublicModule';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from './FinderApplicationModuleEmailContentSpecs';
import { ModuleDisclaimersDataFragmentDoc } from './ModuleDisclaimersData';
import { FinderApplicationEntrypointContextDataFragmentDoc } from './FinderApplicationEntrypointContextData';
import { EntrypointFinderApplicationPrivateModuleFragmentDoc } from './EntrypointFinderApplicationPrivateModule';
import { MobilitySnapshotDataFragmentDoc } from './MobilitySnapshotData';
import { MobilityBookingDetailsDataFragmentDoc } from './MobilityBookingDetailsData';
import { MobilityBookingLocationHomeDataFragmentDoc } from './MobilityBookingLocationHomeData';
import { MobilityBookingLocationPickupDataFragmentDoc } from './MobilityBookingLocationPickupData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { ApplicationAgreementDataFragmentDoc } from './ApplicationAgreementData';
import { ConditionSpecsFragmentDoc } from './ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from './BaseConditionSpecs';
import { MarketingPlatformSpecsFragmentDoc } from './MarketingPlatformSpecs';
import { MarketingPlatformsAgreedSpecsFragmentDoc } from './MarketingPlatformsAgreedSpecs';
import { ApplicationAdyenDepositDataFragmentDoc } from './ApplicationAdyenDepositData';
import { ApplicationPorscheDepositDataFragmentDoc } from './ApplicationPorscheDepositData';
import { ApplicationFiservDepositDataFragmentDoc } from './ApplicationFiservDepositData';
import { ApplicationPayGateDepositDataFragmentDoc } from './ApplicationPayGateDepositData';
import { ApplicationTtbDepositDataFragmentDoc } from './ApplicationTtbDepositData';
import { ApplicationStageDataFragmentDoc } from './ApplicationStageData';
import { PromoCodeDataFragmentDoc } from './PromoCodeData';
import { DealerFragmentFragmentDoc } from './DealerFragment';
import { CompanyContextDataFragmentDoc } from './CompanyContextData';
import { LanguagePackContextDataFragmentDoc } from './LanguagePackContextData';
import { AvailableModulesDataFragmentDoc } from './AvailableModulesData';
import { CompanyDealerDataFragmentDoc } from './CompanyDealerData';
import { MaintenanceUpdateFragmentDoc } from './MaintenanceUpdate';
import { UserAvatarSpecsFragmentDoc } from './UserAvatarSpecs';
import { EdmEmailFooterPublicDataFragmentDoc } from './EdmEmailFooterPublicData';
import { EdmSocialMediaDataFragmentDoc } from './EdmSocialMediaData';
import { ModuleInDealerSpecsFragmentDoc } from './ModuleInDealerSpecs';
import { StandardApplicationModuleInDealerSpecsFragmentDoc } from './StandardApplicationModuleInDealerSpecs';
import { DealerPriceDisclaimerDataFragmentDoc } from './DealerPriceDisclaimerData';
import { DealerVehiclesSpecsFragmentDoc } from './DealerVehiclesSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from './DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from './FinanceProductListData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { DealerInsuranceProductsSpecsFragmentDoc } from './DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from './InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from './ErgoLookupTableSettingDetails';
import { StandardApplicationModuleEmailContentsSpecsFragmentDoc, StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc, StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc, StandardApplicationModuleEmailContentSpecsFragmentDoc, StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc } from './StandardApplicationModuleEmailContentsSpecs';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from './DealerUploadedFileWithPreview';
import { EventApplicationModuleInDealerSpecsFragmentDoc } from './EventApplicationModuleInDealerSpecs';
import { EventApplicationModuleEmailContentSpecsFragmentDoc } from './EventApplicationModuleEmailContentSpecs';
import { ConfiguratorModuleInDealerSpecsFragmentDoc } from './ConfiguratorModuleInDealerSpecs';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from './ConfiguratorModuleEmailContentSpecs';
import { MobilityModuleInDealerSpecsFragmentDoc } from './MobilityModuleInDealerSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from './MobilityModuleEmailScenarioContentSpecs';
import { DealerBookingCodeSpecsFragmentDoc } from './DealerBookingCodeSpecs';
import { MobilityHomeDeliveryDataFragmentDoc } from './MobilityHomeDeliveryData';
import { FinderApplicationPublicModuleInDealerSpecsFragmentDoc } from './FinderApplicationPublicModuleInDealerSpecs';
import { FinderApplicationPrivateModuleInDealerSpecsFragmentDoc } from './FinderApplicationPrivateModuleInDealerSpecs';
import { AppointmentModuleInDealerSpecsFragmentDoc } from './AppointmentModuleInDealerSpecs';
import { VisitAppointmentModuleInDealerSpecsFragmentDoc } from './VisitAppointmentModuleInDealerSpecs';
import { GiftVoucherModuleInDealerSpecsFragmentDoc } from './GiftVoucherModuleInDealerSpecs';
import { GiftVoucherModuleEmailContentsSpecsFragmentDoc, GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc, GiftVoucherModuleEmailDataFragmentDoc } from './GiftVoucherModuleEmailContentsSpecs';
import { LaunchPadModuleInDealerSpecsFragmentDoc } from './LaunchPadModuleInDealerSpecs';
import { SalesOfferModuleInDealerSpecsFragmentDoc } from './SalesOfferModuleInDealerSpecs';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from './SalesOfferModuleEmailContentsSpecs';
import { SalesControlBoardModuleInDealerSpecsFragmentDoc } from './SalesControlBoardModuleInDealerSpecs';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from './DealerIntData';
import { GiftPromoTypeDataFragmentDoc } from './GiftPromoTypeData';
import { DiscountPromoTypeDataFragmentDoc } from './DiscountPromoTypeData';
import { GiftVoucherDataFragmentDoc } from './GiftVoucherData';
import { ModuleSpecsFragmentDoc } from './ModuleSpecs';
import { ConsentsAndDeclarationsModuleSpecsFragmentDoc } from './ConsentsAndDeclarationsModuleSpecs';
import { SimpleVehicleManagementModuleSpecsFragmentDoc } from './SimpleVehicleManagementModuleSpecs';
import { LocalCustomerManagementModuleSpecsFragmentDoc } from './LocalCustomerManagementModuleSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from './LocalCustomerManagementModuleKycFieldSpecs';
import { KycPresetsSpecFragmentDoc } from './KYCPresetsSpec';
import { BankModuleSpecsFragmentDoc } from './BankModuleSpecs';
import { BasicSigningModuleSpecsFragmentDoc } from './BasicSigningModuleSpecs';
import { StandardApplicationModuleSpecsFragmentDoc } from './StandardApplicationModuleSpecs';
import { EventApplicationModuleSpecsFragmentDoc } from './EventApplicationModuleSpecs';
import { AdyenPaymentModuleSpecsFragmentDoc } from './AdyenPaymentModuleSpecs';
import { AdyenPaymentSettingsSpecFragmentDoc } from './AdyenPaymentSettingsSpec';
import { PorschePaymentModuleSpecsFragmentDoc } from './PorschePaymentModuleSpecs';
import { PorschePaymentSettingsSpecFragmentDoc } from './PorschePaymentSettingsSpec';
import { FiservPaymentModuleSpecsFragmentDoc } from './FiservPaymentModuleSpecs';
import { FiservPaymentSettingsSpecFragmentDoc } from './FiservPaymentSettingsSpec';
import { PayGatePaymentModuleSpecsFragmentDoc } from './PayGatePaymentModuleSpecs';
import { PayGatePaymentSettingsSpecFragmentDoc } from './PayGatePaymentSettingsSpec';
import { TtbPaymentModuleSpecsFragmentDoc } from './TtbPaymentModuleSpecs';
import { TtbPaymentSettingsSpecFragmentDoc } from './TtbPaymentSettingsSpec';
import { MyInfoModuleSpecsFragmentDoc } from './MyInfoModuleSpecs';
import { ConfiguratorModuleSpecsFragmentDoc } from './ConfiguratorModuleSpecs';
import { WhatsappLiveChatModuleSpecsFragmentDoc } from './WhatsappLiveChatModuleSpecs';
import { WhatsappLiveChatSettingsSpecFragmentDoc } from './WhatsappLiveChatSettingsSpec';
import { UserlikeChatbotModuleSpecsFragmentDoc } from './UserlikeChatbotModuleSpecs';
import { UserlikeChatbotSettingsSpecFragmentDoc } from './UserlikeChatbotSettingsSpec';
import { PromoCodeModuleSpecsFragmentDoc } from './PromoCodeModuleSpecs';
import { MaintenanceModuleSpecsFragmentDoc } from './MaintenanceModuleSpecs';
import { WebsiteModuleSpecsFragmentDoc } from './WebsiteModuleSpecs';
import { MobilityModuleSpecsFragmentDoc } from './MobilityModuleSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from './MobilitySigningSettingSpecs';
import { LabelsModuleSpecsFragmentDoc } from './LabelsModuleSpecs';
import { FinderVehicleManagementModuleSpecsFragmentDoc } from './FinderVehicleManagementModuleSpecs';
import { FinderApplicationPublicModuleSpecsFragmentDoc } from './FinderApplicationPublicModuleSpecs';
import { FinderApplicationPrivateModuleSpecsFragmentDoc } from './FinderApplicationPrivateModuleSpecs';
import { AutoplayModuleSpecsFragmentDoc } from './AutoplayModuleSpecs';
import { AutoplaySettingSpecsFragmentDoc } from './AutoplaySettingSpecs';
import { CtsModuleSpecsFragmentDoc } from './CtsModuleSpecs';
import { CtsModuleSettingDataFragmentDoc } from './CtsModuleSettingData';
import { InsuranceModuleSpecsFragmentDoc } from './InsuranceModuleSpecs';
import { PorscheMasterDataModuleSpecsFragmentDoc } from './PorscheMasterDataModuleSpecs';
import { GiftVoucherModuleSpecsFragmentDoc } from './GiftVoucherModuleSpecs';
import { TradeInModuleSpecsFragmentDoc } from './TradeInModuleSpecs';
import { TradeInSettingSpecFragmentDoc } from './TradeInSetting';
import { CapModuleSpecsFragmentDoc } from './CapModuleSpecs';
import { CapSettingSpecFragmentDoc } from './CapSettingSpec';
import { PorscheIdModuleSpecsFragmentDoc } from './PorscheIdModuleSpecs';
import { PorscheIdSettingSpecFragmentDoc } from './PorscheIdSettingSpec';
import { PorscheRetainModuleSpecsFragmentDoc } from './PorscheRetainModuleSpecs';
import { DocusignModuleSpecsFragmentDoc } from './DocusignModuleSpecs';
import { DocusignSettingDataFragmentDoc } from './DocusignSettingData';
import { OidcModuleSpecsFragmentDoc } from './OIDCModuleSpecs';
import { MarketingModuleSpecsFragmentDoc } from './MarketingModuleSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from './SalesOfferModuleSpecs';
import { BankDetailsDataFragmentDoc } from './BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from './BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from './FinanceProductDetailsData';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
import { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import { SalesControlBoardModuleSpecsFragmentDoc } from './SalesControlBoardModuleSpecs';
import { MobilityModuleGiftVoucherDataFragmentDoc } from './MobilityModuleGiftVoucherData';
import { GiftVoucherDraftFlowDataFragmentDoc } from './GiftVoucherDraftFlowData';
import { LocalVariantPublicSpecsFragmentDoc } from './LocalVariantPublicSpecs';
import { LocalModelPublicSpecsFragmentDoc } from './LocalModelPublicSpecs';
import { LocalMakePublicSpecsFragmentDoc } from './LocalMakePublicSpecs';
import { DealerJourneyDataFragmentDoc } from './DealerJourneyData';
import { MobilityStockGiftVoucherDataFragmentDoc } from './MobilityStockGiftVoucherData';
import { CompanyPublicSpecsFragmentDoc } from './CompanyPublicSpecs';
export type MobilityApplicationDataFragment = (
  { __typename: 'MobilityApplication' }
  & Pick<SchemaTypes.MobilityApplication, 'scenarios' | 'vehicleId' | 'dealerId' | 'routerId' | 'vin' | 'promoCodeAmount'>
  & { applicant: (
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  ), guarantor?: SchemaTypes.Maybe<(
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  )>, draftFlow: (
    { __typename: 'StandardApplicationDraftFlow' }
    & DraftFlowConfigurationSpecFragment
  ), vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & VehicleSpecs_FinderVehicle_Fragment
  ) | (
    { __typename: 'LocalMake' }
    & VehicleSpecs_LocalMake_Fragment
  ) | (
    { __typename: 'LocalModel' }
    & VehicleSpecs_LocalModel_Fragment
  ) | (
    { __typename: 'LocalVariant' }
    & VehicleSpecs_LocalVariant_Fragment
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
    & MobilityApplicationModuleDataFragment
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ), dealer: (
    { __typename: 'Dealer' }
    & DealerApplicationFragmentFragment
  ), availableAssignees: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )>, documents: Array<(
    { __typename: 'ApplicationDocument' }
    & ApplicationDocumentDataFragment
  )>, applicantKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, guarantorKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, corporateKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, router?: SchemaTypes.Maybe<(
    { __typename: 'Router' }
    & Pick<SchemaTypes.Router, 'id' | 'pathname'>
  )>, endpoint?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationListEndpoint' }
    & EndpointContextData_ApplicationListEndpoint_Fragment
  ) | (
    { __typename: 'ConfiguratorApplicationEntrypoint' }
    & EndpointContextData_ConfiguratorApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'CustomerListEndpoint' }
    & EndpointContextData_CustomerListEndpoint_Fragment
  ) | (
    { __typename: 'DummyPrivatePageEndpoint' }
    & EndpointContextData_DummyPrivatePageEndpoint_Fragment
  ) | (
    { __typename: 'DummyWelcomePageEndpoint' }
    & EndpointContextData_DummyWelcomePageEndpoint_Fragment
  ) | (
    { __typename: 'EventApplicationEntrypoint' }
    & EndpointContextData_EventApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'FinderApplicationEntrypoint' }
    & EndpointContextData_FinderApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicAccessEntrypoint' }
    & EndpointContextData_FinderApplicationPublicAccessEntrypoint_Fragment
  ) | (
    { __typename: 'LaunchPadApplicationEntrypoint' }
    & EndpointContextData_LaunchPadApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'LeadListEndpoint' }
    & EndpointContextData_LeadListEndpoint_Fragment
  ) | (
    { __typename: 'MobilityApplicationEntrypoint' }
    & EndpointContextData_MobilityApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'StandardApplicationEntrypoint' }
    & EndpointContextData_StandardApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'StandardApplicationPublicAccessEntrypoint' }
    & EndpointContextData_StandardApplicationPublicAccessEntrypoint_Fragment
  ) | (
    { __typename: 'WebPageEndpoint' }
    & EndpointContextData_WebPageEndpoint_Fragment
  )>, mobilitySnapshots: Array<(
    { __typename: 'MobilityAdditionalInfoSnapshot' }
    & MobilitySnapshotData_MobilityAdditionalInfoSnapshot_Fragment
  ) | (
    { __typename: 'MobilityAddonSnapshot' }
    & MobilitySnapshotData_MobilityAddonSnapshot_Fragment
  )>, mobilityBookingDetails: (
    { __typename: 'MobilityBookingDetails' }
    & MobilityBookingDetailsDataFragment
  ), applicantAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, guarantorAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, deposit?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationAdyenDeposit' }
    & ApplicationAdyenDepositDataFragment
  ) | (
    { __typename: 'ApplicationFiservDeposit' }
    & ApplicationFiservDepositDataFragment
  ) | (
    { __typename: 'ApplicationPayGateDeposit' }
    & ApplicationPayGateDepositDataFragment
  ) | (
    { __typename: 'ApplicationPorscheDeposit' }
    & ApplicationPorscheDepositDataFragment
  ) | (
    { __typename: 'ApplicationTtbDeposit' }
    & ApplicationTtbDepositDataFragment
  )>, amendments?: SchemaTypes.Maybe<Array<(
    { __typename: 'MobilityApplication' }
    & Pick<SchemaTypes.MobilityApplication, 'id'>
    & { versioning: (
      { __typename: 'AdvancedVersioning' }
      & AdvancedVersioningDataFragment
    ), mobilityBookingDetails: (
      { __typename: 'MobilityBookingDetails' }
      & MobilityBookingDetailsDataFragment
    ) }
    & ApplicationStageData_MobilityApplication_Fragment
  )>>, signing?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationNamirialSigning' }
    & Pick<SchemaTypes.ApplicationNamirialSigning, 'status' | 'envelopeId' | 'redirectionUrl'>
  ) | { __typename: 'ApplicationOTPSigning' }>, promoCode?: SchemaTypes.Maybe<(
    { __typename: 'PromoCode' }
    & PromoCodeDataFragment
  )>, giftVoucher?: SchemaTypes.Maybe<(
    { __typename: 'GiftVoucher' }
    & GiftVoucherDataFragment
  )> }
);

export const MobilityApplicationDataFragmentDoc = /*#__PURE__*/ gql`
    fragment MobilityApplicationData on MobilityApplication {
  scenarios
  applicant {
    ...CustomerSpecs
  }
  guarantor {
    ...CustomerSpecs
  }
  draftFlow {
    ...DraftFlowConfigurationSpec
  }
  vehicleId
  vehicle {
    ...VehicleSpecs
  }
  module {
    ...MobilityApplicationModuleData
    company {
      id
      timeZone
    }
  }
  dealerId
  dealer {
    ...DealerApplicationFragment
  }
  availableAssignees {
    ...UsersOptionsData
  }
  documents {
    ...ApplicationDocumentData
  }
  applicantKYC {
    ...KYCFieldSpecs
  }
  guarantorKYC {
    ...KYCFieldSpecs
  }
  corporateKYC {
    ...KYCFieldSpecs
  }
  routerId
  router {
    id
    pathname
  }
  endpoint {
    ...EndpointContextData
  }
  mobilitySnapshots {
    ...MobilitySnapshotData
  }
  mobilityBookingDetails {
    ...MobilityBookingDetailsData
  }
  applicantAgreements {
    ...ApplicationAgreementData
  }
  guarantorAgreements {
    ...ApplicationAgreementData
  }
  deposit {
    ... on ApplicationAdyenDeposit {
      ...ApplicationAdyenDepositData
    }
    ... on ApplicationPorscheDeposit {
      ...ApplicationPorscheDepositData
    }
    ... on ApplicationFiservDeposit {
      ...ApplicationFiservDepositData
    }
    ... on ApplicationPayGateDeposit {
      ...ApplicationPayGateDepositData
    }
    ... on ApplicationTtbDeposit {
      ...ApplicationTtbDepositData
    }
  }
  amendments {
    id
    ...ApplicationStageData
    versioning {
      ...AdvancedVersioningData
    }
    mobilityBookingDetails {
      ...MobilityBookingDetailsData
    }
  }
  vin
  signing {
    ... on ApplicationNamirialSigning {
      status
      envelopeId
      redirectionUrl
    }
  }
  promoCode {
    ...PromoCodeData
  }
  giftVoucher {
    ...GiftVoucherData
  }
  promoCodeAmount
}
    `;