import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { WebsiteModulePublicSpecsFragment } from './WebsiteModulePublicSpecs';
import type { CompanyWebpagePublicSpecFragment } from './CompanyWebpagePublicSpec';
import type { EdmEmailFooterPublicDataFragment } from './EdmEmailFooterPublicData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { EdmSocialMediaDataFragment } from './EdmSocialMediaData';
import type { WebPageBlockData_ColumnWebPageBlock_Fragment, WebPageBlockData_CustomWebPageBlock_Fragment, WebPageBlockData_ImageWebPageBlock_Fragment, WebPageBlockData_TextCarouselWebPageBlock_Fragment, WebPageBlockData_TextImageWebPageBlock_Fragment } from './WebPageBlockData';
import type { ColumnWebPageBlockDataFragment } from './ColumnWebPageBlockData';
import type { ColumnBlockDataFragment } from './ColumnBlockData';
import type { TextImageWebPageBlockDataFragment } from './TextImageWebPageBlockData';
import type { WebPageButtonDataFragment } from './WebPageButtonData';
import type { ImageWebPageBlockDataFragment } from './ImageWebPageBlockData';
import type { ImageDescriptionWebPageBlockDataFragment } from './ImageDescriptionWebPageBlockData';
import type { CustomWebPageBlockDataFragment } from './CustomWebPageBlockData';
import type { TextCarouselWebPageBlockDataFragment } from './TextCarouselWebPageBlockData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { WebsiteModulePublicSpecsFragmentDoc } from './WebsiteModulePublicSpecs';
import { CompanyWebpagePublicSpecFragmentDoc } from './CompanyWebpagePublicSpec';
import { EdmEmailFooterPublicDataFragmentDoc } from './EdmEmailFooterPublicData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { EdmSocialMediaDataFragmentDoc } from './EdmSocialMediaData';
import { WebPageBlockDataFragmentDoc } from './WebPageBlockData';
import { ColumnWebPageBlockDataFragmentDoc } from './ColumnWebPageBlockData';
import { ColumnBlockDataFragmentDoc } from './ColumnBlockData';
import { TextImageWebPageBlockDataFragmentDoc } from './TextImageWebPageBlockData';
import { WebPageButtonDataFragmentDoc } from './WebPageButtonData';
import { ImageWebPageBlockDataFragmentDoc } from './ImageWebPageBlockData';
import { ImageDescriptionWebPageBlockDataFragmentDoc } from './ImageDescriptionWebPageBlockData';
import { CustomWebPageBlockDataFragmentDoc } from './CustomWebPageBlockData';
import { TextCarouselWebPageBlockDataFragmentDoc } from './TextCarouselWebPageBlockData';
export type WebPageDataFragment = (
  { __typename: 'WebPage' }
  & Pick<SchemaTypes.WebPage, 'id' | 'moduleId' | 'metaTitle' | 'metaDescription' | 'urlSlug' | 'liveChatDisplay' | 'isActive'>
  & { bannerTitle?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, bannerSubTitle?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, pageTitle: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), module: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | (
    { __typename: 'WebsiteModule' }
    & WebsiteModulePublicSpecsFragment
  ) | { __typename: 'WhatsappLiveChatModule' }, bannerImage?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, mobileBannerImage?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, versioning: (
    { __typename: 'SimpleVersioning' }
    & Pick<SchemaTypes.SimpleVersioning, 'updatedAt' | 'createdAt'>
  ), webPageBlock: Array<(
    { __typename: 'ColumnWebPageBlock' }
    & WebPageBlockData_ColumnWebPageBlock_Fragment
  ) | (
    { __typename: 'CustomWebPageBlock' }
    & WebPageBlockData_CustomWebPageBlock_Fragment
  ) | (
    { __typename: 'ImageWebPageBlock' }
    & WebPageBlockData_ImageWebPageBlock_Fragment
  ) | (
    { __typename: 'TextCarouselWebPageBlock' }
    & WebPageBlockData_TextCarouselWebPageBlock_Fragment
  ) | (
    { __typename: 'TextImageWebPageBlock' }
    & WebPageBlockData_TextImageWebPageBlock_Fragment
  )> }
);

export const WebPageDataFragmentDoc = /*#__PURE__*/ gql`
    fragment WebPageData on WebPage {
  id
  moduleId
  bannerTitle {
    ...TranslatedStringData
  }
  bannerSubTitle {
    ...TranslatedStringData
  }
  pageTitle {
    ...TranslatedStringData
  }
  module {
    ... on WebsiteModule {
      ...WebsiteModulePublicSpecs
    }
  }
  metaTitle
  metaDescription
  urlSlug
  liveChatDisplay
  bannerImage {
    ...UploadFileWithPreviewFormData
  }
  mobileBannerImage {
    ...UploadFileWithPreviewFormData
  }
  versioning {
    updatedAt
    createdAt
  }
  webPageBlock {
    ...WebPageBlockData
  }
  isActive
}
    `;