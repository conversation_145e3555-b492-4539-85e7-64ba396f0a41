fragment LeadData on Lead {
    ...StandardLeadData
    ...FinderLeadData
    ...EventLeadData
    ...LaunchpadLeadData
    ...ConfiguratorLeadData
    ...MobilityLeadData

    id

    dealerId

    identifier

    status

    isLead

    permissions

    customerId
    customer {
        ...CustomerSpecs
    }

    companyId
    company {
        id
        displayName
        timeZone
    }

    assignee {
        id
        displayName
    }

    availableAssignees {
        ...UsersOptionsData
    }

    campaignValues {
        capCampaignId
    }

    capValues {
        businessPartnerId
        businessPartnerGuid
        leadId
        leadGuid
        salesPersonId
        salesPersonName
    }

    customerKYC {
        ...KYCFieldSpecs
    }

    versioning {
        createdAt
        updatedAt
        createdBy {
            ...AuthorData
        }
        suiteId
    }

    moduleId
    module {
        id
        displayName

        company {
            id
            timeZone
            currency
            countryCode
            roundings {
                amount {
                    decimals
                }
                percentage {
                    decimals
                }
            }
            coe
            ppsr
            estFee
        }

        ... on LaunchPadModule {
            ...LaunchPadModuleSpecs
        }

        ... on StandardApplicationModule {
            id
            capModuleId
            capPrequalification
            leadCampaignId

            customerModule {
                ... on LocalCustomerManagementModule {
                    id
                    displayName
                    extraSettings {
                        ...KYCExtraSettingsSpecs
                    }
                }
            }

            vehicleModuleId
            dealerVehicles {
                ...DealerVehiclesSpecs
            }
        }

        ... on FinderApplicationPrivateModule {
            id
            capModuleId
            capPrequalification
            leadCampaignId

            customerModule {
                ... on LocalCustomerManagementModule {
                    id
                    displayName
                    extraSettings {
                        ...KYCExtraSettingsSpecs
                    }
                }
            }

            vehicleModuleId
            dealerVehicles {
                ...DealerVehiclesSpecs
            }
        }

        ... on FinderApplicationPublicModule {
            id
            capModuleId
            capPrequalification
            leadCampaignId

            customerModule {
                ... on LocalCustomerManagementModule {
                    id
                    displayName
                    extraSettings {
                        ...KYCExtraSettingsSpecs
                    }
                }
            }

            vehicleModuleId
            dealerVehicles {
                ...DealerVehiclesSpecs
            }
        }

        ... on ConfiguratorModule {
            id
            capModuleId
            capPrequalification
            leadCampaignId

            customerModule {
                ... on LocalCustomerManagementModule {
                    id
                    displayName
                    extraSettings {
                        ...KYCExtraSettingsSpecs
                    }
                }
            }

            vehicleModuleId
            dealerVehicles {
                ...DealerVehiclesSpecs
            }
        }

        ... on EventApplicationModule {
            id
            capModuleId

            customerModule {
                ... on LocalCustomerManagementModule {
                    id
                    displayName
                    extraSettings {
                        ...KYCExtraSettingsSpecs
                    }
                }
            }

            vehicleModuleId
            dealerVehicles {
                ...DealerVehiclesSpecs
            }
        }
    }

    leadModule {
        ...LaunchPadModuleSpecs
    }

    customerAgreements {
        ...ApplicationAgreementData
    }

    tradeInVehicle {
        ...TradeInVehicleData
    }

    dealer {
        ...DealerApplicationFragment
    }

    documents {
        ...ApplicationDocumentData
    }

    originSalesConsultantId
    originSalesConsultant {
        displayName
    }

    applications {
        ...ReferenceApplicationData
    }

    vehicleCondition

    purchaseIntention

    purposeOfVisit

    intentType

    isCustomerSearchPerformed

    salesOffer {
        ...SalesOfferSpecs
    }
}
