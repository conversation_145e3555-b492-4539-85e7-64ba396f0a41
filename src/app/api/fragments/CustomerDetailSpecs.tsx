import type * as SchemaTypes from '../types';

import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from './LocalCustomerFieldData';
import type { KycFieldSpecsFragment } from './KYCFieldSpecs';
import type { ApplicationInCustomerDetails_ConfiguratorApplication_Fragment, ApplicationInCustomerDetails_EventApplication_Fragment, ApplicationInCustomerDetails_FinderApplication_Fragment, ApplicationInCustomerDetails_LaunchpadApplication_Fragment, ApplicationInCustomerDetails_MobilityApplication_Fragment, ApplicationInCustomerDetails_SalesOfferApplication_Fragment, ApplicationInCustomerDetails_StandardApplication_Fragment } from './ApplicationInCustomerDetails';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from './ApplicationStageData';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { CustomerDetailsStandardApplicationFragment } from './CustomerDetailsStandardApplication';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { ApplicationAdyenDepositDataFragment } from './ApplicationAdyenDepositData';
import type { ApplicationPorscheDepositDataFragment } from './ApplicationPorscheDepositData';
import type { ApplicationFiservDepositDataFragment } from './ApplicationFiservDepositData';
import type { ApplicationPayGateDepositDataFragment } from './ApplicationPayGateDepositData';
import type { ApplicationTtbDepositDataFragment } from './ApplicationTtbDepositData';
import type { CustomerDetailsEventApplicationFragment } from './CustomerDetailsEventApplication';
import type { ApplicationEventCustomizedFieldDataFragment } from './ApplicationEventCustomizedFieldData';
import type { CustomerDetailsConfiguratorApplicationFragment } from './CustomerDetailsConfiguratorApplication';
import type { CustomerDetailsFinderApplicationFragment } from './CustomerDetailsFinderApplication';
import type { CustomerDetailsMobilityApplicationFragment } from './CustomerDetailsMobilityApplication';
import type { PeriodDataFragment } from './PeriodData';
import type { CustomerDetailsLaunchpadApplicationFragment } from './CustomerDetailsLaunchpadApplication';
import type { CustomerDetailsSalesOfferApplicationFragment } from './CustomerDetailsSalesOfferApplication';
import { gql } from '@apollo/client';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { LocalCustomerFieldDataFragmentDoc } from './LocalCustomerFieldData';
import { KycFieldSpecsFragmentDoc } from './KYCFieldSpecs';
import { ApplicationInCustomerDetailsFragmentDoc } from './ApplicationInCustomerDetails';
import { ApplicationStageDataFragmentDoc } from './ApplicationStageData';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { CustomerDetailsStandardApplicationFragmentDoc } from './CustomerDetailsStandardApplication';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { ApplicationAdyenDepositDataFragmentDoc } from './ApplicationAdyenDepositData';
import { ApplicationPorscheDepositDataFragmentDoc } from './ApplicationPorscheDepositData';
import { ApplicationFiservDepositDataFragmentDoc } from './ApplicationFiservDepositData';
import { ApplicationPayGateDepositDataFragmentDoc } from './ApplicationPayGateDepositData';
import { ApplicationTtbDepositDataFragmentDoc } from './ApplicationTtbDepositData';
import { CustomerDetailsEventApplicationFragmentDoc } from './CustomerDetailsEventApplication';
import { ApplicationEventCustomizedFieldDataFragmentDoc } from './ApplicationEventCustomizedFieldData';
import { CustomerDetailsConfiguratorApplicationFragmentDoc } from './CustomerDetailsConfiguratorApplication';
import { CustomerDetailsFinderApplicationFragmentDoc } from './CustomerDetailsFinderApplication';
import { CustomerDetailsMobilityApplicationFragmentDoc } from './CustomerDetailsMobilityApplication';
import { PeriodDataFragmentDoc } from './PeriodData';
import { CustomerDetailsLaunchpadApplicationFragmentDoc } from './CustomerDetailsLaunchpadApplication';
import { CustomerDetailsSalesOfferApplicationFragmentDoc } from './CustomerDetailsSalesOfferApplication';
export type CustomerDetailSpecs_CorporateCustomer_Fragment = (
  { __typename: 'CorporateCustomer' }
  & Pick<SchemaTypes.CorporateCustomer, 'id' | 'fullName' | 'latestFullName' | 'isMaskingCustomerData'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { extraSettings: (
      { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
      & KycExtraSettingsSpecsFragment
    ), company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ), fields: Array<(
    { __typename: 'LocalCustomerArrayStringField' }
    & LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment
  ) | (
    { __typename: 'LocalCustomerDateField' }
    & LocalCustomerFieldData_LocalCustomerDateField_Fragment
  ) | (
    { __typename: 'LocalCustomerDrivingLicenseField' }
    & LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment
  ) | (
    { __typename: 'LocalCustomerNumberField' }
    & LocalCustomerFieldData_LocalCustomerNumberField_Fragment
  ) | (
    { __typename: 'LocalCustomerPhoneField' }
    & LocalCustomerFieldData_LocalCustomerPhoneField_Fragment
  ) | (
    { __typename: 'LocalCustomerReferenceDetailSetField' }
    & LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment
  ) | (
    { __typename: 'LocalCustomerSalaryTransferredBankSetField' }
    & LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment
  ) | (
    { __typename: 'LocalCustomerStringDescriptionField' }
    & LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment
  ) | (
    { __typename: 'LocalCustomerStringField' }
    & LocalCustomerFieldData_LocalCustomerStringField_Fragment
  ) | (
    { __typename: 'LocalCustomerUAEIdentitySetField' }
    & LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment
  ) | (
    { __typename: 'LocalCustomerUploadsField' }
    & LocalCustomerFieldData_LocalCustomerUploadsField_Fragment
  ) | (
    { __typename: 'LocalCustomerVerifiedPhoneField' }
    & LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment
  )>, applications: Array<(
    { __typename: 'ConfiguratorApplication' }
    & ApplicationInCustomerDetails_ConfiguratorApplication_Fragment
  ) | (
    { __typename: 'EventApplication' }
    & ApplicationInCustomerDetails_EventApplication_Fragment
  ) | (
    { __typename: 'FinderApplication' }
    & ApplicationInCustomerDetails_FinderApplication_Fragment
  ) | (
    { __typename: 'LaunchpadApplication' }
    & ApplicationInCustomerDetails_LaunchpadApplication_Fragment
  ) | (
    { __typename: 'MobilityApplication' }
    & ApplicationInCustomerDetails_MobilityApplication_Fragment
  ) | (
    { __typename: 'SalesOfferApplication' }
    & ApplicationInCustomerDetails_SalesOfferApplication_Fragment
  ) | (
    { __typename: 'StandardApplication' }
    & ApplicationInCustomerDetails_StandardApplication_Fragment
  )>, kycPresets: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
);

export type CustomerDetailSpecs_Guarantor_Fragment = (
  { __typename: 'Guarantor' }
  & Pick<SchemaTypes.Guarantor, 'id' | 'fullName' | 'isMaskingCustomerData'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { extraSettings: (
      { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
      & KycExtraSettingsSpecsFragment
    ), company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ), fields: Array<(
    { __typename: 'LocalCustomerArrayStringField' }
    & LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment
  ) | (
    { __typename: 'LocalCustomerDateField' }
    & LocalCustomerFieldData_LocalCustomerDateField_Fragment
  ) | (
    { __typename: 'LocalCustomerDrivingLicenseField' }
    & LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment
  ) | (
    { __typename: 'LocalCustomerNumberField' }
    & LocalCustomerFieldData_LocalCustomerNumberField_Fragment
  ) | (
    { __typename: 'LocalCustomerPhoneField' }
    & LocalCustomerFieldData_LocalCustomerPhoneField_Fragment
  ) | (
    { __typename: 'LocalCustomerReferenceDetailSetField' }
    & LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment
  ) | (
    { __typename: 'LocalCustomerSalaryTransferredBankSetField' }
    & LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment
  ) | (
    { __typename: 'LocalCustomerStringDescriptionField' }
    & LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment
  ) | (
    { __typename: 'LocalCustomerStringField' }
    & LocalCustomerFieldData_LocalCustomerStringField_Fragment
  ) | (
    { __typename: 'LocalCustomerUAEIdentitySetField' }
    & LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment
  ) | (
    { __typename: 'LocalCustomerUploadsField' }
    & LocalCustomerFieldData_LocalCustomerUploadsField_Fragment
  ) | (
    { __typename: 'LocalCustomerVerifiedPhoneField' }
    & LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment
  )>, kycPresets: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
);

export type CustomerDetailSpecs_LocalCustomer_Fragment = (
  { __typename: 'LocalCustomer' }
  & Pick<SchemaTypes.LocalCustomer, 'id' | 'fullName' | 'latestFullName' | 'isMaskingCustomerData' | 'customerCiamId' | 'businessPartnerIds'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { extraSettings: (
      { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
      & KycExtraSettingsSpecsFragment
    ), company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    ) }
  ), fields: Array<(
    { __typename: 'LocalCustomerArrayStringField' }
    & LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment
  ) | (
    { __typename: 'LocalCustomerDateField' }
    & LocalCustomerFieldData_LocalCustomerDateField_Fragment
  ) | (
    { __typename: 'LocalCustomerDrivingLicenseField' }
    & LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment
  ) | (
    { __typename: 'LocalCustomerNumberField' }
    & LocalCustomerFieldData_LocalCustomerNumberField_Fragment
  ) | (
    { __typename: 'LocalCustomerPhoneField' }
    & LocalCustomerFieldData_LocalCustomerPhoneField_Fragment
  ) | (
    { __typename: 'LocalCustomerReferenceDetailSetField' }
    & LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment
  ) | (
    { __typename: 'LocalCustomerSalaryTransferredBankSetField' }
    & LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment
  ) | (
    { __typename: 'LocalCustomerStringDescriptionField' }
    & LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment
  ) | (
    { __typename: 'LocalCustomerStringField' }
    & LocalCustomerFieldData_LocalCustomerStringField_Fragment
  ) | (
    { __typename: 'LocalCustomerUAEIdentitySetField' }
    & LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment
  ) | (
    { __typename: 'LocalCustomerUploadsField' }
    & LocalCustomerFieldData_LocalCustomerUploadsField_Fragment
  ) | (
    { __typename: 'LocalCustomerVerifiedPhoneField' }
    & LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment
  )>, kycPresets: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, applications: Array<(
    { __typename: 'ConfiguratorApplication' }
    & ApplicationInCustomerDetails_ConfiguratorApplication_Fragment
  ) | (
    { __typename: 'EventApplication' }
    & ApplicationInCustomerDetails_EventApplication_Fragment
  ) | (
    { __typename: 'FinderApplication' }
    & ApplicationInCustomerDetails_FinderApplication_Fragment
  ) | (
    { __typename: 'LaunchpadApplication' }
    & ApplicationInCustomerDetails_LaunchpadApplication_Fragment
  ) | (
    { __typename: 'MobilityApplication' }
    & ApplicationInCustomerDetails_MobilityApplication_Fragment
  ) | (
    { __typename: 'SalesOfferApplication' }
    & ApplicationInCustomerDetails_SalesOfferApplication_Fragment
  ) | (
    { __typename: 'StandardApplication' }
    & ApplicationInCustomerDetails_StandardApplication_Fragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
);

export type CustomerDetailSpecsFragment = CustomerDetailSpecs_CorporateCustomer_Fragment | CustomerDetailSpecs_Guarantor_Fragment | CustomerDetailSpecs_LocalCustomer_Fragment;

export const CustomerDetailSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment CustomerDetailSpecs on Customer {
  ... on LocalCustomer {
    id
    fullName
    latestFullName
    module {
      ... on LocalCustomerManagementModule {
        extraSettings {
          ...KYCExtraSettingsSpecs
        }
      }
      company {
        timeZone
        countryCode
      }
    }
    fields {
      ...LocalCustomerFieldData
    }
    kycPresets {
      ...KYCFieldSpecs
    }
    applications {
      ...ApplicationInCustomerDetails
    }
    versioning {
      ...AdvancedVersioningData
    }
    isMaskingCustomerData
    customerCiamId
    businessPartnerIds
  }
  ... on CorporateCustomer {
    id
    fullName
    latestFullName
    module {
      ... on LocalCustomerManagementModule {
        extraSettings {
          ...KYCExtraSettingsSpecs
        }
      }
      company {
        timeZone
        countryCode
      }
    }
    fields {
      ...LocalCustomerFieldData
    }
    applications {
      ...ApplicationInCustomerDetails
    }
    kycPresets {
      ...KYCFieldSpecs
    }
    versioning {
      ...AdvancedVersioningData
    }
    isMaskingCustomerData
  }
  ... on Guarantor {
    id
    fullName
    module {
      ... on LocalCustomerManagementModule {
        extraSettings {
          ...KYCExtraSettingsSpecs
        }
      }
      company {
        timeZone
        countryCode
      }
    }
    fields {
      ...LocalCustomerFieldData
    }
    kycPresets {
      ...KYCFieldSpecs
    }
    versioning {
      ...AdvancedVersioningData
    }
    isMaskingCustomerData
  }
}
    `;