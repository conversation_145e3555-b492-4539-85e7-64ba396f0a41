fragment EventLeadData on EventLead {
    vehicle {
        ...VehicleSpecs
    }

    event {
        capPrequalification
        isCapEnabled
        kycExtraSettings {
            ...KYCExtraSettingsSpecs
        }
        utmParametersSettings {
            defaultValue {
                capCampaignId
                capLeadSource
                capLeadOrigin
            }
            overrides {
                capCampaignId
                capLeadSource
                capLeadOrigin
                utmUrl
            }
        }
    }

    customizedFields {
        ...ApplicationEventCustomizedFieldData
    }

    eventId

    mergedToLeadSuiteId
    mergedToLeadIdentifier
}
