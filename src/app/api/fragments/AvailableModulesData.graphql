fragment AvailableModulesData on AvailableModules {
    hasApplicationModules
    hasConfiguratorModules
    hasCustomerModules
    hasVehicleManagementModules
    hasFinderVehicleManagementModules
    hasPromotionCodeModules
    hasInventories
    hasEventModules
    hasAgreementModules
    hasFinanceProducts
    hasPaymentModules
    hasBanks
    hasSystemBanks
    hasInsuranceModules
    hasLabelsModules
    hasMobilityModules
    hasWebsiteModules
    hasCtsModules
    hasPorscheMasterDataModules
    hasConsentModules
    hasCapModules
    hasPorscheRetainModules
    hasAppointmentModules
    hasAutoplayModules
    hasTradeInModules
    hasGiftVoucherModules
    hasVisitAppointmentModules
    hasMarketingModules
    hasLaunchPadModules
    hasSalesControlBoardModule
}
