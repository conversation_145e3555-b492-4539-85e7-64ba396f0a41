fragment UserCompanyContextData on Company {
    id
    color
    currency
    theme
    countryCode
    displayName
    coe
    ppsr
    estFee

    copyright {
        ...TranslatedStringData
    }

    description {
        ...TranslatedStringData
    }

    companyName {
        ...TranslatedStringData
    }

    legalName {
        ...TranslatedStringData
    }

    languages {
        ...LanguagePackContextData
    }

    logo {
        id
        url
    }
    logoNonWhiteBackground {
        id
        url
    }

    favicon {
        ...UploadFileWithPreviewFormData
    }

    font {
        id
        url
    }

    fontBold {
        id
        url
    }

    roundings {
        amount {
            decimals
        }
        percentage {
            decimals
        }
    }
    calculationRounding
    vatRateSettings {
        vatRateTable {
            startDate
            value
        }
        appliedVATRate
    }
    sessionTimeout
    timeZone

    modules {
        id
        displayName
        __typename

        # For finder, need to check the LTA availability
        ... on FinderVehicleManagementModule {
            setting {
                allowLTA
            }
        }

        ... on StandardApplicationModule {
            promoCodeModuleId
        }

        ... on FinderApplicationPublicModule {
            promoCodeModuleId
            bankModuleId
            vehicleModuleId
            finderVehicleConditions
        }

        ... on FinderApplicationPrivateModule {
            promoCodeModuleId
        }

        ... on MobilityModule {
            promoCodeModuleId
        }

        ... on ConfiguratorModule {
            promoCodeModuleId
        }

        ... on PorscheRetainModule {
            link
        }

        ... on LaunchPadModule {
            hasTradeInRequest
        }
    }

    availableModules {
        ...AvailableModulesData
    }

    availableDealers {
        ...CompanyDealerData
    }

    activeMaintenanceModule {
        ...MaintenanceUpdate
    }

    roles {
        id
        users {
            ...UserAvatarSpecs
        }
    }

    userGroups {
        id
        users {
            ...UserAvatarSpecs
        }
    }

    permissions

    edmEmailFooter {
        ...EdmEmailFooterPublicData
    }

    enableContentRefinement

    isInstantApprovalStatsEnabled

    allowLimitDealerFeature

    shouldSendCalendarInvite
}
