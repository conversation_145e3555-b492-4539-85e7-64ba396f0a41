import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { LanguagePackContextDataFragment } from './LanguagePackContextData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { AvailableModulesDataFragment } from './AvailableModulesData';
import type { CompanyDealerDataFragment } from './CompanyDealerData';
import type { MaintenanceUpdateFragment } from './MaintenanceUpdate';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { UserAvatarSpecsFragment } from './UserAvatarSpecs';
import type { EdmEmailFooterPublicDataFragment } from './EdmEmailFooterPublicData';
import type { EdmSocialMediaDataFragment } from './EdmSocialMediaData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { LanguagePackContextDataFragmentDoc } from './LanguagePackContextData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { AvailableModulesDataFragmentDoc } from './AvailableModulesData';
import { CompanyDealerDataFragmentDoc } from './CompanyDealerData';
import { MaintenanceUpdateFragmentDoc } from './MaintenanceUpdate';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { UserAvatarSpecsFragmentDoc } from './UserAvatarSpecs';
import { EdmEmailFooterPublicDataFragmentDoc } from './EdmEmailFooterPublicData';
import { EdmSocialMediaDataFragmentDoc } from './EdmSocialMediaData';
export type CompanyContextDataFragment = (
  { __typename: 'Company' }
  & Pick<SchemaTypes.Company, 'id' | 'color' | 'currency' | 'theme' | 'countryCode' | 'displayName' | 'coe' | 'ppsr' | 'estFee' | 'calculationRounding' | 'sessionTimeout' | 'timeZone' | 'enableContentRefinement' | 'isInstantApprovalStatsEnabled' | 'allowLimitDealerFeature' | 'addressAutofill' | 'findNearbyDealer'>
  & { copyright: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, companyName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), languages: Array<(
    { __typename: 'LanguagePack' }
    & LanguagePackContextDataFragment
  )>, logo?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & Pick<SchemaTypes.UploadedFileWithPreview, 'id' | 'url'>
  )>, logoNonWhiteBackground?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & Pick<SchemaTypes.UploadedFileWithPreview, 'id' | 'url'>
  )>, mobileLogo?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & Pick<SchemaTypes.UploadedFileWithPreview, 'id' | 'url'>
  )>, favicon?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, font?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFile' }
    & Pick<SchemaTypes.UploadedFile, 'id' | 'url'>
  )>, fontBold?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFile' }
    & Pick<SchemaTypes.UploadedFile, 'id' | 'url'>
  )>, roundings: (
    { __typename: 'Roundings' }
    & { amount: (
      { __typename: 'Rounding' }
      & Pick<SchemaTypes.Rounding, 'decimals'>
    ), percentage: (
      { __typename: 'Rounding' }
      & Pick<SchemaTypes.Rounding, 'decimals'>
    ) }
  ), vatRateSettings?: SchemaTypes.Maybe<(
    { __typename: 'VATRateSettings' }
    & Pick<SchemaTypes.VatRateSettings, 'appliedVATRate'>
    & { vatRateTable: Array<(
      { __typename: 'VATRateSettingItem' }
      & Pick<SchemaTypes.VatRateSettingItem, 'startDate' | 'value'>
    )> }
  )>, modules: Array<(
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'promoCodeModuleId' | 'id' | 'displayName'>
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'promoCodeModuleId' | 'id' | 'displayName'>
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'promoCodeModuleId' | 'bankModuleId' | 'vehicleModuleId' | 'finderVehicleConditions' | 'id' | 'displayName'>
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { setting: (
      { __typename: 'FinderVehicleManagementSetting' }
      & Pick<SchemaTypes.FinderVehicleManagementSetting, 'allowLTA'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'hasTradeInRequest' | 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'promoCodeModuleId' | 'id' | 'displayName'>
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'link' | 'id' | 'displayName'>
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'promoCodeModuleId' | 'id' | 'displayName'>
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
  )>, availableModules: (
    { __typename: 'AvailableModules' }
    & AvailableModulesDataFragment
  ), availableDealers?: SchemaTypes.Maybe<Array<(
    { __typename: 'Dealer' }
    & CompanyDealerDataFragment
  )>>, activeMaintenanceModule?: SchemaTypes.Maybe<(
    { __typename: 'MaintenanceModule' }
    & MaintenanceUpdateFragment
  )>, roles: Array<(
    { __typename: 'Role' }
    & Pick<SchemaTypes.Role, 'id'>
    & { users: Array<(
      { __typename: 'User' }
      & UserAvatarSpecsFragment
    )> }
  )>, userGroups: Array<(
    { __typename: 'UserGroup' }
    & Pick<SchemaTypes.UserGroup, 'id'>
    & { users: Array<(
      { __typename: 'User' }
      & UserAvatarSpecsFragment
    )> }
  )>, edmEmailFooter: (
    { __typename: 'EdmEmailFooter' }
    & EdmEmailFooterPublicDataFragment
  ) }
);

export const CompanyContextDataFragmentDoc = /*#__PURE__*/ gql`
    fragment CompanyContextData on Company {
  id
  color
  currency
  theme
  countryCode
  displayName
  coe
  ppsr
  estFee
  copyright {
    ...TranslatedStringData
  }
  description {
    ...TranslatedStringData
  }
  companyName {
    ...TranslatedStringData
  }
  legalName {
    ...TranslatedStringData
  }
  languages {
    ...LanguagePackContextData
  }
  logo {
    id
    url
  }
  logoNonWhiteBackground {
    id
    url
  }
  mobileLogo {
    id
    url
  }
  favicon {
    ...UploadFileWithPreviewFormData
  }
  font {
    id
    url
  }
  fontBold {
    id
    url
  }
  roundings {
    amount {
      decimals
    }
    percentage {
      decimals
    }
  }
  calculationRounding
  vatRateSettings {
    vatRateTable {
      startDate
      value
    }
    appliedVATRate
  }
  sessionTimeout
  timeZone
  modules {
    id
    displayName
    __typename
    ... on FinderVehicleManagementModule {
      setting {
        allowLTA
      }
    }
    ... on StandardApplicationModule {
      promoCodeModuleId
    }
    ... on FinderApplicationPublicModule {
      promoCodeModuleId
      bankModuleId
      vehicleModuleId
      finderVehicleConditions
    }
    ... on FinderApplicationPrivateModule {
      promoCodeModuleId
    }
    ... on MobilityModule {
      promoCodeModuleId
    }
    ... on ConfiguratorModule {
      promoCodeModuleId
    }
    ... on PorscheRetainModule {
      link
    }
    ... on LaunchPadModule {
      hasTradeInRequest
    }
  }
  availableModules {
    ...AvailableModulesData
  }
  availableDealers {
    ...CompanyDealerData
  }
  activeMaintenanceModule {
    ...MaintenanceUpdate
  }
  roles {
    id
    users {
      ...UserAvatarSpecs
    }
  }
  userGroups {
    id
    users {
      ...UserAvatarSpecs
    }
  }
  edmEmailFooter {
    ...EdmEmailFooterPublicData
  }
  enableContentRefinement
  isInstantApprovalStatsEnabled
  allowLimitDealerFeature
  addressAutofill
  findNearbyDealer
}
    `;