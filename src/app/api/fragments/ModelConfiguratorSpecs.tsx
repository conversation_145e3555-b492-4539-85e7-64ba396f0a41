import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { VariantConfiguratorSpecsFragment } from './VariantConfiguratorSpecs';
import type { CompanySpecsFragment } from './CompanySpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { ColorBlockSpecsFragment } from './ColorBlockSpecs';
import type { ColorAndTrimSettingsSpecsFragment } from './ColorAndTrimSettingsSpecs';
import type { TrimBlockSpecsFragment } from './TrimBlockSpecs';
import type { PackageBlockSpecsFragment } from './PackageBlockSpecs';
import type { PackageSettingsSpecsFragment } from './PackageSettingsSpecs';
import type { AdditionalDetailsSpecsFragment } from './AdditionalDetailsSpecs';
import type { PackageTypeSpecs_PackageTypeWithDescription_Fragment, PackageTypeSpecs_PackageTypeWithPrice_Fragment } from './PackageTypeSpecs';
import type { OptionsBlockSpecsFragment } from './OptionsBlockSpecs';
import type { OptionSettingDetails_ComboOptionSettings_Fragment, OptionSettingDetails_DropdownOptionSettings_Fragment, OptionSettingDetails_MultiSelectOptionSettings_Fragment, OptionSettingDetails_SingleSelectOptionSettings_Fragment } from './OptionSettingDetails';
import type { MatrixSpecsFragment } from './MatrixSpecs';
import type { InventoryDetailsData_ConfiguratorInventory_Fragment, InventoryDetailsData_MobilityInventory_Fragment } from './InventoryDetailsData';
import type { StockInventorySpecs_ConfiguratorStockInventory_Fragment, StockInventorySpecs_MobilityStockInventory_Fragment } from './StockInventorySpecs';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from './ApplicationStageData';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from './ReferenceApplicationData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { TradeInVehicleDataFragment } from './TradeInVehicleData';
import type { CompanyPublicSpecsFragment } from './CompanyPublicSpecs';
import type { PeriodDataFragment } from './PeriodData';
import type { StockBlockingPeriodDataFragment } from './StockBlockingPeriod';
import type { ConfiguratorInventoryPublicSpecsFragment } from './ConfiguratorInventoryPublicSpecs';
import type { MobilityInventoryPublicSpecsFragment } from './MobilityInventoryPublicSpecs';
import type { MobilityModuleSpecsFragment } from './MobilityModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerBookingCodeSpecsFragment } from './DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from './MobilitySigningSettingSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { KycPresetsOptionsDataFragment } from './KYCPresetsOptionsData';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from './MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from './MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from './MobilityEmailContentData';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { MobilityOperatorEmailContentDataFragment } from './MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from './MobilityHomeDeliveryData';
import type { CounterSettingsSpecsFragment } from './CounterSettingsSpecs';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { VariantConfiguratorSpecsFragmentDoc } from './VariantConfiguratorSpecs';
import { CompanySpecsFragmentDoc } from './CompanySpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { ColorBlockSpecsFragmentDoc } from './ColorBlockSpecs';
import { ColorAndTrimSettingsSpecsFragmentDoc } from './ColorAndTrimSettingsSpecs';
import { TrimBlockSpecsFragmentDoc } from './TrimBlockSpecs';
import { PackageBlockSpecsFragmentDoc } from './PackageBlockSpecs';
import { PackageSettingsSpecsFragmentDoc } from './PackageSettingsSpecs';
import { AdditionalDetailsSpecsFragmentDoc } from './AdditionalDetailsSpecs';
import { PackageTypeSpecsFragmentDoc } from './PackageTypeSpecs';
import { OptionsBlockSpecsFragmentDoc } from './OptionsBlockSpecs';
import { OptionSettingDetailsFragmentDoc } from './OptionSettingDetails';
import { MatrixSpecsFragmentDoc } from './MatrixSpecs';
import { InventoryDetailsDataFragmentDoc } from './InventoryDetailsData';
import { StockInventorySpecsFragmentDoc } from './StockInventorySpecs';
import { ApplicationStageDataFragmentDoc } from './ApplicationStageData';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from './ReferenceApplicationData';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { TradeInVehicleDataFragmentDoc } from './TradeInVehicleData';
import { CompanyPublicSpecsFragmentDoc } from './CompanyPublicSpecs';
import { PeriodDataFragmentDoc } from './PeriodData';
import { StockBlockingPeriodDataFragmentDoc } from './StockBlockingPeriod';
import { ConfiguratorInventoryPublicSpecsFragmentDoc } from './ConfiguratorInventoryPublicSpecs';
import { MobilityInventoryPublicSpecsFragmentDoc } from './MobilityInventoryPublicSpecs';
import { MobilityModuleSpecsFragmentDoc } from './MobilityModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerBookingCodeSpecsFragmentDoc } from './DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from './MobilitySigningSettingSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { KycPresetsOptionsDataFragmentDoc } from './KYCPresetsOptionsData';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from './MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from './MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from './MobilityEmailContentData';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { MobilityOperatorEmailContentDataFragmentDoc } from './MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from './MobilityHomeDeliveryData';
import { CounterSettingsSpecsFragmentDoc } from './CounterSettingsSpecs';
export type ModelConfiguratorSpecsFragment = (
  { __typename: 'ModelConfigurator' }
  & Pick<SchemaTypes.ModelConfigurator, 'id' | 'isActive' | 'displayName' | 'externalUrl' | 'urlIdentifier' | 'modelId' | 'moduleId' | 'bannerTextPosition' | 'variantConfiguratorIds'>
  & { model: (
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'vehicleModuleId'>
    & { priceDisclaimer: (
      { __typename: 'DealerDisclaimersConfigurator' }
      & { defaultValue: Array<SchemaTypes.Maybe<(
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      )>>, overrides: Array<(
        { __typename: 'DealerDisclaimersOverridesConfigurator' }
        & Pick<SchemaTypes.DealerDisclaimersOverridesConfigurator, 'dealerId'>
        & { value: Array<(
          { __typename: 'TranslatedString' }
          & TranslatedStringDataFragment
        )> }
      )> }
    ), company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ), banner?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFile' }
    & UploadFileFormDataFragment
  )>, mobileBanner?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFile' }
    & UploadFileFormDataFragment
  )>, descriptionImages: Array<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, bannerText?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, pageTitle: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), pageSubtitle: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), pageDescription: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), configuratorTitle: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), variantConfigurators: Array<(
    { __typename: 'VariantConfigurator' }
    & VariantConfiguratorSpecsFragment
  )>, versioning: (
    { __typename: 'SimpleVersioning' }
    & SimpleVersioningDataFragment
  ) }
);

export const ModelConfiguratorSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment ModelConfiguratorSpecs on ModelConfigurator {
  id
  isActive
  displayName
  externalUrl
  urlIdentifier
  modelId
  model {
    id
    name {
      ...TranslatedStringData
    }
  }
  moduleId
  module {
    ... on ConfiguratorModule {
      id
      vehicleModuleId
      priceDisclaimer {
        defaultValue {
          ...TranslatedStringData
        }
        overrides {
          dealerId
          value {
            ...TranslatedStringData
          }
        }
      }
    }
    company {
      timeZone
    }
  }
  banner {
    ...UploadFileFormData
  }
  mobileBanner {
    ...UploadFileFormData
  }
  descriptionImages {
    ...UploadFileWithPreviewFormData
  }
  bannerText {
    ...TranslatedStringData
  }
  bannerTextPosition
  pageTitle {
    ...TranslatedStringData
  }
  pageSubtitle {
    ...TranslatedStringData
  }
  pageDescription {
    ...TranslatedStringData
  }
  configuratorTitle {
    ...TranslatedStringData
  }
  variantConfiguratorIds
  variantConfigurators {
    ...VariantConfiguratorSpecs
  }
  versioning {
    ...SimpleVersioningData
  }
}
    `;