import type * as SchemaTypes from '../types';

import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './BaseConditionSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { MarketingPlatformSpecsFragment } from './MarketingPlatformSpecs';
import { gql } from '@apollo/client';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { ConditionSpecsFragmentDoc } from './ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from './BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { MarketingPlatformSpecsFragmentDoc } from './MarketingPlatformSpecs';
export type ConsentsAndDeclarationsSpecs_CheckboxConsentsAndDeclarations_Fragment = (
  { __typename: 'CheckboxConsentsAndDeclarations' }
  & Pick<SchemaTypes.CheckboxConsentsAndDeclarations, 'isMandatory' | 'legalTextPosition' | 'id' | 'type' | 'displayName' | 'moduleId' | 'orderNumber' | 'isActive' | 'purpose' | 'dataField'>
  & { legalMarkup?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ), title?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  )>, description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ), conditions?: SchemaTypes.Maybe<Array<(
    { __typename: 'ApplicationModuleCondition' }
    & ConditionSpecs_ApplicationModuleCondition_Fragment
  ) | (
    { __typename: 'BankCondition' }
    & ConditionSpecs_BankCondition_Fragment
  ) | (
    { __typename: 'ContextualCondition' }
    & ConditionSpecs_ContextualCondition_Fragment
  ) | (
    { __typename: 'DealerCondition' }
    & ConditionSpecs_DealerCondition_Fragment
  ) | (
    { __typename: 'GiftVoucherCondition' }
    & ConditionSpecs_GiftVoucherCondition_Fragment
  ) | (
    { __typename: 'InsurerCondition' }
    & ConditionSpecs_InsurerCondition_Fragment
  ) | (
    { __typename: 'LocationCondition' }
    & ConditionSpecs_LocationCondition_Fragment
  ) | (
    { __typename: 'LogicCondition' }
    & ConditionSpecs_LogicCondition_Fragment
  ) | (
    { __typename: 'SalesOfferAgreementsCondition' }
    & ConditionSpecs_SalesOfferAgreementsCondition_Fragment
  )>>, featurePurpose: (
    { __typename: 'ConsentFeaturePurpose' }
    & Pick<SchemaTypes.ConsentFeaturePurpose, 'type' | 'featureId'>
  ) }
);

export type ConsentsAndDeclarationsSpecs_GroupConsentsAndDeclarations_Fragment = (
  { __typename: 'GroupConsentsAndDeclarations' }
  & Pick<SchemaTypes.GroupConsentsAndDeclarations, 'id' | 'type' | 'displayName' | 'moduleId' | 'orderNumber' | 'isActive' | 'purpose' | 'dataField'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ), title?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  )>, description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ), conditions?: SchemaTypes.Maybe<Array<(
    { __typename: 'ApplicationModuleCondition' }
    & ConditionSpecs_ApplicationModuleCondition_Fragment
  ) | (
    { __typename: 'BankCondition' }
    & ConditionSpecs_BankCondition_Fragment
  ) | (
    { __typename: 'ContextualCondition' }
    & ConditionSpecs_ContextualCondition_Fragment
  ) | (
    { __typename: 'DealerCondition' }
    & ConditionSpecs_DealerCondition_Fragment
  ) | (
    { __typename: 'GiftVoucherCondition' }
    & ConditionSpecs_GiftVoucherCondition_Fragment
  ) | (
    { __typename: 'InsurerCondition' }
    & ConditionSpecs_InsurerCondition_Fragment
  ) | (
    { __typename: 'LocationCondition' }
    & ConditionSpecs_LocationCondition_Fragment
  ) | (
    { __typename: 'LogicCondition' }
    & ConditionSpecs_LogicCondition_Fragment
  ) | (
    { __typename: 'SalesOfferAgreementsCondition' }
    & ConditionSpecs_SalesOfferAgreementsCondition_Fragment
  )>>, featurePurpose: (
    { __typename: 'ConsentFeaturePurpose' }
    & Pick<SchemaTypes.ConsentFeaturePurpose, 'type' | 'featureId'>
  ) }
);

export type ConsentsAndDeclarationsSpecs_MarketingConsentsAndDeclarations_Fragment = (
  { __typename: 'MarketingConsentsAndDeclarations' }
  & Pick<SchemaTypes.MarketingConsentsAndDeclarations, 'isMandatory' | 'defaultChecked' | 'id' | 'type' | 'displayName' | 'moduleId' | 'orderNumber' | 'isActive' | 'purpose' | 'dataField'>
  & { platform: (
    { __typename: 'MarketingPlatform' }
    & MarketingPlatformSpecsFragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ), title?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  )>, description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ), conditions?: SchemaTypes.Maybe<Array<(
    { __typename: 'ApplicationModuleCondition' }
    & ConditionSpecs_ApplicationModuleCondition_Fragment
  ) | (
    { __typename: 'BankCondition' }
    & ConditionSpecs_BankCondition_Fragment
  ) | (
    { __typename: 'ContextualCondition' }
    & ConditionSpecs_ContextualCondition_Fragment
  ) | (
    { __typename: 'DealerCondition' }
    & ConditionSpecs_DealerCondition_Fragment
  ) | (
    { __typename: 'GiftVoucherCondition' }
    & ConditionSpecs_GiftVoucherCondition_Fragment
  ) | (
    { __typename: 'InsurerCondition' }
    & ConditionSpecs_InsurerCondition_Fragment
  ) | (
    { __typename: 'LocationCondition' }
    & ConditionSpecs_LocationCondition_Fragment
  ) | (
    { __typename: 'LogicCondition' }
    & ConditionSpecs_LogicCondition_Fragment
  ) | (
    { __typename: 'SalesOfferAgreementsCondition' }
    & ConditionSpecs_SalesOfferAgreementsCondition_Fragment
  )>>, featurePurpose: (
    { __typename: 'ConsentFeaturePurpose' }
    & Pick<SchemaTypes.ConsentFeaturePurpose, 'type' | 'featureId'>
  ) }
);

export type ConsentsAndDeclarationsSpecs_TextConsentsAndDeclarations_Fragment = (
  { __typename: 'TextConsentsAndDeclarations' }
  & Pick<SchemaTypes.TextConsentsAndDeclarations, 'id' | 'type' | 'displayName' | 'moduleId' | 'orderNumber' | 'isActive' | 'purpose' | 'dataField'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ), title?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  )>, description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ), conditions?: SchemaTypes.Maybe<Array<(
    { __typename: 'ApplicationModuleCondition' }
    & ConditionSpecs_ApplicationModuleCondition_Fragment
  ) | (
    { __typename: 'BankCondition' }
    & ConditionSpecs_BankCondition_Fragment
  ) | (
    { __typename: 'ContextualCondition' }
    & ConditionSpecs_ContextualCondition_Fragment
  ) | (
    { __typename: 'DealerCondition' }
    & ConditionSpecs_DealerCondition_Fragment
  ) | (
    { __typename: 'GiftVoucherCondition' }
    & ConditionSpecs_GiftVoucherCondition_Fragment
  ) | (
    { __typename: 'InsurerCondition' }
    & ConditionSpecs_InsurerCondition_Fragment
  ) | (
    { __typename: 'LocationCondition' }
    & ConditionSpecs_LocationCondition_Fragment
  ) | (
    { __typename: 'LogicCondition' }
    & ConditionSpecs_LogicCondition_Fragment
  ) | (
    { __typename: 'SalesOfferAgreementsCondition' }
    & ConditionSpecs_SalesOfferAgreementsCondition_Fragment
  )>>, featurePurpose: (
    { __typename: 'ConsentFeaturePurpose' }
    & Pick<SchemaTypes.ConsentFeaturePurpose, 'type' | 'featureId'>
  ) }
);

export type ConsentsAndDeclarationsSpecsFragment = ConsentsAndDeclarationsSpecs_CheckboxConsentsAndDeclarations_Fragment | ConsentsAndDeclarationsSpecs_GroupConsentsAndDeclarations_Fragment | ConsentsAndDeclarationsSpecs_MarketingConsentsAndDeclarations_Fragment | ConsentsAndDeclarationsSpecs_TextConsentsAndDeclarations_Fragment;

export const ConsentsAndDeclarationsSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment ConsentsAndDeclarationsSpecs on ConsentsAndDeclarations {
  id
  type
  displayName
  moduleId
  module {
    company {
      timeZone
    }
  }
  title {
    ...TranslatedStringSpecs
  }
  orderNumber
  isActive
  description {
    ...TranslatedStringSpecs
  }
  versioning {
    ...AdvancedVersioningData
  }
  purpose
  dataField
  conditions {
    ...ConditionSpecs
  }
  featurePurpose {
    type
    featureId
  }
  ... on CheckboxConsentsAndDeclarations {
    isMandatory
    legalTextPosition
    legalMarkup {
      ...TranslatedStringSpecs
    }
  }
  ... on MarketingConsentsAndDeclarations {
    isMandatory
    platform {
      ...MarketingPlatformSpecs
    }
    defaultChecked
  }
}
    `;