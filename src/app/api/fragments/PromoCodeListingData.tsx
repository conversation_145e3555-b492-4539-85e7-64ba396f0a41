import type * as SchemaTypes from '../types';

import type { PeriodDataFragment } from './PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import { gql } from '@apollo/client';
import { PeriodDataFragmentDoc } from './PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
export type PromoCodeListingDataFragment = (
  { __typename: 'PromoCode' }
  & Pick<SchemaTypes.PromoCode, 'id' | 'promoCode' | 'isActive'>
  & { period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'displayName'>
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'displayName'>
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'displayName'>
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'displayName'>
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'displayName'>
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'displayName'>
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'displayName'>
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'displayName'>
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'displayName'>
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'displayName'>
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'displayName'>
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'displayName'>
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'displayName'>
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'displayName'>
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'displayName'>
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'displayName'>
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'displayName'>
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'displayName'>
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'displayName'>
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'displayName'>
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'displayName'>
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'displayName'>
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'displayName'>
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'displayName'>
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'displayName'>
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'displayName'>
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'displayName'>
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'displayName'>
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'displayName'>
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'displayName'>
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'displayName'>
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'displayName'>
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'displayName'>
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'displayName'>
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'displayName'>
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'displayName'>
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'displayName'>
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'displayName'>
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'displayName'>
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'displayName'>
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'displayName'>
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'displayName'>
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'displayName'>
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), promoType: (
    { __typename: 'DiscountPromoType' }
    & Pick<SchemaTypes.DiscountPromoType, 'type' | 'amount'>
  ) | (
    { __typename: 'GiftPromoType' }
    & Pick<SchemaTypes.GiftPromoType, 'type'>
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
);

export const PromoCodeListingDataFragmentDoc = /*#__PURE__*/ gql`
    fragment PromoCodeListingData on PromoCode {
  id
  promoCode
  period {
    ...PeriodData
  }
  module {
    displayName
    ...ModulesCompanyTimezoneData
  }
  promoType {
    ... on GiftPromoType {
      type
    }
    ... on DiscountPromoType {
      type
      amount
    }
  }
  isActive
  versioning {
    ...AdvancedVersioningData
  }
}
    `;