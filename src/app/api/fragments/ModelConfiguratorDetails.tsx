import type * as SchemaTypes from '../types';

import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { VariantConfiguratorDetailsFragment } from './VariantConfiguratorDetails';
import type { VehicleCalculatorSpecs_FinderVehicle_Fragment, VehicleCalculatorSpecs_LocalMake_Fragment, VehicleCalculatorSpecs_LocalModel_Fragment, VehicleCalculatorSpecs_LocalVariant_Fragment } from './VehicleCalculatorSpecs';
import type { LocalVariantCalculatorSpecsFragment } from './LocalVariantCalculatorSpecs';
import type { LocalModelCalculatorSpecsFragment } from './LocalModelCalculatorSpecs';
import type { LocalMakeCalculatorSpecsFragment } from './LocalMakeCalculatorSpecs';
import type { FinderVehicleDataFragment } from './FinderVehicleData';
import type { BlockDetails_ColorBlock_Fragment, BlockDetails_OptionsBlock_Fragment, BlockDetails_PackageBlock_Fragment, BlockDetails_TrimBlock_Fragment } from './BlockDetails';
import type { OptionSettingDetails_ComboOptionSettings_Fragment, OptionSettingDetails_DropdownOptionSettings_Fragment, OptionSettingDetails_MultiSelectOptionSettings_Fragment, OptionSettingDetails_SingleSelectOptionSettings_Fragment } from './OptionSettingDetails';
import type { InventoryDetailsPublicData_ConfiguratorInventory_Fragment, InventoryDetailsPublicData_MobilityInventory_Fragment } from './InventoryDetailsPublicData';
import type { StockInventorySpecs_ConfiguratorStockInventory_Fragment, StockInventorySpecs_MobilityStockInventory_Fragment } from './StockInventorySpecs';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from './ApplicationStageData';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from './ReferenceApplicationData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { TradeInVehicleDataFragment } from './TradeInVehicleData';
import type { CompanyPublicSpecsFragment } from './CompanyPublicSpecs';
import type { PeriodDataFragment } from './PeriodData';
import type { StockBlockingPeriodDataFragment } from './StockBlockingPeriod';
import type { ConfiguratorInventoryPublicSpecsFragment } from './ConfiguratorInventoryPublicSpecs';
import type { MobilityInventoryPublicSpecsFragment } from './MobilityInventoryPublicSpecs';
import type { MobilityModuleSpecsFragment } from './MobilityModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerBookingCodeSpecsFragment } from './DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from './MobilitySigningSettingSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { KycPresetsOptionsDataFragment } from './KYCPresetsOptionsData';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from './MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from './MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from './MobilityEmailContentData';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { MobilityOperatorEmailContentDataFragment } from './MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from './MobilityHomeDeliveryData';
import type { CounterSettingsSpecsFragment } from './CounterSettingsSpecs';
import { gql } from '@apollo/client';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { VariantConfiguratorDetailsFragmentDoc } from './VariantConfiguratorDetails';
import { VehicleCalculatorSpecsFragmentDoc } from './VehicleCalculatorSpecs';
import { LocalVariantCalculatorSpecsFragmentDoc } from './LocalVariantCalculatorSpecs';
import { LocalModelCalculatorSpecsFragmentDoc } from './LocalModelCalculatorSpecs';
import { LocalMakeCalculatorSpecsFragmentDoc } from './LocalMakeCalculatorSpecs';
import { FinderVehicleDataFragmentDoc } from './FinderVehicleData';
import { BlockDetailsFragmentDoc } from './BlockDetails';
import { OptionSettingDetailsFragmentDoc } from './OptionSettingDetails';
import { InventoryDetailsPublicDataFragmentDoc } from './InventoryDetailsPublicData';
import { StockInventorySpecsFragmentDoc } from './StockInventorySpecs';
import { ApplicationStageDataFragmentDoc } from './ApplicationStageData';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from './ReferenceApplicationData';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { TradeInVehicleDataFragmentDoc } from './TradeInVehicleData';
import { CompanyPublicSpecsFragmentDoc } from './CompanyPublicSpecs';
import { PeriodDataFragmentDoc } from './PeriodData';
import { StockBlockingPeriodDataFragmentDoc } from './StockBlockingPeriod';
import { ConfiguratorInventoryPublicSpecsFragmentDoc } from './ConfiguratorInventoryPublicSpecs';
import { MobilityInventoryPublicSpecsFragmentDoc } from './MobilityInventoryPublicSpecs';
import { MobilityModuleSpecsFragmentDoc } from './MobilityModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerBookingCodeSpecsFragmentDoc } from './DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from './MobilitySigningSettingSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { KycPresetsOptionsDataFragmentDoc } from './KYCPresetsOptionsData';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from './MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from './MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from './MobilityEmailContentData';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { MobilityOperatorEmailContentDataFragmentDoc } from './MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from './MobilityHomeDeliveryData';
import { CounterSettingsSpecsFragmentDoc } from './CounterSettingsSpecs';
export type ModelConfiguratorDetailsFragment = (
  { __typename: 'ModelConfigurator' }
  & Pick<SchemaTypes.ModelConfigurator, 'id' | 'externalUrl' | 'bannerTextPosition' | 'urlIdentifier' | 'variantConfiguratorIds'>
  & { banner?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFile' }
    & UploadFileFormDataFragment
  )>, mobileBanner?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFile' }
    & UploadFileFormDataFragment
  )>, descriptionImages: Array<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, model: (
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ), module: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName' | 'isInventoryEnabled'>
  ) | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, bannerText?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, pageTitle: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), pageSubtitle: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), pageDescription: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), configuratorTitle: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), variantConfigurators: Array<(
    { __typename: 'VariantConfigurator' }
    & VariantConfiguratorDetailsFragment
  )> }
);

export const ModelConfiguratorDetailsFragmentDoc = /*#__PURE__*/ gql`
    fragment ModelConfiguratorDetails on ModelConfigurator {
  id
  externalUrl
  banner {
    ...UploadFileFormData
  }
  mobileBanner {
    ...UploadFileFormData
  }
  descriptionImages {
    ...UploadFileWithPreviewFormData
  }
  model {
    id
    name {
      ...TranslatedStringData
    }
  }
  module {
    ... on ConfiguratorModule {
      id
      displayName
      isInventoryEnabled
    }
  }
  bannerText {
    ...TranslatedStringData
  }
  bannerTextPosition
  pageTitle {
    ...TranslatedStringData
  }
  pageSubtitle {
    ...TranslatedStringData
  }
  pageDescription {
    ...TranslatedStringData
  }
  configuratorTitle {
    ...TranslatedStringData
  }
  urlIdentifier
  variantConfiguratorIds
  variantConfigurators {
    ...VariantConfiguratorDetails
  }
}
    `;