import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { BankDetailsDataFragment } from './BankDetailsData';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from './BankIntegrationData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from './FinanceProductDetailsData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from './PeriodData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { BankDetailsDataFragmentDoc } from './BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { BankIntegrationDataFragmentDoc } from './BankIntegrationData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from './FinanceProductDetailsData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
export type LocalDeferredPrincipalDetailsFragment = (
  { __typename: 'LocalDeferredPrincipal' }
  & Pick<SchemaTypes.LocalDeferredPrincipal, 'id' | 'identifier' | 'type' | 'basedOn' | 'order' | 'bankId' | 'webCalcSettingId' | 'moduleId' | 'variantSuiteIds' | 'deriveMethod' | 'calculationMode'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, bank: (
    { __typename: 'SystemBank' }
    & BankDetailsDataFragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), term: (
    { __typename: 'DeferredPrincipalTermSettings' }
    & TermSettingsDetails_DeferredPrincipalTermSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export const LocalDeferredPrincipalDetailsFragmentDoc = /*#__PURE__*/ gql`
    fragment LocalDeferredPrincipalDetails on LocalDeferredPrincipal {
  id
  identifier
  type
  basedOn
  order
  legalName {
    ...TranslatedStringData
  }
  description {
    ...TranslatedStringData
  }
  bankId
  webCalcSettingId
  bank {
    ...BankDetailsData
  }
  moduleId
  module {
    ...ModulesCompanyTimezoneData
  }
  variantSuiteIds
  vehicleReferenceParameters {
    ...VehicleReferenceParametersData
  }
  term {
    ...TermSettingsDetails
  }
  downPayment {
    ...DownPaymentSettingsDetails
  }
  loan {
    ...LoanSettingsDetails
  }
  interestRate {
    ...InterestRateSettingsDetails
  }
  payment {
    ...PaymentSettingsDetails
  }
  deriveMethod
  calculationMode
  versioning {
    isLatest
    suiteId
  }
}
    `;