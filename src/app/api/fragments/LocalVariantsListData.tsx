import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
export type LocalVariantsListDataFragment = (
  { __typename: 'LocalVariant' }
  & Pick<SchemaTypes.LocalVariant, 'id' | 'identifier' | 'order' | 'isActive' | 'vehiclePrice' | 'modelId' | 'bodyType' | 'hasVariantImage'>
  & { name: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), model: (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
    ) }
  ), submodel?: SchemaTypes.Maybe<(
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'suiteId' | 'isLatest'>
  ) }
);

export const LocalVariantsListDataFragmentDoc = /*#__PURE__*/ gql`
    fragment LocalVariantsListData on LocalVariant {
  id
  identifier
  order
  isActive
  vehiclePrice
  name {
    ...TranslatedStringData
  }
  modelId
  model {
    identifier
    name {
      ...TranslatedStringData
    }
  }
  module {
    id
    company {
      id
      displayName
      currency
    }
  }
  submodel {
    identifier
    name {
      ...TranslatedStringData
    }
  }
  bodyType
  hasVariantImage
  versioning {
    suiteId
    isLatest
  }
}
    `;