import type * as SchemaTypes from '../types';

import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { ConfiguratorJourneyBlocksData_ApplicationConfiguratorColorSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorOptionSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorPackageSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorTrimSetting_Fragment } from './ConfiguratorJourneyBlocksData';
import type { BlockDetails_ColorBlock_Fragment, BlockDetails_OptionsBlock_Fragment, BlockDetails_PackageBlock_Fragment, BlockDetails_TrimBlock_Fragment } from './BlockDetails';
import type { OptionSettingDetails_ComboOptionSettings_Fragment, OptionSettingDetails_DropdownOptionSettings_Fragment, OptionSettingDetails_MultiSelectOptionSettings_Fragment, OptionSettingDetails_SingleSelectOptionSettings_Fragment } from './OptionSettingDetails';
import { gql } from '@apollo/client';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { ConfiguratorJourneyBlocksDataFragmentDoc } from './ConfiguratorJourneyBlocksData';
import { BlockDetailsFragmentDoc } from './BlockDetails';
import { OptionSettingDetailsFragmentDoc } from './OptionSettingDetails';
export type ConfiguratorLeadDataFragment = (
  { __typename: 'ConfiguratorLead' }
  & Pick<SchemaTypes.ConfiguratorLead, 'mergedToLeadSuiteId' | 'mergedToLeadIdentifier'>
  & { vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & VehicleSpecs_FinderVehicle_Fragment
  ) | (
    { __typename: 'LocalMake' }
    & VehicleSpecs_LocalMake_Fragment
  ) | (
    { __typename: 'LocalModel' }
    & VehicleSpecs_LocalModel_Fragment
  ) | (
    { __typename: 'LocalVariant' }
    & VehicleSpecs_LocalVariant_Fragment
  )>, configuratorBlocks: Array<(
    { __typename: 'ApplicationConfiguratorColorSetting' }
    & ConfiguratorJourneyBlocksData_ApplicationConfiguratorColorSetting_Fragment
  ) | (
    { __typename: 'ApplicationConfiguratorOptionSetting' }
    & ConfiguratorJourneyBlocksData_ApplicationConfiguratorOptionSetting_Fragment
  ) | (
    { __typename: 'ApplicationConfiguratorPackageSetting' }
    & ConfiguratorJourneyBlocksData_ApplicationConfiguratorPackageSetting_Fragment
  ) | (
    { __typename: 'ApplicationConfiguratorTrimSetting' }
    & ConfiguratorJourneyBlocksData_ApplicationConfiguratorTrimSetting_Fragment
  )>, configurator: (
    { __typename: 'VariantConfigurator' }
    & { blocks: Array<(
      { __typename: 'ColorBlock' }
      & BlockDetails_ColorBlock_Fragment
    ) | (
      { __typename: 'OptionsBlock' }
      & BlockDetails_OptionsBlock_Fragment
    ) | (
      { __typename: 'PackageBlock' }
      & BlockDetails_PackageBlock_Fragment
    ) | (
      { __typename: 'TrimBlock' }
      & BlockDetails_TrimBlock_Fragment
    )> }
  ) }
);

export const ConfiguratorLeadDataFragmentDoc = /*#__PURE__*/ gql`
    fragment ConfiguratorLeadData on ConfiguratorLead {
  vehicle {
    ...VehicleSpecs
  }
  configuratorBlocks {
    ...ConfiguratorJourneyBlocksData
  }
  configurator {
    blocks {
      ...BlockDetails
    }
  }
  mergedToLeadSuiteId
  mergedToLeadIdentifier
}
    `;