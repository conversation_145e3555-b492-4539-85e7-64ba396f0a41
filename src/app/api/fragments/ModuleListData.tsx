import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type ModuleListData_AdyenPaymentModule_Fragment = (
  { __typename: 'AdyenPaymentModule' }
  & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_AppointmentModule_Fragment = (
  { __typename: 'AppointmentModule' }
  & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_AutoplayModule_Fragment = (
  { __typename: 'AutoplayModule' }
  & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_BankModule_Fragment = (
  { __typename: 'BankModule' }
  & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_BasicSigningModule_Fragment = (
  { __typename: 'BasicSigningModule' }
  & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_CapModule_Fragment = (
  { __typename: 'CapModule' }
  & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_ConfiguratorModule_Fragment = (
  { __typename: 'ConfiguratorModule' }
  & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_ConsentsAndDeclarationsModule_Fragment = (
  { __typename: 'ConsentsAndDeclarationsModule' }
  & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_CtsModule_Fragment = (
  { __typename: 'CtsModule' }
  & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_DocusignModule_Fragment = (
  { __typename: 'DocusignModule' }
  & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_EventApplicationModule_Fragment = (
  { __typename: 'EventApplicationModule' }
  & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_FinderApplicationPrivateModule_Fragment = (
  { __typename: 'FinderApplicationPrivateModule' }
  & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_FinderApplicationPublicModule_Fragment = (
  { __typename: 'FinderApplicationPublicModule' }
  & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_FinderVehicleManagementModule_Fragment = (
  { __typename: 'FinderVehicleManagementModule' }
  & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_FiservPaymentModule_Fragment = (
  { __typename: 'FiservPaymentModule' }
  & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_GiftVoucherModule_Fragment = (
  { __typename: 'GiftVoucherModule' }
  & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_InsuranceModule_Fragment = (
  { __typename: 'InsuranceModule' }
  & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_LabelsModule_Fragment = (
  { __typename: 'LabelsModule' }
  & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_LaunchPadModule_Fragment = (
  { __typename: 'LaunchPadModule' }
  & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_LocalCustomerManagementModule_Fragment = (
  { __typename: 'LocalCustomerManagementModule' }
  & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_MaintenanceModule_Fragment = (
  { __typename: 'MaintenanceModule' }
  & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_MarketingModule_Fragment = (
  { __typename: 'MarketingModule' }
  & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_MobilityModule_Fragment = (
  { __typename: 'MobilityModule' }
  & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_MyInfoModule_Fragment = (
  { __typename: 'MyInfoModule' }
  & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_NamirialSigningModule_Fragment = (
  { __typename: 'NamirialSigningModule' }
  & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_OidcModule_Fragment = (
  { __typename: 'OIDCModule' }
  & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_PayGatePaymentModule_Fragment = (
  { __typename: 'PayGatePaymentModule' }
  & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_PorscheIdModule_Fragment = (
  { __typename: 'PorscheIdModule' }
  & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_PorscheMasterDataModule_Fragment = (
  { __typename: 'PorscheMasterDataModule' }
  & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_PorschePaymentModule_Fragment = (
  { __typename: 'PorschePaymentModule' }
  & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_PorscheRetainModule_Fragment = (
  { __typename: 'PorscheRetainModule' }
  & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_PromoCodeModule_Fragment = (
  { __typename: 'PromoCodeModule' }
  & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_SalesControlBoardModule_Fragment = (
  { __typename: 'SalesControlBoardModule' }
  & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_SalesOfferModule_Fragment = (
  { __typename: 'SalesOfferModule' }
  & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_SimpleVehicleManagementModule_Fragment = (
  { __typename: 'SimpleVehicleManagementModule' }
  & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_StandardApplicationModule_Fragment = (
  { __typename: 'StandardApplicationModule' }
  & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_TradeInModule_Fragment = (
  { __typename: 'TradeInModule' }
  & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_TtbPaymentModule_Fragment = (
  { __typename: 'TtbPaymentModule' }
  & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_UserlikeChatbotModule_Fragment = (
  { __typename: 'UserlikeChatbotModule' }
  & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_VehicleDataWithPorscheCodeIntegrationModule_Fragment = (
  { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
  & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_VisitAppointmentModule_Fragment = (
  { __typename: 'VisitAppointmentModule' }
  & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_WebsiteModule_Fragment = (
  { __typename: 'WebsiteModule' }
  & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListData_WhatsappLiveChatModule_Fragment = (
  { __typename: 'WhatsappLiveChatModule' }
  & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ) }
);

export type ModuleListDataFragment = ModuleListData_AdyenPaymentModule_Fragment | ModuleListData_AppointmentModule_Fragment | ModuleListData_AutoplayModule_Fragment | ModuleListData_BankModule_Fragment | ModuleListData_BasicSigningModule_Fragment | ModuleListData_CapModule_Fragment | ModuleListData_ConfiguratorModule_Fragment | ModuleListData_ConsentsAndDeclarationsModule_Fragment | ModuleListData_CtsModule_Fragment | ModuleListData_DocusignModule_Fragment | ModuleListData_EventApplicationModule_Fragment | ModuleListData_FinderApplicationPrivateModule_Fragment | ModuleListData_FinderApplicationPublicModule_Fragment | ModuleListData_FinderVehicleManagementModule_Fragment | ModuleListData_FiservPaymentModule_Fragment | ModuleListData_GiftVoucherModule_Fragment | ModuleListData_InsuranceModule_Fragment | ModuleListData_LabelsModule_Fragment | ModuleListData_LaunchPadModule_Fragment | ModuleListData_LocalCustomerManagementModule_Fragment | ModuleListData_MaintenanceModule_Fragment | ModuleListData_MarketingModule_Fragment | ModuleListData_MobilityModule_Fragment | ModuleListData_MyInfoModule_Fragment | ModuleListData_NamirialSigningModule_Fragment | ModuleListData_OidcModule_Fragment | ModuleListData_PayGatePaymentModule_Fragment | ModuleListData_PorscheIdModule_Fragment | ModuleListData_PorscheMasterDataModule_Fragment | ModuleListData_PorschePaymentModule_Fragment | ModuleListData_PorscheRetainModule_Fragment | ModuleListData_PromoCodeModule_Fragment | ModuleListData_SalesControlBoardModule_Fragment | ModuleListData_SalesOfferModule_Fragment | ModuleListData_SimpleVehicleManagementModule_Fragment | ModuleListData_StandardApplicationModule_Fragment | ModuleListData_TradeInModule_Fragment | ModuleListData_TtbPaymentModule_Fragment | ModuleListData_UserlikeChatbotModule_Fragment | ModuleListData_VehicleDataWithPorscheCodeIntegrationModule_Fragment | ModuleListData_VisitAppointmentModule_Fragment | ModuleListData_WebsiteModule_Fragment | ModuleListData_WhatsappLiveChatModule_Fragment;

export const ModuleListDataFragmentDoc = /*#__PURE__*/ gql`
    fragment ModuleListData on Module {
  id
  displayName
  company {
    id
    displayName
  }
}
    `;