import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { PeriodDataFragment } from './PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
export type FinanceProductListData_LocalDeferredPrincipal_Fragment = (
  { __typename: 'LocalDeferredPrincipal' }
  & Pick<SchemaTypes.LocalDeferredPrincipal, 'id' | 'identifier' | 'displayName' | 'type' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'allVariantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'displayName' | 'moduleId'>
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type FinanceProductListData_LocalHirePurchase_Fragment = (
  { __typename: 'LocalHirePurchase' }
  & Pick<SchemaTypes.LocalHirePurchase, 'id' | 'identifier' | 'displayName' | 'type' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'allVariantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'displayName' | 'moduleId'>
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment = (
  { __typename: 'LocalHirePurchaseWithBalloon' }
  & Pick<SchemaTypes.LocalHirePurchaseWithBalloon, 'id' | 'identifier' | 'displayName' | 'type' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'allVariantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), balloon: (
    { __typename: 'BalloonRangeSettings' }
    & BalloonSettingsDetails_BalloonRangeSettings_Fragment
  ) | (
    { __typename: 'BalloonTableSettings' }
    & BalloonSettingsDetails_BalloonTableSettings_Fragment
  ), bank: (
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'displayName' | 'moduleId'>
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment = (
  { __typename: 'LocalHirePurchaseWithBalloonGFV' }
  & Pick<SchemaTypes.LocalHirePurchaseWithBalloonGfv, 'id' | 'identifier' | 'displayName' | 'type' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'allVariantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), balloon: (
    { __typename: 'BalloonGFVSettings' }
    & BalloonGfvSettingsDetailsFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'displayName' | 'moduleId'>
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type FinanceProductListData_LocalLease_Fragment = (
  { __typename: 'LocalLease' }
  & Pick<SchemaTypes.LocalLease, 'id' | 'identifier' | 'displayName' | 'type' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'allVariantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'displayName' | 'moduleId'>
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type FinanceProductListData_LocalLeasePurchase_Fragment = (
  { __typename: 'LocalLeasePurchase' }
  & Pick<SchemaTypes.LocalLeasePurchase, 'id' | 'identifier' | 'displayName' | 'type' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'allVariantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'displayName' | 'moduleId'>
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type FinanceProductListData_LocalUcclLeasing_Fragment = (
  { __typename: 'LocalUcclLeasing' }
  & Pick<SchemaTypes.LocalUcclLeasing, 'id' | 'identifier' | 'displayName' | 'type' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'allVariantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'displayName' | 'moduleId'>
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type FinanceProductListDataFragment = FinanceProductListData_LocalDeferredPrincipal_Fragment | FinanceProductListData_LocalHirePurchase_Fragment | FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment | FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment | FinanceProductListData_LocalLease_Fragment | FinanceProductListData_LocalLeasePurchase_Fragment | FinanceProductListData_LocalUcclLeasing_Fragment;

export const FinanceProductListDataFragmentDoc = /*#__PURE__*/ gql`
    fragment FinanceProductListData on FinanceProduct {
  ... on LocalHirePurchase {
    id
    identifier
    displayName
    type
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    bank {
      id
      displayName
      moduleId
    }
    period {
      ...PeriodData
    }
    module {
      ...ModulesCompanyTimezoneData
    }
    order
    isActive
    allVariantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalHirePurchaseWithBalloon {
    id
    identifier
    displayName
    type
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    balloon {
      ...BalloonSettingsDetails
    }
    bank {
      id
      displayName
      moduleId
    }
    period {
      ...PeriodData
    }
    module {
      ...ModulesCompanyTimezoneData
    }
    order
    isActive
    allVariantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalHirePurchaseWithBalloonGFV {
    id
    identifier
    displayName
    type
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    balloon {
      ...BalloonGFVSettingsDetails
    }
    bank {
      id
      displayName
      moduleId
    }
    period {
      ...PeriodData
    }
    module {
      ...ModulesCompanyTimezoneData
    }
    order
    isActive
    allVariantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalLease {
    id
    identifier
    displayName
    type
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    bank {
      id
      displayName
      moduleId
    }
    period {
      ...PeriodData
    }
    module {
      ...ModulesCompanyTimezoneData
    }
    order
    isActive
    allVariantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalLeasePurchase {
    id
    identifier
    displayName
    type
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    bank {
      id
      displayName
      moduleId
    }
    period {
      ...PeriodData
    }
    module {
      ...ModulesCompanyTimezoneData
    }
    order
    isActive
    allVariantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalDeferredPrincipal {
    id
    identifier
    displayName
    type
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    bank {
      id
      displayName
      moduleId
    }
    period {
      ...PeriodData
    }
    module {
      ...ModulesCompanyTimezoneData
    }
    order
    isActive
    allVariantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalUcclLeasing {
    id
    identifier
    displayName
    type
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    bank {
      id
      displayName
      moduleId
    }
    period {
      ...PeriodData
    }
    module {
      ...ModulesCompanyTimezoneData
    }
    order
    isActive
    allVariantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    versioning {
      isLatest
      suiteId
    }
  }
}
    `;