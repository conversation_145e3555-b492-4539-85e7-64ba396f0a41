/* eslint-disable */

export type { StatisticNameCounterFragment } from "./statisticNameCounter";

export { StatisticNameCounterFragmentDoc } from "./statisticNameCounter";

export type { StatisticNameCountResultFragment } from "./statisticNameCountResult";

export { StatisticNameCountResultFragmentDoc } from "./statisticNameCountResult";

export type { StatisticNameAmountResultFragment } from "./statisticNameAmountResult";

export { StatisticNameAmountResultFragmentDoc } from "./statisticNameAmountResult";

export type { StatisticNameAmountNumberFragment } from "./statisticNameAmountNumber";

export { StatisticNameAmountNumberFragmentDoc } from "./statisticNameAmountNumber";

export type { StatisticDateCounterFragment } from "./statisticDateCounter";

export { StatisticDateCounterFragmentDoc } from "./statisticDateCounter";

export type { StatisticDateCountResultFragment } from "./statisticDateCountResult";

export { StatisticDateCountResultFragmentDoc } from "./statisticDateCountResult";

export type { StatisticDateAmountResultFragment } from "./statisticDateAmountResult";

export { StatisticDateAmountResultFragmentDoc } from "./statisticDateAmountResult";

export type { StatisticDateAmountFragment } from "./statisticDateAmount";

export { StatisticDateAmountFragmentDoc } from "./statisticDateAmount";

export type {
  FullListingValueFragment,
  FormattedDateDataFragment,
  LocalizedStringDataFragment,
  CurrencyDataFragment,
  LocalizedValueDataFragment,
  NumberUnitDataFragment,
} from "./finderListing.fragment";

export {
  FormattedDateDataFragmentDoc,
  LocalizedStringDataFragmentDoc,
  LocalizedValueDataFragmentDoc,
  NumberUnitDataFragmentDoc,
  FullListingValueFragmentDoc,
  CurrencyDataFragmentDoc,
} from "./finderListing.fragment";

export type { WhatsappLiveChatSettingsSpecFragment } from "./WhatsappLiveChatSettingsSpec";

export { WhatsappLiveChatSettingsSpecFragmentDoc } from "./WhatsappLiveChatSettingsSpec";

export type { WhatsappLiveChatModuleWithPermissionsSpecsFragment } from "./WhatsappLiveChatModuleWithPermissionsSpecs";

export { WhatsappLiveChatModuleWithPermissionsSpecsFragmentDoc } from "./WhatsappLiveChatModuleWithPermissionsSpecs";

export type { WhatsappLiveChatModuleSpecsFragment } from "./WhatsappLiveChatModuleSpecs";

export { WhatsappLiveChatModuleSpecsFragmentDoc } from "./WhatsappLiveChatModuleSpecs";

export type { WebsiteModuleWithPermissionsSpecsFragment } from "./WebsiteModuleWithPermissionsSpecs";

export { WebsiteModuleWithPermissionsSpecsFragmentDoc } from "./WebsiteModuleWithPermissionsSpecs";

export type { WebsiteModuleSpecsFragment } from "./WebsiteModuleSpecs";

export { WebsiteModuleSpecsFragmentDoc } from "./WebsiteModuleSpecs";

export type { WebsiteModulePublicSpecsFragment } from "./WebsiteModulePublicSpecs";

export { WebsiteModulePublicSpecsFragmentDoc } from "./WebsiteModulePublicSpecs";

export type { WebpageEndpointContextDataFragment } from "./WebpageEndpointContextData";

export { WebpageEndpointContextDataFragmentDoc } from "./WebpageEndpointContextData";

export type { WebPagePathDataFragment } from "./WebPagePathData";

export { WebPagePathDataFragmentDoc } from "./WebPagePathData";

export type { WebPageOptionDataFragment } from "./WebPageOptionData";

export { WebPageOptionDataFragmentDoc } from "./WebPageOptionData";

export type { WebPageListDataFragment } from "./WebPageListData";

export { WebPageListDataFragmentDoc } from "./WebPageListData";

export type { WebPageEndpointSpecsFragment } from "./WebPageEndpointSpecs";

export { WebPageEndpointSpecsFragmentDoc } from "./WebPageEndpointSpecs";

export type { WebPageDataFragment } from "./WebPageData";

export { WebPageDataFragmentDoc } from "./WebPageData";

export type { WebPageButtonDataFragment } from "./WebPageButtonData";

export { WebPageButtonDataFragmentDoc } from "./WebPageButtonData";

export type {
  WebPageBlockData_ColumnWebPageBlock_Fragment,
  WebPageBlockData_CustomWebPageBlock_Fragment,
  WebPageBlockData_ImageWebPageBlock_Fragment,
  WebPageBlockData_TextCarouselWebPageBlock_Fragment,
  WebPageBlockData_TextImageWebPageBlock_Fragment,
  WebPageBlockDataFragment,
} from "./WebPageBlockData";

export { WebPageBlockDataFragmentDoc } from "./WebPageBlockData";

export type { WebCalcSettingDataFragment } from "./WebCalcSettingData";

export { WebCalcSettingDataFragmentDoc } from "./WebCalcSettingData";

export type { VisitAppointmentModuleWithPermissionsSpecsFragment } from "./VisitAppointmentModuleWithPermissionsSpecs";

export { VisitAppointmentModuleWithPermissionsSpecsFragmentDoc } from "./VisitAppointmentModuleWithPermissionsSpecs";

export type { VisitAppointmentModuleSpecsFragment } from "./VisitAppointmentModuleSpecs";

export { VisitAppointmentModuleSpecsFragmentDoc } from "./VisitAppointmentModuleSpecs";

export type { VisitAppointmentModuleInDealerSpecsFragment } from "./VisitAppointmentModuleInDealerSpecs";

export { VisitAppointmentModuleInDealerSpecsFragmentDoc } from "./VisitAppointmentModuleInDealerSpecs";

export type {
  VisitAppointmentModuleEmailContentSpecsFragment,
  VisitAppointmentModuleEmailContentCustomerSpecsFragment,
  VisitAppointmentModuleEmailContentSalesPersonSpecsFragment,
  VisitAppointmentModuleEmailContentsSpecsFragment,
} from "./VisitAppointmentModuleEmailContentsSpecs";

export {
  VisitAppointmentModuleEmailContentSpecsFragmentDoc,
  VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc,
  VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc,
  VisitAppointmentModuleEmailContentsSpecsFragmentDoc,
} from "./VisitAppointmentModuleEmailContentsSpecs";

export type { VisitAppointmentModuleApplicationJourneyFragment } from "./VisitAppointmentModuleApplicationJourney";

export { VisitAppointmentModuleApplicationJourneyFragmentDoc } from "./VisitAppointmentModuleApplicationJourney";

export type { VehiclesListByParametersSpecsFragment } from "./VehiclesListByParameters";

export { VehiclesListByParametersSpecsFragmentDoc } from "./VehiclesListByParameters";

export type {
  VehicleWithPermissionsSpecs_FinderVehicle_Fragment,
  VehicleWithPermissionsSpecs_LocalMake_Fragment,
  VehicleWithPermissionsSpecs_LocalModel_Fragment,
  VehicleWithPermissionsSpecs_LocalVariant_Fragment,
  VehicleWithPermissionsSpecsFragment,
} from "./VehicleWithPermissionsSpecs";

export { VehicleWithPermissionsSpecsFragmentDoc } from "./VehicleWithPermissionsSpecs";

export type {
  VehicleSpecs_FinderVehicle_Fragment,
  VehicleSpecs_LocalMake_Fragment,
  VehicleSpecs_LocalModel_Fragment,
  VehicleSpecs_LocalVariant_Fragment,
  VehicleSpecsFragment,
} from "./VehicleSpecs";

export { VehicleSpecsFragmentDoc } from "./VehicleSpecs";

export type { VehicleSalesOfferSpecsFragment } from "./VehicleSalesOfferSpecs";

export { VehicleSalesOfferSpecsFragmentDoc } from "./VehicleSalesOfferSpecs";

export type { VehicleReferenceParametersDataFragment } from "./VehicleReferenceParametersData";

export { VehicleReferenceParametersDataFragmentDoc } from "./VehicleReferenceParametersData";

export type {
  VehicleOptionsData_FinderVehicle_Fragment,
  VehicleOptionsData_LocalMake_Fragment,
  VehicleOptionsData_LocalModel_Fragment,
  VehicleOptionsData_LocalVariant_Fragment,
  VehicleOptionsDataFragment,
} from "./VehicleOptionsData";

export { VehicleOptionsDataFragmentDoc } from "./VehicleOptionsData";

export type {
  VehicleListData_FinderVehicle_Fragment,
  VehicleListData_LocalMake_Fragment,
  VehicleListData_LocalModel_Fragment,
  VehicleListData_LocalVariant_Fragment,
  VehicleListDataFragment,
} from "./VehicleListData";

export { VehicleListDataFragmentDoc } from "./VehicleListData";

export type {
  VehicleDetails_FinderVehicle_Fragment,
  VehicleDetails_LocalMake_Fragment,
  VehicleDetails_LocalModel_Fragment,
  VehicleDetails_LocalVariant_Fragment,
  VehicleDetailsFragment,
} from "./VehicleDetails";

export { VehicleDetailsFragmentDoc } from "./VehicleDetails";

export type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from "./VehicleDataWithPorscheCodeIntegrationSettingSpecs";

export { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from "./VehicleDataWithPorscheCodeIntegrationSettingSpecs";

export type { VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragment } from "./VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecs";

export { VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragmentDoc } from "./VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecs";

export type { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragment } from "./VehicleDataWithPorscheCodeIntegrationModuleSpecs";

export { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragmentDoc } from "./VehicleDataWithPorscheCodeIntegrationModuleSpecs";

export type {
  VehicleCalculatorSpecs_FinderVehicle_Fragment,
  VehicleCalculatorSpecs_LocalMake_Fragment,
  VehicleCalculatorSpecs_LocalModel_Fragment,
  VehicleCalculatorSpecs_LocalVariant_Fragment,
  VehicleCalculatorSpecsFragment,
} from "./VehicleCalculatorSpecs";

export { VehicleCalculatorSpecsFragmentDoc } from "./VehicleCalculatorSpecs";

export type { VariantConfiguratorWithPermissionsSpecsFragment } from "./VariantConfiguratorWithPermissionsSpecs";

export { VariantConfiguratorWithPermissionsSpecsFragmentDoc } from "./VariantConfiguratorWithPermissionsSpecs";

export type { VariantConfiguratorSpecsFragment } from "./VariantConfiguratorSpecs";

export { VariantConfiguratorSpecsFragmentDoc } from "./VariantConfiguratorSpecs";

export type { VariantConfiguratorListItemFragment } from "./VariantConfiguratorListItem";

export { VariantConfiguratorListItemFragmentDoc } from "./VariantConfiguratorListItem";

export type { VariantConfiguratorJourneyDataFragment } from "./VariantConfiguratorJourneyData";

export { VariantConfiguratorJourneyDataFragmentDoc } from "./VariantConfiguratorJourneyData";

export type { VariantConfiguratorDetailsFragment } from "./VariantConfiguratorDetails";

export { VariantConfiguratorDetailsFragmentDoc } from "./VariantConfiguratorDetails";

export type { VsaSalesOfferSpecsFragment } from "./VSASalesOfferSpecs";

export { VsaSalesOfferSpecsFragmentDoc } from "./VSASalesOfferSpecs";

export type { UsersOptionsDataFragment } from "./UsersOptionsData";

export { UsersOptionsDataFragmentDoc } from "./UsersOptionsData";

export type { UserlikeChatbotSettingsSpecFragment } from "./UserlikeChatbotSettingsSpec";

export { UserlikeChatbotSettingsSpecFragmentDoc } from "./UserlikeChatbotSettingsSpec";

export type { UserlikeChatbotModuleWithPermissionsSpecsFragment } from "./UserlikeChatbotModuleWithPermissionsSpecs";

export { UserlikeChatbotModuleWithPermissionsSpecsFragmentDoc } from "./UserlikeChatbotModuleWithPermissionsSpecs";

export type { UserlikeChatbotModuleSpecsFragment } from "./UserlikeChatbotModuleSpecs";

export { UserlikeChatbotModuleSpecsFragmentDoc } from "./UserlikeChatbotModuleSpecs";

export type { UserSpecsFragment } from "./UserSpecs";

export { UserSpecsFragmentDoc } from "./UserSpecs";

export type { UserPreviewDataFragment } from "./UserPreviewData";

export { UserPreviewDataFragmentDoc } from "./UserPreviewData";

export type { UserListDataFragment } from "./UserListData";

export { UserListDataFragmentDoc } from "./UserListData";

export type { UserGroupSpecsFragment } from "./UserGroupSpecs";

export { UserGroupSpecsFragmentDoc } from "./UserGroupSpecs";

export type { UserGroupOptionsDataFragment } from "./UserGroupOptionsData";

export { UserGroupOptionsDataFragmentDoc } from "./UserGroupOptionsData";

export type { UserGroupListDataFragment } from "./UserGroupListData";

export { UserGroupListDataFragmentDoc } from "./UserGroupListData";

export type { UserFullDataFragment } from "./UserFullData";

export { UserFullDataFragmentDoc } from "./UserFullData";

export type { UserCompanyContextDataFragment } from "./UserCompanyContextData";

export { UserCompanyContextDataFragmentDoc } from "./UserCompanyContextData";

export type { UserAvatarSpecsFragment } from "./UserAvatarSpecs";

export { UserAvatarSpecsFragmentDoc } from "./UserAvatarSpecs";

export type { UploadedVariantAssetDataFragment } from "./UploadedVariantAssetData";

export { UploadedVariantAssetDataFragmentDoc } from "./UploadedVariantAssetData";

export type { UploadFileWithPreviewFormDataFragment } from "./UploadFileWithPreviewFormData";

export { UploadFileWithPreviewFormDataFragmentDoc } from "./UploadFileWithPreviewFormData";

export type { UploadFileFormDataFragment } from "./UploadFileFormData";

export { UploadFileFormDataFragmentDoc } from "./UploadFileFormData";

export type { TtbPaymentSettingsSpecFragment } from "./TtbPaymentSettingsSpec";

export { TtbPaymentSettingsSpecFragmentDoc } from "./TtbPaymentSettingsSpec";

export type { TtbPaymentModuleWithPermissionsSpecsFragment } from "./TtbPaymentModuleWithPermissionsSpecs";

export { TtbPaymentModuleWithPermissionsSpecsFragmentDoc } from "./TtbPaymentModuleWithPermissionsSpecs";

export type { TtbPaymentModuleSpecsFragment } from "./TtbPaymentModuleSpecs";

export { TtbPaymentModuleSpecsFragmentDoc } from "./TtbPaymentModuleSpecs";

export type { TrimBlockSpecsFragment } from "./TrimBlockSpecs";

export { TrimBlockSpecsFragmentDoc } from "./TrimBlockSpecs";

export type { TranslatedTextDataFragment } from "./TranslationTextData";

export { TranslatedTextDataFragmentDoc } from "./TranslationTextData";

export type { TranslatedStringSpecsFragment } from "./TranslatedStringSpecs";

export { TranslatedStringSpecsFragmentDoc } from "./TranslatedStringSpecs";

export type { TranslatedStringDataFragment } from "./TranslatedStringData";

export { TranslatedStringDataFragmentDoc } from "./TranslatedStringData";

export type { TradeInVehicleDataFragment } from "./TradeInVehicleData";

export { TradeInVehicleDataFragmentDoc } from "./TradeInVehicleData";

export type { TradeInSettingSpecFragment } from "./TradeInSetting";

export { TradeInSettingSpecFragmentDoc } from "./TradeInSetting";

export type { TradeInSalesOfferSpecsFragment } from "./TradeInSalesOfferSpecs";

export { TradeInSalesOfferSpecsFragmentDoc } from "./TradeInSalesOfferSpecs";

export type { TradeInModuleWithPermissionsSpecsFragment } from "./TradeInModuleWithPermissionsSpecs";

export { TradeInModuleWithPermissionsSpecsFragmentDoc } from "./TradeInModuleWithPermissionsSpecs";

export type { TradeInModuleSpecsFragment } from "./TradeInModuleSpecs";

export { TradeInModuleSpecsFragmentDoc } from "./TradeInModuleSpecs";

export type { TradeInListDataFragment } from "./TradeInListData";

export { TradeInListDataFragmentDoc } from "./TradeInListData";

export type { TradeInDataFragment } from "./TradeInData";

export { TradeInDataFragmentDoc } from "./TradeInData";

export type { TimeSlotDataFragment } from "./TimeSlotData";

export { TimeSlotDataFragmentDoc } from "./TimeSlotData";

export type { ThankYouPageContentSpecsFragment } from "./ThankYouPageContent";

export { ThankYouPageContentSpecsFragmentDoc } from "./ThankYouPageContent";

export type { TextImageWebPageBlockDataFragment } from "./TextImageWebPageBlockData";

export { TextImageWebPageBlockDataFragmentDoc } from "./TextImageWebPageBlockData";

export type { TextCarouselWebPageBlockDataFragment } from "./TextCarouselWebPageBlockData";

export { TextCarouselWebPageBlockDataFragmentDoc } from "./TextCarouselWebPageBlockData";

export type { TestDriveFixedPeriodDataFragment } from "./TestDriveFixedPeriodData";

export { TestDriveFixedPeriodDataFragmentDoc } from "./TestDriveFixedPeriodData";

export type { TestDriveBookingWindowSettingsDataFragment } from "./TestDriveBookingWindowSettingsData";

export { TestDriveBookingWindowSettingsDataFragmentDoc } from "./TestDriveBookingWindowSettingsData";

export type {
  TermSettingsDetails_DeferredPrincipalTermSettings_Fragment,
  TermSettingsDetails_GenericPrincipalTermSettings_Fragment,
  TermSettingsDetailsFragment,
} from "./TermSettingsDetails";

export { TermSettingsDetailsFragmentDoc } from "./TermSettingsDetails";

export type {
  SystemMessageData_MaintenanceUpdate_Fragment,
  SystemMessageData_MessageNotice_Fragment,
  SystemMessageData_UserSessionRevoked_Fragment,
  SystemMessageDataFragment,
} from "./SystemMessageData";

export { SystemMessageDataFragmentDoc } from "./SystemMessageData";

export type {
  StockPublicData_ConfiguratorStockInventory_Fragment,
  StockPublicData_MobilityStockInventory_Fragment,
  StockPublicDataFragment,
} from "./StockPublicData";

export { StockPublicDataFragmentDoc } from "./StockPublicData";

export type {
  StockInventorySpecs_ConfiguratorStockInventory_Fragment,
  StockInventorySpecs_MobilityStockInventory_Fragment,
  StockInventorySpecsFragment,
} from "./StockInventorySpecs";

export { StockInventorySpecsFragmentDoc } from "./StockInventorySpecs";

export type {
  StockDetailsPublicData_ConfiguratorStockInventory_Fragment,
  StockDetailsPublicData_MobilityStockInventory_Fragment,
  StockDetailsPublicDataFragment,
} from "./StockDetailsPublicData";

export { StockDetailsPublicDataFragmentDoc } from "./StockDetailsPublicData";

export type { StockBlockingPeriodDataFragment } from "./StockBlockingPeriod";

export { StockBlockingPeriodDataFragmentDoc } from "./StockBlockingPeriod";

export type { StandardLeadDataFragment } from "./StandardLeadData";

export { StandardLeadDataFragmentDoc } from "./StandardLeadData";

export type { StandardApplicationSpecFragment } from "./StandardApplicationSpec";

export { StandardApplicationSpecFragmentDoc } from "./StandardApplicationSpec";

export type { StandardApplicationPublicAccessEntrypointSpecsFragment } from "./StandardApplicationPublicAccessEntrypointSpecs";

export { StandardApplicationPublicAccessEntrypointSpecsFragmentDoc } from "./StandardApplicationPublicAccessEntrypointSpecs";

export type { StandardApplicationPublicAccessEntrypointContextDataFragment } from "./StandardApplicationPublicAccessEntrypointContextData";

export { StandardApplicationPublicAccessEntrypointContextDataFragmentDoc } from "./StandardApplicationPublicAccessEntrypointContextData";

export type { StandardApplicationModuleWithPermissionsSpecsFragment } from "./StandardApplicationModuleWithPermissionsSpecs";

export { StandardApplicationModuleWithPermissionsSpecsFragmentDoc } from "./StandardApplicationModuleWithPermissionsSpecs";

export type { StandardApplicationModuleSpecsForApplicationFragment } from "./StandardApplicationModuleSpecsForApplication";

export { StandardApplicationModuleSpecsForApplicationFragmentDoc } from "./StandardApplicationModuleSpecsForApplication";

export type { StandardApplicationModuleSpecsFragment } from "./StandardApplicationModuleSpecs";

export { StandardApplicationModuleSpecsFragmentDoc } from "./StandardApplicationModuleSpecs";

export type { StandardApplicationModuleInDealerSpecsFragment } from "./StandardApplicationModuleInDealerSpecs";

export { StandardApplicationModuleInDealerSpecsFragmentDoc } from "./StandardApplicationModuleInDealerSpecs";

export type {
  StandardApplicationModuleEmailContentSpecsFragment,
  StandardApplicationModuleEmailContentShareSubmissionSpecsFragment,
  StandardApplicationModuleEmailContentCustomerSpecsFragment,
  StandardApplicationModuleEmailContentSalesPersonSpecsFragment,
  StandardApplicationModuleEmailContentsSpecsFragment,
} from "./StandardApplicationModuleEmailContentsSpecs";

export {
  StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc,
  StandardApplicationModuleEmailContentSpecsFragmentDoc,
  StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc,
  StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc,
  StandardApplicationModuleEmailContentsSpecsFragmentDoc,
} from "./StandardApplicationModuleEmailContentsSpecs";

export type { StandardApplicationModuleDebugJourneyFragment } from "./StandardApplicationModuleDebugJourney";

export { StandardApplicationModuleDebugJourneyFragmentDoc } from "./StandardApplicationModuleDebugJourney";

export type { StandardApplicationEntrypointSpecsFragment } from "./StandardApplicationEntrypointSpecs";

export { StandardApplicationEntrypointSpecsFragmentDoc } from "./StandardApplicationEntrypointSpecs";

export type { StandardApplicationEntrypointContextDataFragment } from "./StandardApplicationEntrypointContextData";

export { StandardApplicationEntrypointContextDataFragmentDoc } from "./StandardApplicationEntrypointContextData";

export type { StandardApplicationDataFragment } from "./StandardApplicationData";

export { StandardApplicationDataFragmentDoc } from "./StandardApplicationData";

export type { SimpleVersioningDataFragment } from "./SimpleVersioningData";

export { SimpleVersioningDataFragmentDoc } from "./SimpleVersioningData";

export type { SimpleVehicleManagementModuleWithPermissionsSpecsFragment } from "./SimpleVehicleManagementModuleWithPermissionsSpecs";

export { SimpleVehicleManagementModuleWithPermissionsSpecsFragmentDoc } from "./SimpleVehicleManagementModuleWithPermissionsSpecs";

export type { SimpleVehicleManagementModuleSpecsFragment } from "./SimpleVehicleManagementModuleSpecs";

export { SimpleVehicleManagementModuleSpecsFragmentDoc } from "./SimpleVehicleManagementModuleSpecs";

export type { SalesOfferSpecsFragment } from "./SalesOfferSpecs";

export { SalesOfferSpecsFragmentDoc } from "./SalesOfferSpecs";

export type { SalesOfferSigningsSpecsFragment } from "./SalesOfferSigningsSpecs";

export { SalesOfferSigningsSpecsFragmentDoc } from "./SalesOfferSigningsSpecs";

export type { SalesOfferModuleWithPermissionsSpecsFragment } from "./SalesOfferModuleWithPermissionsSpecs";

export { SalesOfferModuleWithPermissionsSpecsFragmentDoc } from "./SalesOfferModuleWithPermissionsSpecs";

export type { SalesOfferModuleSpecsFragment } from "./SalesOfferModuleSpecs";

export { SalesOfferModuleSpecsFragmentDoc } from "./SalesOfferModuleSpecs";

export type { SalesOfferModuleInDealerSpecsFragment } from "./SalesOfferModuleInDealerSpecs";

export { SalesOfferModuleInDealerSpecsFragmentDoc } from "./SalesOfferModuleInDealerSpecs";

export type {
  SalesOfferModuleEmailContentsSpecsFragment,
  SalesOfferEmailContentsSpecsFragment,
} from "./SalesOfferModuleEmailContentsSpecs";

export {
  SalesOfferEmailContentsSpecsFragmentDoc,
  SalesOfferModuleEmailContentsSpecsFragmentDoc,
} from "./SalesOfferModuleEmailContentsSpecs";

export type { SalesOfferModuleDebugJourneyFragment } from "./SalesOfferModuleDebugJourney";

export { SalesOfferModuleDebugJourneyFragmentDoc } from "./SalesOfferModuleDebugJourney";

export type { SalesOfferKycPresetSpecsFragment } from "./SalesOfferKYCPresetSpecs";

export { SalesOfferKycPresetSpecsFragmentDoc } from "./SalesOfferKYCPresetSpecs";

export type { SalesOfferJourneyDataFragment } from "./SalesOfferJourneyData";

export { SalesOfferJourneyDataFragmentDoc } from "./SalesOfferJourneyData";

export type { SalesOfferDocumentDataFragment } from "./SalesOfferDocumentData";

export { SalesOfferDocumentDataFragmentDoc } from "./SalesOfferDocumentData";

export type { SalesOfferConsentsSpecsFragment } from "./SalesOfferConsentsSpecs";

export { SalesOfferConsentsSpecsFragmentDoc } from "./SalesOfferConsentsSpecs";

export type { SalesOfferApplicationSpecsFragment } from "./SalesOfferApplicationSpecs";

export { SalesOfferApplicationSpecsFragmentDoc } from "./SalesOfferApplicationSpecs";

export type { SalesOfferApplicationDataFragment } from "./SalesOfferApplicationData";

export { SalesOfferApplicationDataFragmentDoc } from "./SalesOfferApplicationData";

export type { SalesOfferApplicationConfigurationDataFragment } from "./SalesOfferApplicationConfigurationData";

export { SalesOfferApplicationConfigurationDataFragmentDoc } from "./SalesOfferApplicationConfigurationData";

export type { SalesControlBoardModuleWithPermissionsSpecsFragment } from "./SalesControlBoardModuleWithPermissionsSpecs";

export { SalesControlBoardModuleWithPermissionsSpecsFragmentDoc } from "./SalesControlBoardModuleWithPermissionsSpecs";

export type { SalesControlBoardModuleSpecsFragment } from "./SalesControlBoardModuleSpecs";

export { SalesControlBoardModuleSpecsFragmentDoc } from "./SalesControlBoardModuleSpecs";

export type { SalesControlBoardModuleInDealerSpecsFragment } from "./SalesControlBoardModuleInDealerSpecs";

export { SalesControlBoardModuleInDealerSpecsFragmentDoc } from "./SalesControlBoardModuleInDealerSpecs";

export type { RouterWithPermissionsSpecsFragment } from "./RouterWithPermissionsSpecs";

export { RouterWithPermissionsSpecsFragmentDoc } from "./RouterWithPermissionsSpecs";

export type {
  RouterFinderApplicationModuleFragment,
  RouterFinderApplicationPrivateModuleFragment,
  RouterSpecsFragment,
} from "./RouterSpecs";

export {
  RouterFinderApplicationModuleFragmentDoc,
  RouterFinderApplicationPrivateModuleFragmentDoc,
  RouterSpecsFragmentDoc,
} from "./RouterSpecs";

export type { RouterListWithEndpointsDataFragment } from "./RouterListWithEndpoints";

export { RouterListWithEndpointsDataFragmentDoc } from "./RouterListWithEndpoints";

export type { RouterListDataFragment } from "./RouterListData";

export { RouterListDataFragmentDoc } from "./RouterListData";

export type {
  RouterLayoutContextData_BasicLayout_Fragment,
  RouterLayoutContextData_BasicProLayout_Fragment,
  RouterLayoutContextData_PorscheV3Layout_Fragment,
  RouterLayoutContextDataFragment,
} from "./RouterLayoutContextData";

export { RouterLayoutContextDataFragmentDoc } from "./RouterLayoutContextData";

export type { RouterContextDataFragment } from "./RouterContextData";

export { RouterContextDataFragmentDoc } from "./RouterContextData";

export type { RoleSpecsFragment } from "./RoleSpecs";

export { RoleSpecsFragmentDoc } from "./RoleSpecs";

export type { RoleListDataFragment } from "./RoleListData";

export { RoleListDataFragmentDoc } from "./RoleListData";

export type { ResidualValueSettingsDetailsFragment } from "./ResidualValueSettingsDetails";

export { ResidualValueSettingsDetailsFragmentDoc } from "./ResidualValueSettingsDetails";

export type {
  ReferenceDepositData_ApplicationAdyenDeposit_Fragment,
  ReferenceDepositData_ApplicationFiservDeposit_Fragment,
  ReferenceDepositData_ApplicationPayGateDeposit_Fragment,
  ReferenceDepositData_ApplicationPorscheDeposit_Fragment,
  ReferenceDepositData_ApplicationTtbDeposit_Fragment,
  ReferenceDepositDataFragment,
  ReferenceFinancingData_DefaultApplicationFinancing_Fragment,
  ReferenceFinancingData_NewZealandApplicationFinancing_Fragment,
  ReferenceFinancingData_SingaporeApplicationFinancing_Fragment,
  ReferenceFinancingDataFragment,
  ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment,
  ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment,
  ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment,
  ReferenceInsuranceDataFragment,
  ReferenceApplicationData_ConfiguratorApplication_Fragment,
  ReferenceApplicationData_EventApplication_Fragment,
  ReferenceApplicationData_FinderApplication_Fragment,
  ReferenceApplicationData_LaunchpadApplication_Fragment,
  ReferenceApplicationData_MobilityApplication_Fragment,
  ReferenceApplicationData_SalesOfferApplication_Fragment,
  ReferenceApplicationData_StandardApplication_Fragment,
  ReferenceApplicationDataFragment,
} from "./ReferenceApplicationData";

export {
  ReferenceDepositDataFragmentDoc,
  ReferenceFinancingDataFragmentDoc,
  ReferenceInsuranceDataFragmentDoc,
  ReferenceApplicationDataFragmentDoc,
} from "./ReferenceApplicationData";

export type { PromoCodeSpecFragment } from "./PromoCodeSpec";

export { PromoCodeSpecFragmentDoc } from "./PromoCodeSpec";

export type { PromoCodeModuleWithPermissionsSpecsFragment } from "./PromoCodeModuleWithPermissionsSpecs";

export { PromoCodeModuleWithPermissionsSpecsFragmentDoc } from "./PromoCodeModuleWithPermissionsSpecs";

export type { PromoCodeModuleSpecsFragment } from "./PromoCodeModuleSpecs";

export { PromoCodeModuleSpecsFragmentDoc } from "./PromoCodeModuleSpecs";

export type { PromoCodeListingDataFragment } from "./PromoCodeListingData";

export { PromoCodeListingDataFragmentDoc } from "./PromoCodeListingData";

export type { PromoCodeDataFragment } from "./PromoCodeData";

export { PromoCodeDataFragmentDoc } from "./PromoCodeData";

export type {
  ProductionInsuranceProductDetails_Eazy_Fragment,
  ProductionInsuranceProductDetails_ErgoLookupTable_Fragment,
  ProductionInsuranceProductDetailsFragment,
} from "./ProductionInsuranceProductDetails";

export { ProductionInsuranceProductDetailsFragmentDoc } from "./ProductionInsuranceProductDetails";

export type {
  ProductionFinanceProductDetails_LocalDeferredPrincipal_Fragment,
  ProductionFinanceProductDetails_LocalHirePurchase_Fragment,
  ProductionFinanceProductDetails_LocalHirePurchaseWithBalloon_Fragment,
  ProductionFinanceProductDetails_LocalHirePurchaseWithBalloonGfv_Fragment,
  ProductionFinanceProductDetails_LocalLease_Fragment,
  ProductionFinanceProductDetails_LocalLeasePurchase_Fragment,
  ProductionFinanceProductDetails_LocalUcclLeasing_Fragment,
  ProductionFinanceProductDetailsFragment,
} from "./ProductionFinanceProductDetails";

export { ProductionFinanceProductDetailsFragmentDoc } from "./ProductionFinanceProductDetails";

export type { ProductionBankDetailsFragment } from "./ProductionBankDetails";

export { ProductionBankDetailsFragmentDoc } from "./ProductionBankDetails";

export type { PrefetchKycConsentsDataFragment } from "./PrefetchKYCConsentsData";

export { PrefetchKycConsentsDataFragmentDoc } from "./PrefetchKYCConsentsData";

export type {
  PorscheVehicleDataSpecsFragment,
  PorscheVehicleDataFeatureSpecsFragment,
  PorscheVehicleImagesSpecsFragment,
} from "./PorscheVehicleDataSpecs";

export {
  PorscheVehicleDataFeatureSpecsFragmentDoc,
  PorscheVehicleImagesSpecsFragmentDoc,
  PorscheVehicleDataSpecsFragmentDoc,
} from "./PorscheVehicleDataSpecs";

export type { PorscheRetainModuleWithPermissionsSpecsFragment } from "./PorscheRetainModuleWithPermissionsSpecs";

export { PorscheRetainModuleWithPermissionsSpecsFragmentDoc } from "./PorscheRetainModuleWithPermissionsSpecs";

export type { PorscheRetainModuleSpecsFragment } from "./PorscheRetainModuleSpecs";

export { PorscheRetainModuleSpecsFragmentDoc } from "./PorscheRetainModuleSpecs";

export type { PorschePaymentSettingsSpecFragment } from "./PorschePaymentSettingsSpec";

export { PorschePaymentSettingsSpecFragmentDoc } from "./PorschePaymentSettingsSpec";

export type { PorschePaymentModuleWithPermissionsSpecsFragment } from "./PorschePaymentModuleWithPermissionsSpecs";

export { PorschePaymentModuleWithPermissionsSpecsFragmentDoc } from "./PorschePaymentModuleWithPermissionsSpecs";

export type { PorschePaymentModuleSpecsFragment } from "./PorschePaymentModuleSpecs";

export { PorschePaymentModuleSpecsFragmentDoc } from "./PorschePaymentModuleSpecs";

export type { PorscheMasterDataModuleWithPermissionsSpecsFragment } from "./PorscheMasterDataModuleWithPermissionsSpecs";

export { PorscheMasterDataModuleWithPermissionsSpecsFragmentDoc } from "./PorscheMasterDataModuleWithPermissionsSpecs";

export type { PorscheMasterDataModuleSpecsFragment } from "./PorscheMasterDataModuleSpecs";

export { PorscheMasterDataModuleSpecsFragmentDoc } from "./PorscheMasterDataModuleSpecs";

export type { PorscheIdSettingSpecFragment } from "./PorscheIdSettingSpec";

export { PorscheIdSettingSpecFragmentDoc } from "./PorscheIdSettingSpec";

export type { PorscheIdModuleWithPermissionsSpecsFragment } from "./PorscheIdModuleWithPermissionsSpecs";

export { PorscheIdModuleWithPermissionsSpecsFragmentDoc } from "./PorscheIdModuleWithPermissionsSpecs";

export type { PorscheIdModuleSpecsFragment } from "./PorscheIdModuleSpecs";

export { PorscheIdModuleSpecsFragmentDoc } from "./PorscheIdModuleSpecs";

export type { PermissionSpecsFragment } from "./PermissionSpecs";

export { PermissionSpecsFragmentDoc } from "./PermissionSpecs";

export type { PeriodDataFragment } from "./PeriodData";

export { PeriodDataFragmentDoc } from "./PeriodData";

export type { PaymentSettingsDetailsFragment } from "./PaymentSettingsDetails";

export { PaymentSettingsDetailsFragmentDoc } from "./PaymentSettingsDetails";

export type { PayGatePaymentSettingsSpecFragment } from "./PayGatePaymentSettingsSpec";

export { PayGatePaymentSettingsSpecFragmentDoc } from "./PayGatePaymentSettingsSpec";

export type { PayGatePaymentModuleWithPermissionsSpecsFragment } from "./PayGatePaymentModuleWithPermissionsSpecs";

export { PayGatePaymentModuleWithPermissionsSpecsFragmentDoc } from "./PayGatePaymentModuleWithPermissionsSpecs";

export type { PayGatePaymentModuleSpecsFragment } from "./PayGatePaymentModuleSpecs";

export { PayGatePaymentModuleSpecsFragmentDoc } from "./PayGatePaymentModuleSpecs";

export type { PathScriptSpecsFragment } from "./PathScriptSpecs";

export { PathScriptSpecsFragmentDoc } from "./PathScriptSpecs";

export type {
  PackageTypeSpecs_PackageTypeWithDescription_Fragment,
  PackageTypeSpecs_PackageTypeWithPrice_Fragment,
  PackageTypeSpecsFragment,
} from "./PackageTypeSpecs";

export { PackageTypeSpecsFragmentDoc } from "./PackageTypeSpecs";

export type { PackageSettingsSpecsFragment } from "./PackageSettingsSpecs";

export { PackageSettingsSpecsFragmentDoc } from "./PackageSettingsSpecs";

export type { PackageBlockSpecsFragment } from "./PackageBlockSpecs";

export { PackageBlockSpecsFragmentDoc } from "./PackageBlockSpecs";

export type { OptionsBlockSpecsFragment } from "./OptionsBlockSpecs";

export { OptionsBlockSpecsFragmentDoc } from "./OptionsBlockSpecs";

export type {
  OptionSettingDetails_ComboOptionSettings_Fragment,
  OptionSettingDetails_DropdownOptionSettings_Fragment,
  OptionSettingDetails_MultiSelectOptionSettings_Fragment,
  OptionSettingDetails_SingleSelectOptionSettings_Fragment,
  OptionSettingDetailsFragment,
} from "./OptionSettingDetails";

export { OptionSettingDetailsFragmentDoc } from "./OptionSettingDetails";

export type { OidcModuleSpecsFragment } from "./OIDCModuleSpecs";

export { OidcModuleSpecsFragmentDoc } from "./OIDCModuleSpecs";

export type { NzFeesDealerMarketDataFragment } from "./NzFeesDealerMarketData";

export { NzFeesDealerMarketDataFragmentDoc } from "./NzFeesDealerMarketData";

export type { NearbyDealersDataFragment } from "./NearbyDealersData";

export { NearbyDealersDataFragmentDoc } from "./NearbyDealersData";

export type { NamirialSigningModuleWithPermissionsSpecsFragment } from "./NamirialSigningModuleWithPermissionsSpecs";

export { NamirialSigningModuleWithPermissionsSpecsFragmentDoc } from "./NamirialSigningModuleWithPermissionsSpecs";

export type { NamirialSigningModuleSpecsFragment } from "./NamirialSigningModuleSpecs";

export { NamirialSigningModuleSpecsFragmentDoc } from "./NamirialSigningModuleSpecs";

export type { NamirialSigningDataFragment } from "./NamirialSigningData";

export { NamirialSigningDataFragmentDoc } from "./NamirialSigningData";

export type { NamirialSettingsSpecFragment } from "./NamirialSettingsSpec";

export { NamirialSettingsSpecFragmentDoc } from "./NamirialSettingsSpec";

export type { MyInfoSettingsListFragment } from "./MyInfoSettingsList";

export { MyInfoSettingsListFragmentDoc } from "./MyInfoSettingsList";

export type { MyInfoSettingSpecFragment } from "./MyInfoSettingSpec";

export { MyInfoSettingSpecFragmentDoc } from "./MyInfoSettingSpec";

export type { MyInfoModuleWithPermissionsSpecsFragment } from "./MyInfoModuleWithPermissionsSpecs";

export { MyInfoModuleWithPermissionsSpecsFragmentDoc } from "./MyInfoModuleWithPermissionsSpecs";

export type { MyInfoModuleSpecsFragment } from "./MyInfoModuleSpecs";

export { MyInfoModuleSpecsFragmentDoc } from "./MyInfoModuleSpecs";

export type {
  ModulesOptionsData_AdyenPaymentModule_Fragment,
  ModulesOptionsData_AppointmentModule_Fragment,
  ModulesOptionsData_AutoplayModule_Fragment,
  ModulesOptionsData_BankModule_Fragment,
  ModulesOptionsData_BasicSigningModule_Fragment,
  ModulesOptionsData_CapModule_Fragment,
  ModulesOptionsData_ConfiguratorModule_Fragment,
  ModulesOptionsData_ConsentsAndDeclarationsModule_Fragment,
  ModulesOptionsData_CtsModule_Fragment,
  ModulesOptionsData_DocusignModule_Fragment,
  ModulesOptionsData_EventApplicationModule_Fragment,
  ModulesOptionsData_FinderApplicationPrivateModule_Fragment,
  ModulesOptionsData_FinderApplicationPublicModule_Fragment,
  ModulesOptionsData_FinderVehicleManagementModule_Fragment,
  ModulesOptionsData_FiservPaymentModule_Fragment,
  ModulesOptionsData_GiftVoucherModule_Fragment,
  ModulesOptionsData_InsuranceModule_Fragment,
  ModulesOptionsData_LabelsModule_Fragment,
  ModulesOptionsData_LaunchPadModule_Fragment,
  ModulesOptionsData_LocalCustomerManagementModule_Fragment,
  ModulesOptionsData_MaintenanceModule_Fragment,
  ModulesOptionsData_MarketingModule_Fragment,
  ModulesOptionsData_MobilityModule_Fragment,
  ModulesOptionsData_MyInfoModule_Fragment,
  ModulesOptionsData_NamirialSigningModule_Fragment,
  ModulesOptionsData_OidcModule_Fragment,
  ModulesOptionsData_PayGatePaymentModule_Fragment,
  ModulesOptionsData_PorscheIdModule_Fragment,
  ModulesOptionsData_PorscheMasterDataModule_Fragment,
  ModulesOptionsData_PorschePaymentModule_Fragment,
  ModulesOptionsData_PorscheRetainModule_Fragment,
  ModulesOptionsData_PromoCodeModule_Fragment,
  ModulesOptionsData_SalesControlBoardModule_Fragment,
  ModulesOptionsData_SalesOfferModule_Fragment,
  ModulesOptionsData_SimpleVehicleManagementModule_Fragment,
  ModulesOptionsData_StandardApplicationModule_Fragment,
  ModulesOptionsData_TradeInModule_Fragment,
  ModulesOptionsData_TtbPaymentModule_Fragment,
  ModulesOptionsData_UserlikeChatbotModule_Fragment,
  ModulesOptionsData_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModulesOptionsData_VisitAppointmentModule_Fragment,
  ModulesOptionsData_WebsiteModule_Fragment,
  ModulesOptionsData_WhatsappLiveChatModule_Fragment,
  ModulesOptionsDataFragment,
} from "./ModulesOptionsData";

export { ModulesOptionsDataFragmentDoc } from "./ModulesOptionsData";

export type {
  ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment,
  ModulesCompanyTimezoneData_AppointmentModule_Fragment,
  ModulesCompanyTimezoneData_AutoplayModule_Fragment,
  ModulesCompanyTimezoneData_BankModule_Fragment,
  ModulesCompanyTimezoneData_BasicSigningModule_Fragment,
  ModulesCompanyTimezoneData_CapModule_Fragment,
  ModulesCompanyTimezoneData_ConfiguratorModule_Fragment,
  ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment,
  ModulesCompanyTimezoneData_CtsModule_Fragment,
  ModulesCompanyTimezoneData_DocusignModule_Fragment,
  ModulesCompanyTimezoneData_EventApplicationModule_Fragment,
  ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment,
  ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment,
  ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment,
  ModulesCompanyTimezoneData_FiservPaymentModule_Fragment,
  ModulesCompanyTimezoneData_GiftVoucherModule_Fragment,
  ModulesCompanyTimezoneData_InsuranceModule_Fragment,
  ModulesCompanyTimezoneData_LabelsModule_Fragment,
  ModulesCompanyTimezoneData_LaunchPadModule_Fragment,
  ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment,
  ModulesCompanyTimezoneData_MaintenanceModule_Fragment,
  ModulesCompanyTimezoneData_MarketingModule_Fragment,
  ModulesCompanyTimezoneData_MobilityModule_Fragment,
  ModulesCompanyTimezoneData_MyInfoModule_Fragment,
  ModulesCompanyTimezoneData_NamirialSigningModule_Fragment,
  ModulesCompanyTimezoneData_OidcModule_Fragment,
  ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment,
  ModulesCompanyTimezoneData_PorscheIdModule_Fragment,
  ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment,
  ModulesCompanyTimezoneData_PorschePaymentModule_Fragment,
  ModulesCompanyTimezoneData_PorscheRetainModule_Fragment,
  ModulesCompanyTimezoneData_PromoCodeModule_Fragment,
  ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment,
  ModulesCompanyTimezoneData_SalesOfferModule_Fragment,
  ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment,
  ModulesCompanyTimezoneData_StandardApplicationModule_Fragment,
  ModulesCompanyTimezoneData_TradeInModule_Fragment,
  ModulesCompanyTimezoneData_TtbPaymentModule_Fragment,
  ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment,
  ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment,
  ModulesCompanyTimezoneData_WebsiteModule_Fragment,
  ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment,
  ModulesCompanyTimezoneDataFragment,
} from "./ModulesCompanyTimezoneData";

export { ModulesCompanyTimezoneDataFragmentDoc } from "./ModulesCompanyTimezoneData";

export type {
  ModuleWithScenarios_AdyenPaymentModule_Fragment,
  ModuleWithScenarios_AppointmentModule_Fragment,
  ModuleWithScenarios_AutoplayModule_Fragment,
  ModuleWithScenarios_BankModule_Fragment,
  ModuleWithScenarios_BasicSigningModule_Fragment,
  ModuleWithScenarios_CapModule_Fragment,
  ModuleWithScenarios_ConfiguratorModule_Fragment,
  ModuleWithScenarios_ConsentsAndDeclarationsModule_Fragment,
  ModuleWithScenarios_CtsModule_Fragment,
  ModuleWithScenarios_DocusignModule_Fragment,
  ModuleWithScenarios_EventApplicationModule_Fragment,
  ModuleWithScenarios_FinderApplicationPrivateModule_Fragment,
  ModuleWithScenarios_FinderApplicationPublicModule_Fragment,
  ModuleWithScenarios_FinderVehicleManagementModule_Fragment,
  ModuleWithScenarios_FiservPaymentModule_Fragment,
  ModuleWithScenarios_GiftVoucherModule_Fragment,
  ModuleWithScenarios_InsuranceModule_Fragment,
  ModuleWithScenarios_LabelsModule_Fragment,
  ModuleWithScenarios_LaunchPadModule_Fragment,
  ModuleWithScenarios_LocalCustomerManagementModule_Fragment,
  ModuleWithScenarios_MaintenanceModule_Fragment,
  ModuleWithScenarios_MarketingModule_Fragment,
  ModuleWithScenarios_MobilityModule_Fragment,
  ModuleWithScenarios_MyInfoModule_Fragment,
  ModuleWithScenarios_NamirialSigningModule_Fragment,
  ModuleWithScenarios_OidcModule_Fragment,
  ModuleWithScenarios_PayGatePaymentModule_Fragment,
  ModuleWithScenarios_PorscheIdModule_Fragment,
  ModuleWithScenarios_PorscheMasterDataModule_Fragment,
  ModuleWithScenarios_PorschePaymentModule_Fragment,
  ModuleWithScenarios_PorscheRetainModule_Fragment,
  ModuleWithScenarios_PromoCodeModule_Fragment,
  ModuleWithScenarios_SalesControlBoardModule_Fragment,
  ModuleWithScenarios_SalesOfferModule_Fragment,
  ModuleWithScenarios_SimpleVehicleManagementModule_Fragment,
  ModuleWithScenarios_StandardApplicationModule_Fragment,
  ModuleWithScenarios_TradeInModule_Fragment,
  ModuleWithScenarios_TtbPaymentModule_Fragment,
  ModuleWithScenarios_UserlikeChatbotModule_Fragment,
  ModuleWithScenarios_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModuleWithScenarios_VisitAppointmentModule_Fragment,
  ModuleWithScenarios_WebsiteModule_Fragment,
  ModuleWithScenarios_WhatsappLiveChatModule_Fragment,
  ModuleWithScenariosFragment,
} from "./ModuleWithScenarios";

export { ModuleWithScenariosFragmentDoc } from "./ModuleWithScenarios";

export type {
  ModuleWithPermissionsSpecs_AdyenPaymentModule_Fragment,
  ModuleWithPermissionsSpecs_AppointmentModule_Fragment,
  ModuleWithPermissionsSpecs_AutoplayModule_Fragment,
  ModuleWithPermissionsSpecs_BankModule_Fragment,
  ModuleWithPermissionsSpecs_BasicSigningModule_Fragment,
  ModuleWithPermissionsSpecs_CapModule_Fragment,
  ModuleWithPermissionsSpecs_ConfiguratorModule_Fragment,
  ModuleWithPermissionsSpecs_ConsentsAndDeclarationsModule_Fragment,
  ModuleWithPermissionsSpecs_CtsModule_Fragment,
  ModuleWithPermissionsSpecs_DocusignModule_Fragment,
  ModuleWithPermissionsSpecs_EventApplicationModule_Fragment,
  ModuleWithPermissionsSpecs_FinderApplicationPrivateModule_Fragment,
  ModuleWithPermissionsSpecs_FinderApplicationPublicModule_Fragment,
  ModuleWithPermissionsSpecs_FinderVehicleManagementModule_Fragment,
  ModuleWithPermissionsSpecs_FiservPaymentModule_Fragment,
  ModuleWithPermissionsSpecs_GiftVoucherModule_Fragment,
  ModuleWithPermissionsSpecs_InsuranceModule_Fragment,
  ModuleWithPermissionsSpecs_LabelsModule_Fragment,
  ModuleWithPermissionsSpecs_LaunchPadModule_Fragment,
  ModuleWithPermissionsSpecs_LocalCustomerManagementModule_Fragment,
  ModuleWithPermissionsSpecs_MaintenanceModule_Fragment,
  ModuleWithPermissionsSpecs_MarketingModule_Fragment,
  ModuleWithPermissionsSpecs_MobilityModule_Fragment,
  ModuleWithPermissionsSpecs_MyInfoModule_Fragment,
  ModuleWithPermissionsSpecs_NamirialSigningModule_Fragment,
  ModuleWithPermissionsSpecs_OidcModule_Fragment,
  ModuleWithPermissionsSpecs_PayGatePaymentModule_Fragment,
  ModuleWithPermissionsSpecs_PorscheIdModule_Fragment,
  ModuleWithPermissionsSpecs_PorscheMasterDataModule_Fragment,
  ModuleWithPermissionsSpecs_PorschePaymentModule_Fragment,
  ModuleWithPermissionsSpecs_PorscheRetainModule_Fragment,
  ModuleWithPermissionsSpecs_PromoCodeModule_Fragment,
  ModuleWithPermissionsSpecs_SalesControlBoardModule_Fragment,
  ModuleWithPermissionsSpecs_SalesOfferModule_Fragment,
  ModuleWithPermissionsSpecs_SimpleVehicleManagementModule_Fragment,
  ModuleWithPermissionsSpecs_StandardApplicationModule_Fragment,
  ModuleWithPermissionsSpecs_TradeInModule_Fragment,
  ModuleWithPermissionsSpecs_TtbPaymentModule_Fragment,
  ModuleWithPermissionsSpecs_UserlikeChatbotModule_Fragment,
  ModuleWithPermissionsSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModuleWithPermissionsSpecs_VisitAppointmentModule_Fragment,
  ModuleWithPermissionsSpecs_WebsiteModule_Fragment,
  ModuleWithPermissionsSpecs_WhatsappLiveChatModule_Fragment,
  ModuleWithPermissionsSpecsFragment,
} from "./ModuleWithPermissionsSpecs";

export { ModuleWithPermissionsSpecsFragmentDoc } from "./ModuleWithPermissionsSpecs";

export type { ModuleVariantDataFragment } from "./ModuleVariantData";

export { ModuleVariantDataFragmentDoc } from "./ModuleVariantData";

export type {
  ModuleSpecs_AdyenPaymentModule_Fragment,
  ModuleSpecs_AppointmentModule_Fragment,
  ModuleSpecs_AutoplayModule_Fragment,
  ModuleSpecs_BankModule_Fragment,
  ModuleSpecs_BasicSigningModule_Fragment,
  ModuleSpecs_CapModule_Fragment,
  ModuleSpecs_ConfiguratorModule_Fragment,
  ModuleSpecs_ConsentsAndDeclarationsModule_Fragment,
  ModuleSpecs_CtsModule_Fragment,
  ModuleSpecs_DocusignModule_Fragment,
  ModuleSpecs_EventApplicationModule_Fragment,
  ModuleSpecs_FinderApplicationPrivateModule_Fragment,
  ModuleSpecs_FinderApplicationPublicModule_Fragment,
  ModuleSpecs_FinderVehicleManagementModule_Fragment,
  ModuleSpecs_FiservPaymentModule_Fragment,
  ModuleSpecs_GiftVoucherModule_Fragment,
  ModuleSpecs_InsuranceModule_Fragment,
  ModuleSpecs_LabelsModule_Fragment,
  ModuleSpecs_LaunchPadModule_Fragment,
  ModuleSpecs_LocalCustomerManagementModule_Fragment,
  ModuleSpecs_MaintenanceModule_Fragment,
  ModuleSpecs_MarketingModule_Fragment,
  ModuleSpecs_MobilityModule_Fragment,
  ModuleSpecs_MyInfoModule_Fragment,
  ModuleSpecs_NamirialSigningModule_Fragment,
  ModuleSpecs_OidcModule_Fragment,
  ModuleSpecs_PayGatePaymentModule_Fragment,
  ModuleSpecs_PorscheIdModule_Fragment,
  ModuleSpecs_PorscheMasterDataModule_Fragment,
  ModuleSpecs_PorschePaymentModule_Fragment,
  ModuleSpecs_PorscheRetainModule_Fragment,
  ModuleSpecs_PromoCodeModule_Fragment,
  ModuleSpecs_SalesControlBoardModule_Fragment,
  ModuleSpecs_SalesOfferModule_Fragment,
  ModuleSpecs_SimpleVehicleManagementModule_Fragment,
  ModuleSpecs_StandardApplicationModule_Fragment,
  ModuleSpecs_TradeInModule_Fragment,
  ModuleSpecs_TtbPaymentModule_Fragment,
  ModuleSpecs_UserlikeChatbotModule_Fragment,
  ModuleSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModuleSpecs_VisitAppointmentModule_Fragment,
  ModuleSpecs_WebsiteModule_Fragment,
  ModuleSpecs_WhatsappLiveChatModule_Fragment,
  ModuleSpecsFragment,
} from "./ModuleSpecs";

export { ModuleSpecsFragmentDoc } from "./ModuleSpecs";

export type {
  ModuleListData_AdyenPaymentModule_Fragment,
  ModuleListData_AppointmentModule_Fragment,
  ModuleListData_AutoplayModule_Fragment,
  ModuleListData_BankModule_Fragment,
  ModuleListData_BasicSigningModule_Fragment,
  ModuleListData_CapModule_Fragment,
  ModuleListData_ConfiguratorModule_Fragment,
  ModuleListData_ConsentsAndDeclarationsModule_Fragment,
  ModuleListData_CtsModule_Fragment,
  ModuleListData_DocusignModule_Fragment,
  ModuleListData_EventApplicationModule_Fragment,
  ModuleListData_FinderApplicationPrivateModule_Fragment,
  ModuleListData_FinderApplicationPublicModule_Fragment,
  ModuleListData_FinderVehicleManagementModule_Fragment,
  ModuleListData_FiservPaymentModule_Fragment,
  ModuleListData_GiftVoucherModule_Fragment,
  ModuleListData_InsuranceModule_Fragment,
  ModuleListData_LabelsModule_Fragment,
  ModuleListData_LaunchPadModule_Fragment,
  ModuleListData_LocalCustomerManagementModule_Fragment,
  ModuleListData_MaintenanceModule_Fragment,
  ModuleListData_MarketingModule_Fragment,
  ModuleListData_MobilityModule_Fragment,
  ModuleListData_MyInfoModule_Fragment,
  ModuleListData_NamirialSigningModule_Fragment,
  ModuleListData_OidcModule_Fragment,
  ModuleListData_PayGatePaymentModule_Fragment,
  ModuleListData_PorscheIdModule_Fragment,
  ModuleListData_PorscheMasterDataModule_Fragment,
  ModuleListData_PorschePaymentModule_Fragment,
  ModuleListData_PorscheRetainModule_Fragment,
  ModuleListData_PromoCodeModule_Fragment,
  ModuleListData_SalesControlBoardModule_Fragment,
  ModuleListData_SalesOfferModule_Fragment,
  ModuleListData_SimpleVehicleManagementModule_Fragment,
  ModuleListData_StandardApplicationModule_Fragment,
  ModuleListData_TradeInModule_Fragment,
  ModuleListData_TtbPaymentModule_Fragment,
  ModuleListData_UserlikeChatbotModule_Fragment,
  ModuleListData_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModuleListData_VisitAppointmentModule_Fragment,
  ModuleListData_WebsiteModule_Fragment,
  ModuleListData_WhatsappLiveChatModule_Fragment,
  ModuleListDataFragment,
} from "./ModuleListData";

export { ModuleListDataFragmentDoc } from "./ModuleListData";

export type {
  ModuleInDealerSpecs_AdyenPaymentModule_Fragment,
  ModuleInDealerSpecs_AppointmentModule_Fragment,
  ModuleInDealerSpecs_AutoplayModule_Fragment,
  ModuleInDealerSpecs_BankModule_Fragment,
  ModuleInDealerSpecs_BasicSigningModule_Fragment,
  ModuleInDealerSpecs_CapModule_Fragment,
  ModuleInDealerSpecs_ConfiguratorModule_Fragment,
  ModuleInDealerSpecs_ConsentsAndDeclarationsModule_Fragment,
  ModuleInDealerSpecs_CtsModule_Fragment,
  ModuleInDealerSpecs_DocusignModule_Fragment,
  ModuleInDealerSpecs_EventApplicationModule_Fragment,
  ModuleInDealerSpecs_FinderApplicationPrivateModule_Fragment,
  ModuleInDealerSpecs_FinderApplicationPublicModule_Fragment,
  ModuleInDealerSpecs_FinderVehicleManagementModule_Fragment,
  ModuleInDealerSpecs_FiservPaymentModule_Fragment,
  ModuleInDealerSpecs_GiftVoucherModule_Fragment,
  ModuleInDealerSpecs_InsuranceModule_Fragment,
  ModuleInDealerSpecs_LabelsModule_Fragment,
  ModuleInDealerSpecs_LaunchPadModule_Fragment,
  ModuleInDealerSpecs_LocalCustomerManagementModule_Fragment,
  ModuleInDealerSpecs_MaintenanceModule_Fragment,
  ModuleInDealerSpecs_MarketingModule_Fragment,
  ModuleInDealerSpecs_MobilityModule_Fragment,
  ModuleInDealerSpecs_MyInfoModule_Fragment,
  ModuleInDealerSpecs_NamirialSigningModule_Fragment,
  ModuleInDealerSpecs_OidcModule_Fragment,
  ModuleInDealerSpecs_PayGatePaymentModule_Fragment,
  ModuleInDealerSpecs_PorscheIdModule_Fragment,
  ModuleInDealerSpecs_PorscheMasterDataModule_Fragment,
  ModuleInDealerSpecs_PorschePaymentModule_Fragment,
  ModuleInDealerSpecs_PorscheRetainModule_Fragment,
  ModuleInDealerSpecs_PromoCodeModule_Fragment,
  ModuleInDealerSpecs_SalesControlBoardModule_Fragment,
  ModuleInDealerSpecs_SalesOfferModule_Fragment,
  ModuleInDealerSpecs_SimpleVehicleManagementModule_Fragment,
  ModuleInDealerSpecs_StandardApplicationModule_Fragment,
  ModuleInDealerSpecs_TradeInModule_Fragment,
  ModuleInDealerSpecs_TtbPaymentModule_Fragment,
  ModuleInDealerSpecs_UserlikeChatbotModule_Fragment,
  ModuleInDealerSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModuleInDealerSpecs_VisitAppointmentModule_Fragment,
  ModuleInDealerSpecs_WebsiteModule_Fragment,
  ModuleInDealerSpecs_WhatsappLiveChatModule_Fragment,
  ModuleInDealerSpecsFragment,
} from "./ModuleInDealerSpecs";

export { ModuleInDealerSpecsFragmentDoc } from "./ModuleInDealerSpecs";

export type { ModuleDisclaimersDataFragment } from "./ModuleDisclaimersData";

export { ModuleDisclaimersDataFragmentDoc } from "./ModuleDisclaimersData";

export type { ModelConfiguratorWithPermissionsSpecsFragment } from "./ModelConfiguratorWithPermissionsSpecs";

export { ModelConfiguratorWithPermissionsSpecsFragmentDoc } from "./ModelConfiguratorWithPermissionsSpecs";

export type { ModelConfiguratorSpecsFragment } from "./ModelConfiguratorSpecs";

export { ModelConfiguratorSpecsFragmentDoc } from "./ModelConfiguratorSpecs";

export type { ModelConfiguratorListDataFragment } from "./ModelConfiguratorListData";

export { ModelConfiguratorListDataFragmentDoc } from "./ModelConfiguratorListData";

export type { ModelConfiguratorDetailsFragment } from "./ModelConfiguratorDetails";

export { ModelConfiguratorDetailsFragmentDoc } from "./ModelConfiguratorDetails";

export type {
  MobilityStockPublicList_ConfiguratorStockInventory_Fragment,
  MobilityStockPublicList_MobilityStockInventory_Fragment,
  MobilityStockPublicListFragment,
} from "./MobilityStockPublicList";

export { MobilityStockPublicListFragmentDoc } from "./MobilityStockPublicList";

export type { MobilityStockGiftVoucherDataFragment } from "./MobilityStockGiftVoucherData";

export { MobilityStockGiftVoucherDataFragmentDoc } from "./MobilityStockGiftVoucherData";

export type {
  MobilitySnapshotData_MobilityAdditionalInfoSnapshot_Fragment,
  MobilitySnapshotData_MobilityAddonSnapshot_Fragment,
  MobilitySnapshotDataFragment,
} from "./MobilitySnapshotData";

export { MobilitySnapshotDataFragmentDoc } from "./MobilitySnapshotData";

export type { MobilitySigningSettingSpecsFragment } from "./MobilitySigningSettingSpecs";

export { MobilitySigningSettingSpecsFragmentDoc } from "./MobilitySigningSettingSpecs";

export type { MobilityOperatorEmailContentDataFragment } from "./MobilityOperatorEmailContentData";

export { MobilityOperatorEmailContentDataFragmentDoc } from "./MobilityOperatorEmailContentData";

export type { MobilityModuleWithPermissionsSpecsFragment } from "./MobilityModuleWithPermissionsSpecs";

export { MobilityModuleWithPermissionsSpecsFragmentDoc } from "./MobilityModuleWithPermissionsSpecs";

export type { MobilityModuleSpecsFragment } from "./MobilityModuleSpecs";

export { MobilityModuleSpecsFragmentDoc } from "./MobilityModuleSpecs";

export type { MobilityModuleInDealerSpecsFragment } from "./MobilityModuleInDealerSpecs";

export { MobilityModuleInDealerSpecsFragmentDoc } from "./MobilityModuleInDealerSpecs";

export type { MobilityModuleGiftVoucherDataFragment } from "./MobilityModuleGiftVoucherData";

export { MobilityModuleGiftVoucherDataFragmentDoc } from "./MobilityModuleGiftVoucherData";

export type { MobilityModuleEmailScenarioContentSpecsFragment } from "./MobilityModuleEmailScenarioContentSpecs";

export { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from "./MobilityModuleEmailScenarioContentSpecs";

export type { MobilityLocationDataFragment } from "./MobilityLocationData";

export { MobilityLocationDataFragmentDoc } from "./MobilityLocationData";

export type {
  MobilityListData_MobilityAdditionalInfo_Fragment,
  MobilityListData_MobilityAddon_Fragment,
  MobilityListDataFragment,
} from "./MobilityListData";

export { MobilityListDataFragmentDoc } from "./MobilityListData";

export type { MobilityLeadDataFragment } from "./MobilityLeadData";

export { MobilityLeadDataFragmentDoc } from "./MobilityLeadData";

export type { MobilityInventorySpecsFragment } from "./MobilityInventorySpecs";

export { MobilityInventorySpecsFragmentDoc } from "./MobilityInventorySpecs";

export type { MobilityInventoryPublicSpecsFragment } from "./MobilityInventoryPublicSpecs";

export { MobilityInventoryPublicSpecsFragmentDoc } from "./MobilityInventoryPublicSpecs";

export type { MobilityInventoryOptionFragment } from "./MobilityInventoryOption";

export { MobilityInventoryOptionFragmentDoc } from "./MobilityInventoryOption";

export type { MobilityHomeDeliveryDataFragment } from "./MobilityHomeDeliveryData";

export { MobilityHomeDeliveryDataFragmentDoc } from "./MobilityHomeDeliveryData";

export type { MobilityEmailContentDataFragment } from "./MobilityEmailContentData";

export { MobilityEmailContentDataFragmentDoc } from "./MobilityEmailContentData";

export type {
  MobilityData_MobilityAdditionalInfo_Fragment,
  MobilityData_MobilityAddon_Fragment,
  MobilityDataFragment,
} from "./MobilityData";

export { MobilityDataFragmentDoc } from "./MobilityData";

export type { MobilityCustomerEmailContentDataFragment } from "./MobilityCustomerEmailContentData";

export { MobilityCustomerEmailContentDataFragmentDoc } from "./MobilityCustomerEmailContentData";

export type { MobilityBookingLocationPickupDataFragment } from "./MobilityBookingLocationPickupData";

export { MobilityBookingLocationPickupDataFragmentDoc } from "./MobilityBookingLocationPickupData";

export type { MobilityBookingLocationHomeDataFragment } from "./MobilityBookingLocationHomeData";

export { MobilityBookingLocationHomeDataFragmentDoc } from "./MobilityBookingLocationHomeData";

export type { MobilityBookingDetailsDataFragment } from "./MobilityBookingDetailsData";

export { MobilityBookingDetailsDataFragmentDoc } from "./MobilityBookingDetailsData";

export type { MobilityApplicationSpecsFragment } from "./MobilityApplicationSpecs";

export { MobilityApplicationSpecsFragmentDoc } from "./MobilityApplicationSpecs";

export type { MobilityApplicationModuleDataFragment } from "./MobilityApplicationModuleData";

export { MobilityApplicationModuleDataFragmentDoc } from "./MobilityApplicationModuleData";

export type { MobilityApplicationExpiredFragment } from "./MobilityApplicationExpired";

export { MobilityApplicationExpiredFragmentDoc } from "./MobilityApplicationExpired";

export type { MobilityApplicationEntrypointSpecsFragment } from "./MobilityApplicationEntrypointSpecs";

export { MobilityApplicationEntrypointSpecsFragmentDoc } from "./MobilityApplicationEntrypointSpecs";

export type { MobilityApplicationEntrypointContextDataFragment } from "./MobilityApplicationEntrypointContextData";

export { MobilityApplicationEntrypointContextDataFragmentDoc } from "./MobilityApplicationEntrypointContextData";

export type { MobilityApplicationDataFragment } from "./MobilityApplicationData";

export { MobilityApplicationDataFragmentDoc } from "./MobilityApplicationData";

export type { MobilityAddonDataFragment } from "./MobilityAddonData";

export { MobilityAddonDataFragmentDoc } from "./MobilityAddonData";

export type { MobilityAdditionalInfoDataFragment } from "./MobilityAdditionalInfoData";

export { MobilityAdditionalInfoDataFragmentDoc } from "./MobilityAdditionalInfoData";

export type {
  MenuItemSpecs_MenuCustomPathItem_Fragment,
  MenuItemSpecs_MenuEndpointItem_Fragment,
  MenuItemSpecs_MenuLogoutActionItem_Fragment,
  MenuItemSpecsFragment,
} from "./MenuItemSpecs";

export { MenuItemSpecsFragmentDoc } from "./MenuItemSpecs";

export type { MatrixSpecsFragment } from "./MatrixSpecs";

export { MatrixSpecsFragmentDoc } from "./MatrixSpecs";

export type { MatrixDataFragment } from "./MatrixData";

export { MatrixDataFragmentDoc } from "./MatrixData";

export type { MarketingPlatformsAgreedSpecsFragment } from "./MarketingPlatformsAgreedSpecs";

export { MarketingPlatformsAgreedSpecsFragmentDoc } from "./MarketingPlatformsAgreedSpecs";

export type { MarketingPlatformSpecsFragment } from "./MarketingPlatformSpecs";

export { MarketingPlatformSpecsFragmentDoc } from "./MarketingPlatformSpecs";

export type { MarketingModuleWithPermissionsSpecsFragment } from "./MarketingModuleWithPermissionsSpecs";

export { MarketingModuleWithPermissionsSpecsFragmentDoc } from "./MarketingModuleWithPermissionsSpecs";

export type { MarketingModuleSpecsFragment } from "./MarketingModuleSpecs";

export { MarketingModuleSpecsFragmentDoc } from "./MarketingModuleSpecs";

export type { MaintenanceUpdateFragment } from "./MaintenanceUpdate";

export { MaintenanceUpdateFragmentDoc } from "./MaintenanceUpdate";

export type { MaintenanceModuleWithPermissionsSpecsFragment } from "./MaintenanceModuleWithPermissionsSpecs";

export { MaintenanceModuleWithPermissionsSpecsFragmentDoc } from "./MaintenanceModuleWithPermissionsSpecs";

export type { MaintenanceModuleSpecsFragment } from "./MaintenanceModuleSpecs";

export { MaintenanceModuleSpecsFragmentDoc } from "./MaintenanceModuleSpecs";

export type { MainDetailsSalesOfferSpecsFragment } from "./MainDetailsSalesOfferSpecs";

export { MainDetailsSalesOfferSpecsFragmentDoc } from "./MainDetailsSalesOfferSpecs";

export type { LocalVariantsListDataFragment } from "./LocalVariantsListData";

export { LocalVariantsListDataFragmentDoc } from "./LocalVariantsListData";

export type { LocalVariantWithPermissionsSpecsFragment } from "./LocalVariantWithPermissionsSpecs";

export { LocalVariantWithPermissionsSpecsFragmentDoc } from "./LocalVariantWithPermissionsSpecs";

export type { LocalVariantSpecsFragment } from "./LocalVariantSpecs";

export { LocalVariantSpecsFragmentDoc } from "./LocalVariantSpecs";

export type { LocalVariantPublicSpecsFragment } from "./LocalVariantPublicSpecs";

export { LocalVariantPublicSpecsFragmentDoc } from "./LocalVariantPublicSpecs";

export type { LocalVariantProductionListSpecsFragment } from "./LocalVariantProductionListSpecs";

export { LocalVariantProductionListSpecsFragmentDoc } from "./LocalVariantProductionListSpecs";

export type { LocalVariantCalculatorSpecsFragment } from "./LocalVariantCalculatorSpecs";

export { LocalVariantCalculatorSpecsFragmentDoc } from "./LocalVariantCalculatorSpecs";

export type { LocalUcclLeasingOnlyDetailsFragment } from "./LocalUcclLeasingOnlyDetails";

export { LocalUcclLeasingOnlyDetailsFragmentDoc } from "./LocalUcclLeasingOnlyDetails";

export type { LocalUcclLeasingDetailsFragment } from "./LocalUcclLeasingDetails";

export { LocalUcclLeasingDetailsFragmentDoc } from "./LocalUcclLeasingDetails";

export type { LocalModelsListDataFragment } from "./LocalModelsListData";

export { LocalModelsListDataFragmentDoc } from "./LocalModelsListData";

export type { LocalModelWithPermissionsSpecsFragment } from "./LocalModelWithPermissionsSpecs";

export { LocalModelWithPermissionsSpecsFragmentDoc } from "./LocalModelWithPermissionsSpecs";

export type { LocalModelSpecsFragment } from "./LocalModelSpecs";

export { LocalModelSpecsFragmentDoc } from "./LocalModelSpecs";

export type { LocalModelPublicSpecsFragment } from "./LocalModelPublicSpecs";

export { LocalModelPublicSpecsFragmentDoc } from "./LocalModelPublicSpecs";

export type { LocalModelCalculatorSpecsFragment } from "./LocalModelCalculatorSpecs";

export { LocalModelCalculatorSpecsFragmentDoc } from "./LocalModelCalculatorSpecs";

export type { LocalMakesListDataFragment } from "./LocalMakesListData";

export { LocalMakesListDataFragmentDoc } from "./LocalMakesListData";

export type { LocalMakeWithPermissionsSpecsFragment } from "./LocalMakeWithPermissionsSpecs";

export { LocalMakeWithPermissionsSpecsFragmentDoc } from "./LocalMakeWithPermissionsSpecs";

export type { LocalMakeSpecsFragment } from "./LocalMakeSpecs";

export { LocalMakeSpecsFragmentDoc } from "./LocalMakeSpecs";

export type { LocalMakePublicSpecsFragment } from "./LocalMakePublicSpecs";

export { LocalMakePublicSpecsFragmentDoc } from "./LocalMakePublicSpecs";

export type { LocalMakeCalculatorSpecsFragment } from "./LocalMakeCalculatorSpecs";

export { LocalMakeCalculatorSpecsFragmentDoc } from "./LocalMakeCalculatorSpecs";

export type { LocalLeasePurchaseDetailsFragment } from "./LocalLeasePurchaseDetails";

export { LocalLeasePurchaseDetailsFragmentDoc } from "./LocalLeasePurchaseDetails";

export type { LocalLeaseDetailsFragment } from "./LocalLeaseDetails";

export { LocalLeaseDetailsFragmentDoc } from "./LocalLeaseDetails";

export type { LocalHirePurchaseWithBalloonGfvDetailsFragment } from "./LocalHirePurchaseWithBalloonGFVDetails";

export { LocalHirePurchaseWithBalloonGfvDetailsFragmentDoc } from "./LocalHirePurchaseWithBalloonGFVDetails";

export type { LocalHirePurchaseWithBalloonDetailsFragment } from "./LocalHirePurchaseWithBalloonDetails";

export { LocalHirePurchaseWithBalloonDetailsFragmentDoc } from "./LocalHirePurchaseWithBalloonDetails";

export type { LocalHirePurchaseDetailsFragment } from "./LocalHirePurchaseDetails";

export { LocalHirePurchaseDetailsFragmentDoc } from "./LocalHirePurchaseDetails";

export type { LocalFittedOptionsSpecsFragment } from "./LocalFittedOptionsSpecs";

export { LocalFittedOptionsSpecsFragmentDoc } from "./LocalFittedOptionsSpecs";

export type { LocalDeferredPrincipalDetailsFragment } from "./LocalDeferredPrincipalDetails";

export { LocalDeferredPrincipalDetailsFragmentDoc } from "./LocalDeferredPrincipalDetails";

export type { LocalCustomerManagementModuleWithPermissionsSpecsFragment } from "./LocalCustomerManagementModuleWithPermissionsSpecs";

export { LocalCustomerManagementModuleWithPermissionsSpecsFragmentDoc } from "./LocalCustomerManagementModuleWithPermissionsSpecs";

export type { LocalCustomerManagementModuleSpecsFragment } from "./LocalCustomerManagementModuleSpecs";

export { LocalCustomerManagementModuleSpecsFragmentDoc } from "./LocalCustomerManagementModuleSpecs";

export type { LocalCustomerManagementModuleKycFieldSpecsFragment } from "./LocalCustomerManagementModuleKycFieldSpecs";

export { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from "./LocalCustomerManagementModuleKycFieldSpecs";

export type {
  LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment,
  LocalCustomerFieldData_LocalCustomerDateField_Fragment,
  LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment,
  LocalCustomerFieldData_LocalCustomerNumberField_Fragment,
  LocalCustomerFieldData_LocalCustomerPhoneField_Fragment,
  LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment,
  LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment,
  LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment,
  LocalCustomerFieldData_LocalCustomerStringField_Fragment,
  LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment,
  LocalCustomerFieldData_LocalCustomerUploadsField_Fragment,
  LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment,
  LocalCustomerFieldDataFragment,
} from "./LocalCustomerFieldData";

export { LocalCustomerFieldDataFragmentDoc } from "./LocalCustomerFieldData";

export type { LocalCustomerDataFragment } from "./LocalCustomerData";

export { LocalCustomerDataFragmentDoc } from "./LocalCustomerData";

export type { LoanSettingsDetailsFragment } from "./LoanSettingsDetails";

export { LoanSettingsDetailsFragmentDoc } from "./LoanSettingsDetails";

export type { ListGiftVoucherDataFragment } from "./ListGiftVoucherData";

export { ListGiftVoucherDataFragmentDoc } from "./ListGiftVoucherData";

export type { LeaseSettingsDetailsFragment } from "./LeaseSettingsDetails";

export { LeaseSettingsDetailsFragmentDoc } from "./LeaseSettingsDetails";

export type { LeadListEndpointSpecsFragment } from "./LeadListEndpointSpecs";

export { LeadListEndpointSpecsFragmentDoc } from "./LeadListEndpointSpecs";

export type { LeadListEndpointContextDataFragment } from "./LeadListEndpointContextData";

export { LeadListEndpointContextDataFragmentDoc } from "./LeadListEndpointContextData";

export type {
  LeadListData_ConfiguratorLead_Fragment,
  LeadListData_EventLead_Fragment,
  LeadListData_FinderLead_Fragment,
  LeadListData_LaunchpadLead_Fragment,
  LeadListData_MobilityLead_Fragment,
  LeadListData_StandardLead_Fragment,
  LeadListDataFragment,
} from "./LeadListData";

export { LeadListDataFragmentDoc } from "./LeadListData";

export type {
  LeadInCustomerList_ConfiguratorLead_Fragment,
  LeadInCustomerList_EventLead_Fragment,
  LeadInCustomerList_FinderLead_Fragment,
  LeadInCustomerList_LaunchpadLead_Fragment,
  LeadInCustomerList_MobilityLead_Fragment,
  LeadInCustomerList_StandardLead_Fragment,
  LeadInCustomerListFragment,
} from "./LeadInCustomerList";

export { LeadInCustomerListFragmentDoc } from "./LeadInCustomerList";

export type {
  LeadData_ConfiguratorLead_Fragment,
  LeadData_EventLead_Fragment,
  LeadData_FinderLead_Fragment,
  LeadData_LaunchpadLead_Fragment,
  LeadData_MobilityLead_Fragment,
  LeadData_StandardLead_Fragment,
  LeadDataFragment,
} from "./LeadData";

export { LeadDataFragmentDoc } from "./LeadData";

export type { LaunchpadModuleSpecsForApplicationFragment } from "./LaunchpadModuleSpecsForApplication";

export { LaunchpadModuleSpecsForApplicationFragmentDoc } from "./LaunchpadModuleSpecsForApplication";

export type { LaunchpadModuleDebugJourneyFragment } from "./LaunchpadModuleDebugJourney";

export { LaunchpadModuleDebugJourneyFragmentDoc } from "./LaunchpadModuleDebugJourney";

export type { LaunchpadLeadDataFragment } from "./LaunchpadLeadData";

export { LaunchpadLeadDataFragmentDoc } from "./LaunchpadLeadData";

export type { LaunchpadApplicationSpecFragment } from "./LaunchpadApplicationSpec";

export { LaunchpadApplicationSpecFragmentDoc } from "./LaunchpadApplicationSpec";

export type { LaunchpadApplicationModuleDebugJourneyFragment } from "./LaunchpadApplicationModuleDebugJourney";

export { LaunchpadApplicationModuleDebugJourneyFragmentDoc } from "./LaunchpadApplicationModuleDebugJourney";

export type { LaunchpadApplicationDataFragment } from "./LaunchpadApplicationData";

export { LaunchpadApplicationDataFragmentDoc } from "./LaunchpadApplicationData";

export type { LaunchpadApplicationConfigurationDataFragment } from "./LaunchpadApplicationConfigurationData";

export { LaunchpadApplicationConfigurationDataFragmentDoc } from "./LaunchpadApplicationConfigurationData";

export type { LaunchPadModuleWithPermissionsSpecsFragment } from "./LaunchPadModuleWithPermissionsSpecs";

export { LaunchPadModuleWithPermissionsSpecsFragmentDoc } from "./LaunchPadModuleWithPermissionsSpecs";

export type { LaunchPadModuleSpecsFragment } from "./LaunchPadModuleSpecs";

export { LaunchPadModuleSpecsFragmentDoc } from "./LaunchPadModuleSpecs";

export type { LaunchPadModuleInDealerSpecsFragment } from "./LaunchPadModuleInDealerSpecs";

export { LaunchPadModuleInDealerSpecsFragmentDoc } from "./LaunchPadModuleInDealerSpecs";

export type { LaunchPadApplicationEntrypointSpecsFragment } from "./LaunchPadApplicationEntrypointSpecs";

export { LaunchPadApplicationEntrypointSpecsFragmentDoc } from "./LaunchPadApplicationEntrypointSpecs";

export type { LaunchPadApplicationEntrypointContextDataFragment } from "./LaunchPadApplicationEntrypointContextData";

export { LaunchPadApplicationEntrypointContextDataFragmentDoc } from "./LaunchPadApplicationEntrypointContextData";

export type { LanguagePackSpecsFragment } from "./LanguagePackSpecs";

export { LanguagePackSpecsFragmentDoc } from "./LanguagePackSpecs";

export type { LanguagePackOptionDataFragment } from "./LanguagePackOptionData";

export { LanguagePackOptionDataFragmentDoc } from "./LanguagePackOptionData";

export type { LanguagePackListDataFragment } from "./LanguagePackListData";

export { LanguagePackListDataFragmentDoc } from "./LanguagePackListData";

export type { LanguagePackContextDataFragment } from "./LanguagePackContextData";

export { LanguagePackContextDataFragmentDoc } from "./LanguagePackContextData";

export type { LabelsPublicDataFragment } from "./LabelsPublicData";

export { LabelsPublicDataFragmentDoc } from "./LabelsPublicData";

export type { LabelsModuleWithPermissionsSpecsFragment } from "./LabelsModuleWithPermissionsSpecs";

export { LabelsModuleWithPermissionsSpecsFragmentDoc } from "./LabelsModuleWithPermissionsSpecs";

export type { LabelsModuleSpecsFragment } from "./LabelsModuleSpecs";

export { LabelsModuleSpecsFragmentDoc } from "./LabelsModuleSpecs";

export type { LabelsDataFragment } from "./LabelsData";

export { LabelsDataFragmentDoc } from "./LabelsData";

export type { KycPresetsSpecFragment } from "./KYCPresetsSpec";

export { KycPresetsSpecFragmentDoc } from "./KYCPresetsSpec";

export type { KycPresetsOptionsDataFragment } from "./KYCPresetsOptionsData";

export { KycPresetsOptionsDataFragmentDoc } from "./KYCPresetsOptionsData";

export type { KycFieldSpecsFragment } from "./KYCFieldSpecs";

export { KycFieldSpecsFragmentDoc } from "./KYCFieldSpecs";

export type { KycExtraSettingsSpecsFragment } from "./KYCExtraSettingsSpecs";

export { KycExtraSettingsSpecsFragmentDoc } from "./KYCExtraSettingsSpecs";

export type { JourneyEventDataFragment } from "./JourneyEventData";

export { JourneyEventDataFragmentDoc } from "./JourneyEventData";

export type { JourneyDraftFlowFragment } from "./JourneyDraftFlow";

export { JourneyDraftFlowFragmentDoc } from "./JourneyDraftFlow";

export type { InventoryVariantOptionSpecsFragment } from "./InventoryVariantOptionSpecs";

export { InventoryVariantOptionSpecsFragmentDoc } from "./InventoryVariantOptionSpecs";

export type { InventorySubModelOptionSpecsFragment } from "./InventorySubModelOptionSpecs";

export { InventorySubModelOptionSpecsFragmentDoc } from "./InventorySubModelOptionSpecs";

export type { InventoryOptionSpecsFragment } from "./InventoryOptionSpecs";

export { InventoryOptionSpecsFragmentDoc } from "./InventoryOptionSpecs";

export type { InventoryModuleOptionSpecsFragment } from "./InventoryModuleOptionSpecs";

export { InventoryModuleOptionSpecsFragmentDoc } from "./InventoryModuleOptionSpecs";

export type { InventoryModelOptionSpecsFragment } from "./InventoryModelOptionSpecs";

export { InventoryModelOptionSpecsFragmentDoc } from "./InventoryModelOptionSpecs";

export type {
  InventoryListData_ConfiguratorInventory_Fragment,
  InventoryListData_MobilityInventory_Fragment,
  InventoryListDataFragment,
} from "./InventoryListData";

export { InventoryListDataFragmentDoc } from "./InventoryListData";

export type {
  InventoryDetailsWithPermissionsData_ConfiguratorInventory_Fragment,
  InventoryDetailsWithPermissionsData_MobilityInventory_Fragment,
  InventoryDetailsWithPermissionsDataFragment,
} from "./InventoryDetailsWithPermissionsData";

export { InventoryDetailsWithPermissionsDataFragmentDoc } from "./InventoryDetailsWithPermissionsData";

export type {
  InventoryDetailsPublicData_ConfiguratorInventory_Fragment,
  InventoryDetailsPublicData_MobilityInventory_Fragment,
  InventoryDetailsPublicDataFragment,
} from "./InventoryDetailsPublicData";

export { InventoryDetailsPublicDataFragmentDoc } from "./InventoryDetailsPublicData";

export type {
  InventoryDetailsData_ConfiguratorInventory_Fragment,
  InventoryDetailsData_MobilityInventory_Fragment,
  InventoryDetailsDataFragment,
} from "./InventoryDetailsData";

export { InventoryDetailsDataFragmentDoc } from "./InventoryDetailsData";

export type {
  InterestRateSettingsDetails_InterestRateFixedSettings_Fragment,
  InterestRateSettingsDetails_InterestRateRangeSettings_Fragment,
  InterestRateSettingsDetails_InterestRateTableSettings_Fragment,
  InterestRateSettingsDetailsFragment,
} from "./InterestRateSettingsDetails";

export { InterestRateSettingsDetailsFragmentDoc } from "./InterestRateSettingsDetails";

export type { InsurerListDataFragment } from "./InsurerListData";

export { InsurerListDataFragmentDoc } from "./InsurerListData";

export type {
  InsurerIntegrationData_EazyInsurerIntegration_Fragment,
  InsurerIntegrationData_EmailInsurerIntegration_Fragment,
  InsurerIntegrationDataFragment,
} from "./InsurerIntegrationData";

export { InsurerIntegrationDataFragmentDoc } from "./InsurerIntegrationData";

export type { InsurerEntrypointContextDataFragment } from "./InsurerEntrypointContextData";

export { InsurerEntrypointContextDataFragmentDoc } from "./InsurerEntrypointContextData";

export type { InsurerDetailsDataFragment } from "./InsurerDetailsData";

export { InsurerDetailsDataFragmentDoc } from "./InsurerDetailsData";

export type { InsuranceSalesOfferSpecsFragment } from "./InsuranceSalesOfferSpecs";

export { InsuranceSalesOfferSpecsFragmentDoc } from "./InsuranceSalesOfferSpecs";

export type {
  InsuranceProductListData_Eazy_Fragment,
  InsuranceProductListData_ErgoLookupTable_Fragment,
  InsuranceProductListDataFragment,
} from "./InsuranceProductListData";

export { InsuranceProductListDataFragmentDoc } from "./InsuranceProductListData";

export type {
  InsuranceProductDetailsWithPermissionsData_Eazy_Fragment,
  InsuranceProductDetailsWithPermissionsData_ErgoLookupTable_Fragment,
  InsuranceProductDetailsWithPermissionsDataFragment,
} from "./InsuranceProductDetailsWithPermissionsData";

export { InsuranceProductDetailsWithPermissionsDataFragmentDoc } from "./InsuranceProductDetailsWithPermissionsData";

export type {
  InsuranceProductDetailsData_Eazy_Fragment,
  InsuranceProductDetailsData_ErgoLookupTable_Fragment,
  InsuranceProductDetailsDataFragment,
} from "./InsuranceProductDetailsData";

export { InsuranceProductDetailsDataFragmentDoc } from "./InsuranceProductDetailsData";

export type {
  InsuranceProductDetails_Eazy_Fragment,
  InsuranceProductDetails_ErgoLookupTable_Fragment,
  InsuranceProductDetailsFragment,
} from "./InsuranceProductDetails";

export { InsuranceProductDetailsFragmentDoc } from "./InsuranceProductDetails";

export type { InsuranceModuleWithPermissionsSpecsFragment } from "./InsuranceModuleWithPermissionsSpecs";

export { InsuranceModuleWithPermissionsSpecsFragmentDoc } from "./InsuranceModuleWithPermissionsSpecs";

export type { InsuranceModuleSpecsFragment } from "./InsuranceModuleSpecs";

export { InsuranceModuleSpecsFragmentDoc } from "./InsuranceModuleSpecs";

export type { ImportExcelSpecsFragment } from "./ImportExcelSpecs";

export { ImportExcelSpecsFragmentDoc } from "./ImportExcelSpecs";

export type { ImageWebPageBlockDataFragment } from "./ImageWebPageBlockData";

export { ImageWebPageBlockDataFragmentDoc } from "./ImageWebPageBlockData";

export type { ImageDescriptionWebPageBlockDataFragment } from "./ImageDescriptionWebPageBlockData";

export { ImageDescriptionWebPageBlockDataFragmentDoc } from "./ImageDescriptionWebPageBlockData";

export type { GuarantorDataFragment } from "./GuarantorData";

export { GuarantorDataFragmentDoc } from "./GuarantorData";

export type { GiftVoucherModuleWithPermissionsSpecsFragment } from "./GiftVoucherModuleWithPermissionsSpecs";

export { GiftVoucherModuleWithPermissionsSpecsFragmentDoc } from "./GiftVoucherModuleWithPermissionsSpecs";

export type { GiftVoucherModuleSpecsFragment } from "./GiftVoucherModuleSpecs";

export { GiftVoucherModuleSpecsFragmentDoc } from "./GiftVoucherModuleSpecs";

export type { GiftVoucherModuleInDealerSpecsFragment } from "./GiftVoucherModuleInDealerSpecs";

export { GiftVoucherModuleInDealerSpecsFragmentDoc } from "./GiftVoucherModuleInDealerSpecs";

export type {
  GiftVoucherModuleEmailDataFragment,
  GiftVoucherModuleEmailContentCustomerSpecsFragment,
  GiftVoucherModuleEmailContentSalesPersonSpecsFragment,
  GiftVoucherModuleEmailContentsSpecsFragment,
} from "./GiftVoucherModuleEmailContentsSpecs";

export {
  GiftVoucherModuleEmailDataFragmentDoc,
  GiftVoucherModuleEmailContentSalesPersonSpecsFragmentDoc,
  GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc,
  GiftVoucherModuleEmailContentsSpecsFragmentDoc,
} from "./GiftVoucherModuleEmailContentsSpecs";

export type { GiftVoucherJourneyContextDataFragment } from "./GiftVoucherJourneyData";

export { GiftVoucherJourneyContextDataFragmentDoc } from "./GiftVoucherJourneyData";

export type { GiftVoucherDraftFlowDataFragment } from "./GiftVoucherDraftFlowData";

export { GiftVoucherDraftFlowDataFragmentDoc } from "./GiftVoucherDraftFlowData";

export type {
  GiftVoucherDataFragment,
  GiftVoucherSpecsFragment,
} from "./GiftVoucherData";

export {
  GiftVoucherDataFragmentDoc,
  GiftVoucherSpecsFragmentDoc,
} from "./GiftVoucherData";

export type { GiftVoucherCodeDataFragment } from "./GiftVoucherCodeData";

export { GiftVoucherCodeDataFragmentDoc } from "./GiftVoucherCodeData";

export type { GiftPromoTypeDataFragment } from "./GiftPromoTypeData";

export { GiftPromoTypeDataFragmentDoc } from "./GiftPromoTypeData";

export type {
  GetModulesList_AdyenPaymentModule_Fragment,
  GetModulesList_AppointmentModule_Fragment,
  GetModulesList_AutoplayModule_Fragment,
  GetModulesList_BankModule_Fragment,
  GetModulesList_BasicSigningModule_Fragment,
  GetModulesList_CapModule_Fragment,
  GetModulesList_ConfiguratorModule_Fragment,
  GetModulesList_ConsentsAndDeclarationsModule_Fragment,
  GetModulesList_CtsModule_Fragment,
  GetModulesList_DocusignModule_Fragment,
  GetModulesList_EventApplicationModule_Fragment,
  GetModulesList_FinderApplicationPrivateModule_Fragment,
  GetModulesList_FinderApplicationPublicModule_Fragment,
  GetModulesList_FinderVehicleManagementModule_Fragment,
  GetModulesList_FiservPaymentModule_Fragment,
  GetModulesList_GiftVoucherModule_Fragment,
  GetModulesList_InsuranceModule_Fragment,
  GetModulesList_LabelsModule_Fragment,
  GetModulesList_LaunchPadModule_Fragment,
  GetModulesList_LocalCustomerManagementModule_Fragment,
  GetModulesList_MaintenanceModule_Fragment,
  GetModulesList_MarketingModule_Fragment,
  GetModulesList_MobilityModule_Fragment,
  GetModulesList_MyInfoModule_Fragment,
  GetModulesList_NamirialSigningModule_Fragment,
  GetModulesList_OidcModule_Fragment,
  GetModulesList_PayGatePaymentModule_Fragment,
  GetModulesList_PorscheIdModule_Fragment,
  GetModulesList_PorscheMasterDataModule_Fragment,
  GetModulesList_PorschePaymentModule_Fragment,
  GetModulesList_PorscheRetainModule_Fragment,
  GetModulesList_PromoCodeModule_Fragment,
  GetModulesList_SalesControlBoardModule_Fragment,
  GetModulesList_SalesOfferModule_Fragment,
  GetModulesList_SimpleVehicleManagementModule_Fragment,
  GetModulesList_StandardApplicationModule_Fragment,
  GetModulesList_TradeInModule_Fragment,
  GetModulesList_TtbPaymentModule_Fragment,
  GetModulesList_UserlikeChatbotModule_Fragment,
  GetModulesList_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  GetModulesList_VisitAppointmentModule_Fragment,
  GetModulesList_WebsiteModule_Fragment,
  GetModulesList_WhatsappLiveChatModule_Fragment,
  GetModulesListFragment,
} from "./GetModulesList";

export { GetModulesListFragmentDoc } from "./GetModulesList";

export type { FlexibleDiscountDataFragment } from "./FlexibleDiscountData";

export { FlexibleDiscountDataFragmentDoc } from "./FlexibleDiscountData";

export type { FiservPaymentSettingsSpecFragment } from "./FiservPaymentSettingsSpec";

export { FiservPaymentSettingsSpecFragmentDoc } from "./FiservPaymentSettingsSpec";

export type { FiservPaymentModuleWithPermissionsSpecsFragment } from "./FiservPaymentModuleWithPermissionsSpecs";

export { FiservPaymentModuleWithPermissionsSpecsFragmentDoc } from "./FiservPaymentModuleWithPermissionsSpecs";

export type { FiservPaymentModuleSpecsFragment } from "./FiservPaymentModuleSpecs";

export { FiservPaymentModuleSpecsFragmentDoc } from "./FiservPaymentModuleSpecs";

export type { FinderVehiclesListDataFragment } from "./FinderVehiclesListData";

export { FinderVehiclesListDataFragmentDoc } from "./FinderVehiclesListData";

export type { FinderVehicleWithPermissionsSpecsFragment } from "./FinderVehicleWithPermissionsSpecs";

export { FinderVehicleWithPermissionsSpecsFragmentDoc } from "./FinderVehicleWithPermissionsSpecs";

export type { FinderVehicleSpecsFragment } from "./FinderVehicleSpecs";

export { FinderVehicleSpecsFragmentDoc } from "./FinderVehicleSpecs";

export type { FinderVehicleSelectionDataFragment } from "./FinderVehicleSelectionData";

export { FinderVehicleSelectionDataFragmentDoc } from "./FinderVehicleSelectionData";

export type { FinderVehicleManagementModuleWithPermissionsSpecsFragment } from "./FinderVehicleManagementModuleWithPermissionsSpecs";

export { FinderVehicleManagementModuleWithPermissionsSpecsFragmentDoc } from "./FinderVehicleManagementModuleWithPermissionsSpecs";

export type { FinderVehicleManagementModuleSpecsFragment } from "./FinderVehicleManagementModuleSpecs";

export { FinderVehicleManagementModuleSpecsFragmentDoc } from "./FinderVehicleManagementModuleSpecs";

export type { FinderVehicleDataFragment } from "./FinderVehicleData";

export { FinderVehicleDataFragmentDoc } from "./FinderVehicleData";

export type { FinderVehicleCalculatorDataFragment } from "./FinderVehicleCalculatorData";

export { FinderVehicleCalculatorDataFragmentDoc } from "./FinderVehicleCalculatorData";

export type { FinderLeadDataFragment } from "./FinderLeadData";

export { FinderLeadDataFragmentDoc } from "./FinderLeadData";

export type { FinderConfigurationDataFragment } from "./FinderConfigurationData";

export { FinderConfigurationDataFragmentDoc } from "./FinderConfigurationData";

export type { FinderApplicationSpecsFragment } from "./FinderApplicationSpecs";

export { FinderApplicationSpecsFragmentDoc } from "./FinderApplicationSpecs";

export type { FinderApplicationPublicModuleWithPermissionsSpecsFragment } from "./FinderApplicationPublicModuleWithPermissionsSpecs";

export { FinderApplicationPublicModuleWithPermissionsSpecsFragmentDoc } from "./FinderApplicationPublicModuleWithPermissionsSpecs";

export type { FinderApplicationPublicModuleSpecsForApplicationFragment } from "./FinderApplicationPublicModuleSpecsForApplication";

export { FinderApplicationPublicModuleSpecsForApplicationFragmentDoc } from "./FinderApplicationPublicModuleSpecsForApplication";

export type { FinderApplicationPublicModuleSpecsFragment } from "./FinderApplicationPublicModuleSpecs";

export { FinderApplicationPublicModuleSpecsFragmentDoc } from "./FinderApplicationPublicModuleSpecs";

export type { FinderApplicationPublicModuleInDealerSpecsFragment } from "./FinderApplicationPublicModuleInDealerSpecs";

export { FinderApplicationPublicModuleInDealerSpecsFragmentDoc } from "./FinderApplicationPublicModuleInDealerSpecs";

export type { FinderApplicationPublicModuleDebugJourneyFragment } from "./FinderApplicationPublicModuleDebugJourney";

export { FinderApplicationPublicModuleDebugJourneyFragmentDoc } from "./FinderApplicationPublicModuleDebugJourney";

export type { FinderApplicationPublicModuleDataFragment } from "./FinderApplicationPublicModuleData";

export { FinderApplicationPublicModuleDataFragmentDoc } from "./FinderApplicationPublicModuleData";

export type { FinderApplicationPublicAccessEntrypointSpecsFragment } from "./FinderApplicationPublicAccessEntrypointSpecs";

export { FinderApplicationPublicAccessEntrypointSpecsFragmentDoc } from "./FinderApplicationPublicAccessEntrypointSpecs";

export type { FinderApplicationPublicAccessEntrypointContextDataFragment } from "./FinderApplicationPublicAccessEntrypointContextData";

export { FinderApplicationPublicAccessEntrypointContextDataFragmentDoc } from "./FinderApplicationPublicAccessEntrypointContextData";

export type { FinderApplicationPrivateModuleWithPermissionsSpecsFragment } from "./FinderApplicationPrivateModuleWithPermissionsSpecs";

export { FinderApplicationPrivateModuleWithPermissionsSpecsFragmentDoc } from "./FinderApplicationPrivateModuleWithPermissionsSpecs";

export type { FinderApplicationPrivateModuleSpecsForApplicationFragment } from "./FinderApplicationPrivateModuleSpecsForApplication";

export { FinderApplicationPrivateModuleSpecsForApplicationFragmentDoc } from "./FinderApplicationPrivateModuleSpecsForApplication";

export type { FinderApplicationPrivateModuleSpecsFragment } from "./FinderApplicationPrivateModuleSpecs";

export { FinderApplicationPrivateModuleSpecsFragmentDoc } from "./FinderApplicationPrivateModuleSpecs";

export type { FinderApplicationPrivateModuleInDealerSpecsFragment } from "./FinderApplicationPrivateModuleInDealerSpecs";

export { FinderApplicationPrivateModuleInDealerSpecsFragmentDoc } from "./FinderApplicationPrivateModuleInDealerSpecs";

export type { FinderApplicationPrivateModuleDebugJourneyFragment } from "./FinderApplicationPrivateModuleDebugJourney";

export { FinderApplicationPrivateModuleDebugJourneyFragmentDoc } from "./FinderApplicationPrivateModuleDebugJourney";

export type { FinderApplicationPrivateModuleDataFragment } from "./FinderApplicationPrivateModuleData";

export { FinderApplicationPrivateModuleDataFragmentDoc } from "./FinderApplicationPrivateModuleData";

export type { FinderApplicationModuleEmailContentSpecsFragment } from "./FinderApplicationModuleEmailContentSpecs";

export { FinderApplicationModuleEmailContentSpecsFragmentDoc } from "./FinderApplicationModuleEmailContentSpecs";

export type { FinderApplicationEntrypointSpecsFragment } from "./FinderApplicationEntrypointSpecs";

export { FinderApplicationEntrypointSpecsFragmentDoc } from "./FinderApplicationEntrypointSpecs";

export type { FinderApplicationEntrypointContextDataFragment } from "./FinderApplicationEntrypointContextData";

export { FinderApplicationEntrypointContextDataFragmentDoc } from "./FinderApplicationEntrypointContextData";

export type { FinderApplicationDataFragment } from "./FinderApplicationData";

export { FinderApplicationDataFragmentDoc } from "./FinderApplicationData";

export type { FinanceSalesOfferSpecsFragment } from "./FinanceSalesOfferSpecs";

export { FinanceSalesOfferSpecsFragmentDoc } from "./FinanceSalesOfferSpecs";

export type {
  FinanceProductListData_LocalDeferredPrincipal_Fragment,
  FinanceProductListData_LocalHirePurchase_Fragment,
  FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment,
  FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment,
  FinanceProductListData_LocalLease_Fragment,
  FinanceProductListData_LocalLeasePurchase_Fragment,
  FinanceProductListData_LocalUcclLeasing_Fragment,
  FinanceProductListDataFragment,
} from "./FinanceProductListData";

export { FinanceProductListDataFragmentDoc } from "./FinanceProductListData";

export type {
  FinanceProductDetailsWithPermissionsData_LocalDeferredPrincipal_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalHirePurchase_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalHirePurchaseWithBalloon_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalHirePurchaseWithBalloonGfv_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalLease_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalLeasePurchase_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalUcclLeasing_Fragment,
  FinanceProductDetailsWithPermissionsDataFragment,
} from "./FinanceProductDetailsWithPermissionsData";

export { FinanceProductDetailsWithPermissionsDataFragmentDoc } from "./FinanceProductDetailsWithPermissionsData";

export type {
  FinanceProductDetailsData_LocalDeferredPrincipal_Fragment,
  FinanceProductDetailsData_LocalHirePurchase_Fragment,
  FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment,
  FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment,
  FinanceProductDetailsData_LocalLease_Fragment,
  FinanceProductDetailsData_LocalLeasePurchase_Fragment,
  FinanceProductDetailsData_LocalUcclLeasing_Fragment,
  FinanceProductDetailsDataFragment,
} from "./FinanceProductDetailsData";

export { FinanceProductDetailsDataFragmentDoc } from "./FinanceProductDetailsData";

export type {
  FinanceProductDetails_LocalDeferredPrincipal_Fragment,
  FinanceProductDetails_LocalHirePurchase_Fragment,
  FinanceProductDetails_LocalHirePurchaseWithBalloon_Fragment,
  FinanceProductDetails_LocalHirePurchaseWithBalloonGfv_Fragment,
  FinanceProductDetails_LocalLease_Fragment,
  FinanceProductDetails_LocalLeasePurchase_Fragment,
  FinanceProductDetails_LocalUcclLeasing_Fragment,
  FinanceProductDetailsFragment,
} from "./FinanceProductDetails";

export { FinanceProductDetailsFragmentDoc } from "./FinanceProductDetails";

export type { FeatureValueDataFragment } from "./FeatureValueData";

export { FeatureValueDataFragmentDoc } from "./FeatureValueData";

export type {
  EventModuleData_AdyenPaymentModule_Fragment,
  EventModuleData_AppointmentModule_Fragment,
  EventModuleData_AutoplayModule_Fragment,
  EventModuleData_BankModule_Fragment,
  EventModuleData_BasicSigningModule_Fragment,
  EventModuleData_CapModule_Fragment,
  EventModuleData_ConfiguratorModule_Fragment,
  EventModuleData_ConsentsAndDeclarationsModule_Fragment,
  EventModuleData_CtsModule_Fragment,
  EventModuleData_DocusignModule_Fragment,
  EventModuleData_EventApplicationModule_Fragment,
  EventModuleData_FinderApplicationPrivateModule_Fragment,
  EventModuleData_FinderApplicationPublicModule_Fragment,
  EventModuleData_FinderVehicleManagementModule_Fragment,
  EventModuleData_FiservPaymentModule_Fragment,
  EventModuleData_GiftVoucherModule_Fragment,
  EventModuleData_InsuranceModule_Fragment,
  EventModuleData_LabelsModule_Fragment,
  EventModuleData_LaunchPadModule_Fragment,
  EventModuleData_LocalCustomerManagementModule_Fragment,
  EventModuleData_MaintenanceModule_Fragment,
  EventModuleData_MarketingModule_Fragment,
  EventModuleData_MobilityModule_Fragment,
  EventModuleData_MyInfoModule_Fragment,
  EventModuleData_NamirialSigningModule_Fragment,
  EventModuleData_OidcModule_Fragment,
  EventModuleData_PayGatePaymentModule_Fragment,
  EventModuleData_PorscheIdModule_Fragment,
  EventModuleData_PorscheMasterDataModule_Fragment,
  EventModuleData_PorschePaymentModule_Fragment,
  EventModuleData_PorscheRetainModule_Fragment,
  EventModuleData_PromoCodeModule_Fragment,
  EventModuleData_SalesControlBoardModule_Fragment,
  EventModuleData_SalesOfferModule_Fragment,
  EventModuleData_SimpleVehicleManagementModule_Fragment,
  EventModuleData_StandardApplicationModule_Fragment,
  EventModuleData_TradeInModule_Fragment,
  EventModuleData_TtbPaymentModule_Fragment,
  EventModuleData_UserlikeChatbotModule_Fragment,
  EventModuleData_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  EventModuleData_VisitAppointmentModule_Fragment,
  EventModuleData_WebsiteModule_Fragment,
  EventModuleData_WhatsappLiveChatModule_Fragment,
  EventModuleDataFragment,
} from "./EventModuleData";

export { EventModuleDataFragmentDoc } from "./EventModuleData";

export type { EventListDataFragment } from "./EventListData";

export { EventListDataFragmentDoc } from "./EventListData";

export type { EventLeadDataFragment } from "./EventLeadData";

export { EventLeadDataFragmentDoc } from "./EventLeadData";

export type { EventDataFragment } from "./EventData";

export { EventDataFragmentDoc } from "./EventData";

export type { EventApplicationSpecFragment } from "./EventApplicationSpec";

export { EventApplicationSpecFragmentDoc } from "./EventApplicationSpec";

export type { EventApplicationModuleWithPermissionsSpecsFragment } from "./EventApplicationModuleWithPermissionsSpecs";

export { EventApplicationModuleWithPermissionsSpecsFragmentDoc } from "./EventApplicationModuleWithPermissionsSpecs";

export type { EventApplicationModuleSpecsForApplicationFragment } from "./EventApplicationModuleSpecsForApplication";

export { EventApplicationModuleSpecsForApplicationFragmentDoc } from "./EventApplicationModuleSpecsForApplication";

export type { EventApplicationModuleSpecsFragment } from "./EventApplicationModuleSpecs";

export { EventApplicationModuleSpecsFragmentDoc } from "./EventApplicationModuleSpecs";

export type { EventApplicationModuleInDealerSpecsFragment } from "./EventApplicationModuleInDealerSpecs";

export { EventApplicationModuleInDealerSpecsFragmentDoc } from "./EventApplicationModuleInDealerSpecs";

export type {
  EventApplicationModuleEmailContentSpecsFragment,
  EventEmailContentSpecsFragment,
} from "./EventApplicationModuleEmailContentSpecs";

export {
  EventApplicationModuleEmailContentSpecsFragmentDoc,
  EventEmailContentSpecsFragmentDoc,
} from "./EventApplicationModuleEmailContentSpecs";

export type { EventApplicationModuleDebugJourneyFragment } from "./EventApplicationModuleDebugJourney";

export { EventApplicationModuleDebugJourneyFragmentDoc } from "./EventApplicationModuleDebugJourney";

export type { EventApplicationEntrypointSpecsFragment } from "./EventApplicationEntrypointSpecs";

export { EventApplicationEntrypointSpecsFragmentDoc } from "./EventApplicationEntrypointSpecs";

export type { EventApplicationEntrypointContextDataFragment } from "./EventApplicationEntrypointContextData";

export { EventApplicationEntrypointContextDataFragmentDoc } from "./EventApplicationEntrypointContextData";

export type { EventApplicationDataFragment } from "./EventApplicationData";

export { EventApplicationDataFragmentDoc } from "./EventApplicationData";

export type { EventApplicationConfigurationDataFragment } from "./EventApplicationConfigurationData";

export { EventApplicationConfigurationDataFragmentDoc } from "./EventApplicationConfigurationData";

export type { ErgoLookupTableSettingsDetailsFragment } from "./ErgoLookupTableSettingDetails";

export { ErgoLookupTableSettingsDetailsFragmentDoc } from "./ErgoLookupTableSettingDetails";

export type { ErgoLookupTableDetailsFragment } from "./ErgoLookupTableDetails";

export { ErgoLookupTableDetailsFragmentDoc } from "./ErgoLookupTableDetails";

export type { EntrypointFinderApplicationPublicModuleFragment } from "./EntrypointFinderApplicationPublicModule";

export { EntrypointFinderApplicationPublicModuleFragmentDoc } from "./EntrypointFinderApplicationPublicModule";

export type { EntrypointFinderApplicationPrivateModuleFragment } from "./EntrypointFinderApplicationPrivateModule";

export { EntrypointFinderApplicationPrivateModuleFragmentDoc } from "./EntrypointFinderApplicationPrivateModule";

export type {
  EndpointListSpecs_ApplicationListEndpoint_Fragment,
  EndpointListSpecs_ConfiguratorApplicationEntrypoint_Fragment,
  EndpointListSpecs_CustomerListEndpoint_Fragment,
  EndpointListSpecs_DummyPrivatePageEndpoint_Fragment,
  EndpointListSpecs_DummyWelcomePageEndpoint_Fragment,
  EndpointListSpecs_EventApplicationEntrypoint_Fragment,
  EndpointListSpecs_FinderApplicationEntrypoint_Fragment,
  EndpointListSpecs_FinderApplicationPublicAccessEntrypoint_Fragment,
  EndpointListSpecs_LaunchPadApplicationEntrypoint_Fragment,
  EndpointListSpecs_LeadListEndpoint_Fragment,
  EndpointListSpecs_MobilityApplicationEntrypoint_Fragment,
  EndpointListSpecs_StandardApplicationEntrypoint_Fragment,
  EndpointListSpecs_StandardApplicationPublicAccessEntrypoint_Fragment,
  EndpointListSpecs_WebPageEndpoint_Fragment,
  EndpointListSpecsFragment,
} from "./EndpointListSpecs";

export { EndpointListSpecsFragmentDoc } from "./EndpointListSpecs";

export type {
  EndpointContextData_ApplicationListEndpoint_Fragment,
  EndpointContextData_ConfiguratorApplicationEntrypoint_Fragment,
  EndpointContextData_CustomerListEndpoint_Fragment,
  EndpointContextData_DummyPrivatePageEndpoint_Fragment,
  EndpointContextData_DummyWelcomePageEndpoint_Fragment,
  EndpointContextData_EventApplicationEntrypoint_Fragment,
  EndpointContextData_FinderApplicationEntrypoint_Fragment,
  EndpointContextData_FinderApplicationPublicAccessEntrypoint_Fragment,
  EndpointContextData_LaunchPadApplicationEntrypoint_Fragment,
  EndpointContextData_LeadListEndpoint_Fragment,
  EndpointContextData_MobilityApplicationEntrypoint_Fragment,
  EndpointContextData_StandardApplicationEntrypoint_Fragment,
  EndpointContextData_StandardApplicationPublicAccessEntrypoint_Fragment,
  EndpointContextData_WebPageEndpoint_Fragment,
  EndpointContextDataFragment,
} from "./EndpointContextData";

export { EndpointContextDataFragmentDoc } from "./EndpointContextData";

export type { EdmSocialMediaDataFragment } from "./EdmSocialMediaData";

export { EdmSocialMediaDataFragmentDoc } from "./EdmSocialMediaData";

export type { EdmEmailFooterPublicDataFragment } from "./EdmEmailFooterPublicData";

export { EdmEmailFooterPublicDataFragmentDoc } from "./EdmEmailFooterPublicData";

export type { EazyDetailsFragment } from "./EazyDetails";

export { EazyDetailsFragmentDoc } from "./EazyDetails";

export type { DummyWelcomePageEndpointSpecsFragment } from "./DummyWelcomePageEndpointSpecs";

export { DummyWelcomePageEndpointSpecsFragmentDoc } from "./DummyWelcomePageEndpointSpecs";

export type { DummyPrivatePageEndpointSpecsFragment } from "./DummyPrivatePageEndpointSpecs";

export { DummyPrivatePageEndpointSpecsFragmentDoc } from "./DummyPrivatePageEndpointSpecs";

export type { DummyPrivatePageEndpointContextDataFragment } from "./DummyPrivatePageEndpointContextData";

export { DummyPrivatePageEndpointContextDataFragmentDoc } from "./DummyPrivatePageEndpointContextData";

export type { DraftFlowConfigurationSpecFragment } from "./DraftFlowConfigurationSpec";

export { DraftFlowConfigurationSpecFragmentDoc } from "./DraftFlowConfigurationSpec";

export type {
  DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment,
  DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment,
  DownPaymentSettingsDetailsFragment,
} from "./DownPaymentSettingsDetails";

export { DownPaymentSettingsDetailsFragmentDoc } from "./DownPaymentSettingsDetails";

export type { DocusignSettingDataFragment } from "./DocusignSettingData";

export { DocusignSettingDataFragmentDoc } from "./DocusignSettingData";

export type { DocusignModuleSpecsWithPermissionSpecsFragment } from "./DocusignModuleSpecsWithPermissionSpecs";

export { DocusignModuleSpecsWithPermissionSpecsFragmentDoc } from "./DocusignModuleSpecsWithPermissionSpecs";

export type { DocusignModuleSpecsFragment } from "./DocusignModuleSpecs";

export { DocusignModuleSpecsFragmentDoc } from "./DocusignModuleSpecs";

export type { DiscountPromoTypeDataFragment } from "./DiscountPromoTypeData";

export { DiscountPromoTypeDataFragmentDoc } from "./DiscountPromoTypeData";

export type {
  DiscountCodeData_GiftVoucher_Fragment,
  DiscountCodeData_PromoCode_Fragment,
  DiscountCodeDataFragment,
} from "./DiscountCodeData";

export { DiscountCodeDataFragmentDoc } from "./DiscountCodeData";

export type {
  DepositSettingsDetails_DepositRangeSettings_Fragment,
  DepositSettingsDetails_DepositTableSettings_Fragment,
  DepositSettingsDetailsFragment,
} from "./DepositSettingsDetails";

export { DepositSettingsDetailsFragmentDoc } from "./DepositSettingsDetails";

export type { DepositSalesOfferSpecsFragment } from "./DepositSalesOfferSpecs";

export { DepositSalesOfferSpecsFragmentDoc } from "./DepositSalesOfferSpecs";

export type { DepositAmountDataFragment } from "./DepositAmountData";

export { DepositAmountDataFragmentDoc } from "./DepositAmountData";

export type { DebugJourneyDataFragment } from "./DebugJourneyData";

export { DebugJourneyDataFragmentDoc } from "./DebugJourneyData";

export type {
  DealershipSettingSpecData_DealershipMyInfoSetting_Fragment,
  DealershipSettingSpecData_DealershipPaymentSetting_Fragment,
  DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment,
  DealershipSettingSpecDataFragment,
} from "./DealershipSettingSpecData";

export { DealershipSettingSpecDataFragmentDoc } from "./DealershipSettingSpecData";

export type {
  DealershipSettingDetailsData_DealershipMyInfoSetting_Fragment,
  DealershipSettingDetailsData_DealershipPaymentSetting_Fragment,
  DealershipSettingDetailsData_DealershipPublicSalesPerson_Fragment,
  DealershipSettingDetailsDataFragment,
} from "./DealershipSettingDetailsData";

export { DealershipSettingDetailsDataFragmentDoc } from "./DealershipSettingDetailsData";

export type { DealerWithPermissionsFragmentFragment } from "./DealerWithPermissionsFragment";

export { DealerWithPermissionsFragmentFragmentDoc } from "./DealerWithPermissionsFragment";

export type { DealerVehiclesSpecsFragment } from "./DealerVehiclesSpecs";

export { DealerVehiclesSpecsFragmentDoc } from "./DealerVehiclesSpecs";

export type { DealerUploadedFileWithPreviewDataFragment } from "./DealerUploadedFileWithPreview";

export { DealerUploadedFileWithPreviewDataFragmentDoc } from "./DealerUploadedFileWithPreview";

export type { DealerTranslatedStringSettingDataFragment } from "./DealerTranslatedStringData";

export { DealerTranslatedStringSettingDataFragmentDoc } from "./DealerTranslatedStringData";

export type { DealerSocialMediaFragmentFragment } from "./DealerSocialMediaFragment";

export { DealerSocialMediaFragmentFragmentDoc } from "./DealerSocialMediaFragment";

export type { DealerPriceDisclaimerDataFragment } from "./DealerPriceDisclaimerData";

export { DealerPriceDisclaimerDataFragmentDoc } from "./DealerPriceDisclaimerData";

export type { DealerDisclaimersConfiguratorDataFragment } from "./DealerPriceDisclaimerConfiguratorData";

export { DealerDisclaimersConfiguratorDataFragmentDoc } from "./DealerPriceDisclaimerConfiguratorData";

export type { DealerOptionsDataFragment } from "./DealerOptionsData";

export { DealerOptionsDataFragmentDoc } from "./DealerOptionsData";

export type { DealerMarketDataFragment } from "./DealerMarketData";

export { DealerMarketDataFragmentDoc } from "./DealerMarketData";

export type { DealerListDataFragment } from "./DealerListData";

export { DealerListDataFragmentDoc } from "./DealerListData";

export type { DealerJourneyDataFragment } from "./DealerJourneyData";

export { DealerJourneyDataFragmentDoc } from "./DealerJourneyData";

export type { DealerIntegrationDetailsFragmentFragment } from "./DealerIntegrationDetailsFragment";

export { DealerIntegrationDetailsFragmentFragmentDoc } from "./DealerIntegrationDetailsFragment";

export type {
  DealerIntDataFragment,
  DealerObjectIdDataFragment,
  DealerFloatDataFragment,
} from "./DealerIntData";

export {
  DealerIntDataFragmentDoc,
  DealerObjectIdDataFragmentDoc,
  DealerFloatDataFragmentDoc,
} from "./DealerIntData";

export type { DealerInsuranceProductsSpecsFragment } from "./DealerInsuranceProductsSpecs";

export { DealerInsuranceProductsSpecsFragmentDoc } from "./DealerInsuranceProductsSpecs";

export type { DealerFragmentFragment } from "./DealerFragment";

export { DealerFragmentFragmentDoc } from "./DealerFragment";

export type { DealerFinanceProductsSpecsFragment } from "./DealerFinanceProductsSpecs";

export { DealerFinanceProductsSpecsFragmentDoc } from "./DealerFinanceProductsSpecs";

export type { DealerDisclaimersFragmentFragment } from "./DealerDisclaimersFragment";

export { DealerDisclaimersFragmentFragmentDoc } from "./DealerDisclaimersFragment";

export type { DealerContactFragmentFragment } from "./DealerContactFragment";

export { DealerContactFragmentFragmentDoc } from "./DealerContactFragment";

export type { DealerBooleanSettingDataFragment } from "./DealerBooleanSettingData";

export { DealerBooleanSettingDataFragmentDoc } from "./DealerBooleanSettingData";

export type { DealerBookingCodeSpecsFragment } from "./DealerBookingCodeSpecs";

export { DealerBookingCodeSpecsFragmentDoc } from "./DealerBookingCodeSpecs";

export type { DealerBookingCodeDataFragment } from "./DealerBookingCodeData";

export { DealerBookingCodeDataFragmentDoc } from "./DealerBookingCodeData";

export type { DealerApplicationFragmentFragment } from "./DealerApplicationFragment";

export { DealerApplicationFragmentFragmentDoc } from "./DealerApplicationFragment";

export type { DateUnitDataFragment } from "./DateUnitData";

export { DateUnitDataFragmentDoc } from "./DateUnitData";

export type { CustomizedFieldDataFragment } from "./CustomizedFieldData";

export { CustomizedFieldDataFragmentDoc } from "./CustomizedFieldData";

export type {
  CustomerSpecs_CorporateCustomer_Fragment,
  CustomerSpecs_Guarantor_Fragment,
  CustomerSpecs_LocalCustomer_Fragment,
  CustomerSpecsFragment,
} from "./CustomerSpecs";

export { CustomerSpecsFragmentDoc } from "./CustomerSpecs";

export type { CustomerListStandardApplicationFragment } from "./CustomerListStandardApplication";

export { CustomerListStandardApplicationFragmentDoc } from "./CustomerListStandardApplication";

export type {
  CustomerListSpecs_CorporateCustomer_Fragment,
  CustomerListSpecs_Guarantor_Fragment,
  CustomerListSpecs_LocalCustomer_Fragment,
  CustomerListSpecsFragment,
} from "./CustomerListSpecs";

export { CustomerListSpecsFragmentDoc } from "./CustomerListSpecs";

export type { CustomerListMobilityApplicationFragment } from "./CustomerListMobilityApplication";

export { CustomerListMobilityApplicationFragmentDoc } from "./CustomerListMobilityApplication";

export type { CustomerListLaunchpadApplicationFragment } from "./CustomerListLaunchpadApplication";

export { CustomerListLaunchpadApplicationFragmentDoc } from "./CustomerListLaunchpadApplication";

export type { CustomerListFinderApplicationFragment } from "./CustomerListFinderApplication";

export { CustomerListFinderApplicationFragmentDoc } from "./CustomerListFinderApplication";

export type { CustomerListEventApplicationFragment } from "./CustomerListEventApplication";

export { CustomerListEventApplicationFragmentDoc } from "./CustomerListEventApplication";

export type { CustomerListEndpointSpecsFragment } from "./CustomerListEndpointSpecs";

export { CustomerListEndpointSpecsFragmentDoc } from "./CustomerListEndpointSpecs";

export type { CustomerListEndpointContextDataFragment } from "./CustomerListEndpointContextData";

export { CustomerListEndpointContextDataFragmentDoc } from "./CustomerListEndpointContextData";

export type { CustomerListConfiguratorApplicationFragment } from "./CustomerListConfiguratorApplication";

export { CustomerListConfiguratorApplicationFragmentDoc } from "./CustomerListConfiguratorApplication";

export type { CustomerDetailsStandardApplicationFragment } from "./CustomerDetailsStandardApplication";

export { CustomerDetailsStandardApplicationFragmentDoc } from "./CustomerDetailsStandardApplication";

export type { CustomerDetailsSalesOfferApplicationFragment } from "./CustomerDetailsSalesOfferApplication";

export { CustomerDetailsSalesOfferApplicationFragmentDoc } from "./CustomerDetailsSalesOfferApplication";

export type { CustomerDetailsMobilityApplicationFragment } from "./CustomerDetailsMobilityApplication";

export { CustomerDetailsMobilityApplicationFragmentDoc } from "./CustomerDetailsMobilityApplication";

export type { CustomerDetailsLaunchpadApplicationFragment } from "./CustomerDetailsLaunchpadApplication";

export { CustomerDetailsLaunchpadApplicationFragmentDoc } from "./CustomerDetailsLaunchpadApplication";

export type { CustomerDetailsFinderApplicationFragment } from "./CustomerDetailsFinderApplication";

export { CustomerDetailsFinderApplicationFragmentDoc } from "./CustomerDetailsFinderApplication";

export type { CustomerDetailsEventApplicationFragment } from "./CustomerDetailsEventApplication";

export { CustomerDetailsEventApplicationFragmentDoc } from "./CustomerDetailsEventApplication";

export type { CustomerDetailsConfiguratorApplicationFragment } from "./CustomerDetailsConfiguratorApplication";

export { CustomerDetailsConfiguratorApplicationFragmentDoc } from "./CustomerDetailsConfiguratorApplication";

export type {
  CustomerDetailSpecs_CorporateCustomer_Fragment,
  CustomerDetailSpecs_Guarantor_Fragment,
  CustomerDetailSpecs_LocalCustomer_Fragment,
  CustomerDetailSpecsFragment,
} from "./CustomerDetailSpecs";

export { CustomerDetailSpecsFragmentDoc } from "./CustomerDetailSpecs";

export type { CustomWebPageBlockDataFragment } from "./CustomWebPageBlockData";

export { CustomWebPageBlockDataFragmentDoc } from "./CustomWebPageBlockData";

export type { CustomTestDriveBookingSlotsDataFragment } from "./CustomTestDriveBookingSlotsData";

export { CustomTestDriveBookingSlotsDataFragmentDoc } from "./CustomTestDriveBookingSlotsData";

export type { CurrentUserDataFragment } from "./CurrentUserData";

export { CurrentUserDataFragmentDoc } from "./CurrentUserData";

export type { CurrentUserCompaniesDataFragment } from "./CurrentUserCompaniesData";

export { CurrentUserCompaniesDataFragmentDoc } from "./CurrentUserCompaniesData";

export type { CtsModuleWithPermissionsSpecsFragment } from "./CtsModuleWithPermissionsSpecs";

export { CtsModuleWithPermissionsSpecsFragmentDoc } from "./CtsModuleWithPermissionsSpecs";

export type { CtsModuleSpecsFragment } from "./CtsModuleSpecs";

export { CtsModuleSpecsFragmentDoc } from "./CtsModuleSpecs";

export type { CtsModuleSettingDataFragment } from "./CtsModuleSettingData";

export { CtsModuleSettingDataFragmentDoc } from "./CtsModuleSettingData";

export type { CounterSettingsSpecsFragment } from "./CounterSettingsSpecs";

export { CounterSettingsSpecsFragmentDoc } from "./CounterSettingsSpecs";

export type { CorporateCustomerDataFragment } from "./CorporateCustomerData";

export { CorporateCustomerDataFragmentDoc } from "./CorporateCustomerData";

export type {
  ConsentsAndDeclarationsWithPermissionsSpecs_CheckboxConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsWithPermissionsSpecs_GroupConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsWithPermissionsSpecs_MarketingConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsWithPermissionsSpecs_TextConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsWithPermissionsSpecsFragment,
} from "./ConsentsAndDeclarationsWithPermissionsSpecs";

export { ConsentsAndDeclarationsWithPermissionsSpecsFragmentDoc } from "./ConsentsAndDeclarationsWithPermissionsSpecs";

export type {
  ConsentsAndDeclarationsSpecs_CheckboxConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsSpecs_GroupConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsSpecs_MarketingConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsSpecs_TextConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsSpecsFragment,
} from "./ConsentsAndDeclarationsSpecs";

export { ConsentsAndDeclarationsSpecsFragmentDoc } from "./ConsentsAndDeclarationsSpecs";

export type { ConsentsAndDeclarationsModuleWithPermissionsSpecsFragment } from "./ConsentsAndDeclarationsModuleWithPermissionsSpecs";

export { ConsentsAndDeclarationsModuleWithPermissionsSpecsFragmentDoc } from "./ConsentsAndDeclarationsModuleWithPermissionsSpecs";

export type { ConsentsAndDeclarationsModuleSpecsFragment } from "./ConsentsAndDeclarationsModuleSpecs";

export { ConsentsAndDeclarationsModuleSpecsFragmentDoc } from "./ConsentsAndDeclarationsModuleSpecs";

export type {
  ConsentsAndDeclarationsListData_CheckboxConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsListData_GroupConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsListData_MarketingConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsListData_TextConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsListDataFragment,
} from "./ConsentsAndDeclarationsListData";

export { ConsentsAndDeclarationsListDataFragmentDoc } from "./ConsentsAndDeclarationsListData";

export type { ConfiguratorModuleWithPermissionsSpecsFragment } from "./ConfiguratorModuleWithPermissionsSpecs";

export { ConfiguratorModuleWithPermissionsSpecsFragmentDoc } from "./ConfiguratorModuleWithPermissionsSpecs";

export type { ConfiguratorModuleSpecsForApplicationFragment } from "./ConfiguratorModuleSpecsForApplication";

export { ConfiguratorModuleSpecsForApplicationFragmentDoc } from "./ConfiguratorModuleSpecsForApplication";

export type { ConfiguratorModuleSpecsFragment } from "./ConfiguratorModuleSpecs";

export { ConfiguratorModuleSpecsFragmentDoc } from "./ConfiguratorModuleSpecs";

export type { ConfiguratorModuleInDealerSpecsFragment } from "./ConfiguratorModuleInDealerSpecs";

export { ConfiguratorModuleInDealerSpecsFragmentDoc } from "./ConfiguratorModuleInDealerSpecs";

export type { ConfiguratorModuleEmailContentSpecsFragment } from "./ConfiguratorModuleEmailContentSpecs";

export { ConfiguratorModuleEmailContentSpecsFragmentDoc } from "./ConfiguratorModuleEmailContentSpecs";

export type { ConfiguratorLeadDataFragment } from "./ConfiguratorLeadData";

export { ConfiguratorLeadDataFragmentDoc } from "./ConfiguratorLeadData";

export type {
  ConfiguratorJourneyBlocksData_ApplicationConfiguratorColorSetting_Fragment,
  ConfiguratorJourneyBlocksData_ApplicationConfiguratorOptionSetting_Fragment,
  ConfiguratorJourneyBlocksData_ApplicationConfiguratorPackageSetting_Fragment,
  ConfiguratorJourneyBlocksData_ApplicationConfiguratorTrimSetting_Fragment,
  ConfiguratorJourneyBlocksDataFragment,
} from "./ConfiguratorJourneyBlocksData";

export { ConfiguratorJourneyBlocksDataFragmentDoc } from "./ConfiguratorJourneyBlocksData";

export type { ConfiguratorInventorySpecsFragment } from "./ConfiguratorInventorySpecs";

export { ConfiguratorInventorySpecsFragmentDoc } from "./ConfiguratorInventorySpecs";

export type { ConfiguratorInventoryPublicSpecsFragment } from "./ConfiguratorInventoryPublicSpecs";

export { ConfiguratorInventoryPublicSpecsFragmentDoc } from "./ConfiguratorInventoryPublicSpecs";

export type {
  ConfiguratorDetails_ModelConfigurator_Fragment,
  ConfiguratorDetails_VariantConfigurator_Fragment,
  ConfiguratorDetailsFragment,
} from "./ConfiguratorDetails";

export { ConfiguratorDetailsFragmentDoc } from "./ConfiguratorDetails";

export type { ConfiguratorApplicationSpecFragment } from "./ConfiguratorApplicationSpec";

export { ConfiguratorApplicationSpecFragmentDoc } from "./ConfiguratorApplicationSpec";

export type { ConfiguratorApplicationModuleDebugJourneyFragment } from "./ConfiguratorApplicationModuleDebugJourney";

export { ConfiguratorApplicationModuleDebugJourneyFragmentDoc } from "./ConfiguratorApplicationModuleDebugJourney";

export type { ConfiguratorApplicationModuleDataFragment } from "./ConfiguratorApplicationModuleData";

export { ConfiguratorApplicationModuleDataFragmentDoc } from "./ConfiguratorApplicationModuleData";

export type { ConfiguratorApplicationEntrypointSpecsFragment } from "./ConfiguratorApplicationEntrypointSpecs";

export { ConfiguratorApplicationEntrypointSpecsFragmentDoc } from "./ConfiguratorApplicationEntrypointSpecs";

export type { ConfiguratorApplicationEntrypointContextDataFragment } from "./ConfiguratorApplicationEntrypointContextData";

export { ConfiguratorApplicationEntrypointContextDataFragmentDoc } from "./ConfiguratorApplicationEntrypointContextData";

export type { ConfiguratorApplicationDataFragment } from "./ConfiguratorApplicationData";

export { ConfiguratorApplicationDataFragmentDoc } from "./ConfiguratorApplicationData";

export type { ConfiguratorApplicationConfigurationDataFragment } from "./ConfiguratorApplicationConfigurationData";

export { ConfiguratorApplicationConfigurationDataFragmentDoc } from "./ConfiguratorApplicationConfigurationData";

export type {
  ConditionSpecs_ApplicationModuleCondition_Fragment,
  ConditionSpecs_BankCondition_Fragment,
  ConditionSpecs_ContextualCondition_Fragment,
  ConditionSpecs_DealerCondition_Fragment,
  ConditionSpecs_GiftVoucherCondition_Fragment,
  ConditionSpecs_InsurerCondition_Fragment,
  ConditionSpecs_LocationCondition_Fragment,
  ConditionSpecs_LogicCondition_Fragment,
  ConditionSpecs_SalesOfferAgreementsCondition_Fragment,
  ConditionSpecsFragment,
} from "./ConditionSpecs";

export { ConditionSpecsFragmentDoc } from "./ConditionSpecs";

export type { CompanyWithPermissionsSpecsFragment } from "./CompanyWithPermissionsSpecs";

export { CompanyWithPermissionsSpecsFragmentDoc } from "./CompanyWithPermissionsSpecs";

export type { CompanyWithPermissionsContextDataFragment } from "./CompanyWithPermissionsContextData";

export { CompanyWithPermissionsContextDataFragmentDoc } from "./CompanyWithPermissionsContextData";

export type { CompanyWebpagePublicSpecFragment } from "./CompanyWebpagePublicSpec";

export { CompanyWebpagePublicSpecFragmentDoc } from "./CompanyWebpagePublicSpec";

export type { CompanySpecsFragment } from "./CompanySpecs";

export { CompanySpecsFragmentDoc } from "./CompanySpecs";

export type { CompanySelectionItemFragment } from "./CompanySelectionItem";

export { CompanySelectionItemFragmentDoc } from "./CompanySelectionItem";

export type { CompanyPublicSpecsFragment } from "./CompanyPublicSpecs";

export { CompanyPublicSpecsFragmentDoc } from "./CompanyPublicSpecs";

export type { CompanyListDataFragment } from "./CompanyListData";

export { CompanyListDataFragmentDoc } from "./CompanyListData";

export type { CompanyInModuleOptionDataFragment } from "./CompanyInModuleOptionData";

export { CompanyInModuleOptionDataFragmentDoc } from "./CompanyInModuleOptionData";

export type { CompanyDealerDataFragment } from "./CompanyDealerData";

export { CompanyDealerDataFragmentDoc } from "./CompanyDealerData";

export type { CompanyContextDataFragment } from "./CompanyContextData";

export { CompanyContextDataFragmentDoc } from "./CompanyContextData";

export type { ColumnWebPageBlockDataFragment } from "./ColumnWebPageBlockData";

export { ColumnWebPageBlockDataFragmentDoc } from "./ColumnWebPageBlockData";

export type { ColumnBlockDataFragment } from "./ColumnBlockData";

export { ColumnBlockDataFragmentDoc } from "./ColumnBlockData";

export type { ColorBlockSpecsFragment } from "./ColorBlockSpecs";

export { ColorBlockSpecsFragmentDoc } from "./ColorBlockSpecs";

export type { ColorAndTrimSettingsSpecsFragment } from "./ColorAndTrimSettingsSpecs";

export { ColorAndTrimSettingsSpecsFragmentDoc } from "./ColorAndTrimSettingsSpecs";

export type { CapVehicleModelMetadataSpecsFragment } from "./CapVehicleModelMetadataSpecs";

export { CapVehicleModelMetadataSpecsFragmentDoc } from "./CapVehicleModelMetadataSpecs";

export type { CapVehicleMakeMetadataSpecsFragment } from "./CapVehicleMakeMetadataSpecs";

export { CapVehicleMakeMetadataSpecsFragmentDoc } from "./CapVehicleMakeMetadataSpecs";

export type { CapSettingSpecFragment } from "./CapSettingSpec";

export { CapSettingSpecFragmentDoc } from "./CapSettingSpec";

export type { CapModuleWithPermissionsSpecsFragment } from "./CapModuleWithPermissionsSpecs";

export { CapModuleWithPermissionsSpecsFragmentDoc } from "./CapModuleWithPermissionsSpecs";

export type { CapModuleSpecsFragment } from "./CapModuleSpecs";

export { CapModuleSpecsFragmentDoc } from "./CapModuleSpecs";

export type { CapLeadFragment } from "./CapLead";

export { CapLeadFragmentDoc } from "./CapLead";

export type { CapBusinessPartnerFragment } from "./CapBusinessPartner";

export { CapBusinessPartnerFragmentDoc } from "./CapBusinessPartner";

export type {
  CalculatorResultData_CalculatorDeferredPrincipalResult_Fragment,
  CalculatorResultData_CalculatorHirePurchaseResult_Fragment,
  CalculatorResultData_CalculatorHirePurchaseWithBalloonGfvResult_Fragment,
  CalculatorResultData_CalculatorHirePurchaseWithBalloonResult_Fragment,
  CalculatorResultData_CalculatorLeasePurchaseResult_Fragment,
  CalculatorResultData_CalculatorLeaseResult_Fragment,
  CalculatorResultData_CalculatorUcclLeasingResult_Fragment,
  CalculatorResultDataFragment,
} from "./CalculatorResultData";

export { CalculatorResultDataFragmentDoc } from "./CalculatorResultData";

export type {
  BlockDetails_ColorBlock_Fragment,
  BlockDetails_OptionsBlock_Fragment,
  BlockDetails_PackageBlock_Fragment,
  BlockDetails_TrimBlock_Fragment,
  BlockDetailsFragment,
} from "./BlockDetails";

export { BlockDetailsFragmentDoc } from "./BlockDetails";

export type { BasicSigningModuleWithPermissionsSpecsFragment } from "./BasicSigningModuleWithPermissionsSpecs";

export { BasicSigningModuleWithPermissionsSpecsFragmentDoc } from "./BasicSigningModuleWithPermissionsSpecs";

export type { BasicSigningModuleSpecsFragment } from "./BasicSigningModuleSpecs";

export { BasicSigningModuleSpecsFragmentDoc } from "./BasicSigningModuleSpecs";

export type { BasicRouterLayoutSpecsFragment } from "./BasicRouterLayoutSpecs";

export { BasicRouterLayoutSpecsFragmentDoc } from "./BasicRouterLayoutSpecs";

export type { BasicProLayoutSpecsFragment } from "./BasicProLayoutSpecs";

export { BasicProLayoutSpecsFragmentDoc } from "./BasicProLayoutSpecs";

export type {
  BaseConditionSpecs_ApplicationModuleCondition_Fragment,
  BaseConditionSpecs_BankCondition_Fragment,
  BaseConditionSpecs_ContextualCondition_Fragment,
  BaseConditionSpecs_DealerCondition_Fragment,
  BaseConditionSpecs_GiftVoucherCondition_Fragment,
  BaseConditionSpecs_InsurerCondition_Fragment,
  BaseConditionSpecs_LocationCondition_Fragment,
  BaseConditionSpecs_LogicCondition_Fragment,
  BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment,
  BaseConditionSpecsFragment,
} from "./BaseConditionSpecs";

export { BaseConditionSpecsFragmentDoc } from "./BaseConditionSpecs";

export type { BannerPublicDataFragment } from "./BannerPublicData";

export { BannerPublicDataFragmentDoc } from "./BannerPublicData";

export type { BannerListDataFragment } from "./BannerListData";

export { BannerListDataFragmentDoc } from "./BannerListData";

export type { BannerDataFragment } from "./BannerData";

export { BannerDataFragmentDoc } from "./BannerData";

export type { BankOptionsDataFragment } from "./BankOptionsData";

export { BankOptionsDataFragmentDoc } from "./BankOptionsData";

export type { BankModuleWithPermissionsSpecsFragment } from "./BankModuleWithPermissionsSpecs";

export { BankModuleWithPermissionsSpecsFragmentDoc } from "./BankModuleWithPermissionsSpecs";

export type { BankModuleSpecsFragment } from "./BankModuleSpecs";

export { BankModuleSpecsFragmentDoc } from "./BankModuleSpecs";

export type { BankListDataFragment } from "./BankListData";

export { BankListDataFragmentDoc } from "./BankListData";

export type {
  BankIntegrationData_DbsBankIntegration_Fragment,
  BankIntegrationData_EmailBankIntegration_Fragment,
  BankIntegrationData_EnbdBankIntegration_Fragment,
  BankIntegrationData_HlfBankIntegration_Fragment,
  BankIntegrationData_HlfBankV2Integration_Fragment,
  BankIntegrationData_MaybankIntegration_Fragment,
  BankIntegrationData_UobBankIntegration_Fragment,
  BankIntegrationDataFragment,
} from "./BankIntegrationData";

export { BankIntegrationDataFragmentDoc } from "./BankIntegrationData";

export type { BankDetailsWithPermissionsDataFragment } from "./BankDetailsWithPermissionsData";

export { BankDetailsWithPermissionsDataFragmentDoc } from "./BankDetailsWithPermissionsData";

export type { BankDetailsDataFragment } from "./BankDetailsData";

export { BankDetailsDataFragmentDoc } from "./BankDetailsData";

export type { BankDealerMarketDataFragment } from "./BankDealerMarketData";

export { BankDealerMarketDataFragmentDoc } from "./BankDealerMarketData";

export type {
  BalloonSettingsDetails_BalloonRangeSettings_Fragment,
  BalloonSettingsDetails_BalloonTableSettings_Fragment,
  BalloonSettingsDetailsFragment,
} from "./BalloonSettingsDetails";

export { BalloonSettingsDetailsFragmentDoc } from "./BalloonSettingsDetails";

export type { BalloonGfvSettingsDetailsFragment } from "./BalloonGFVSettingsDetails";

export { BalloonGfvSettingsDetailsFragmentDoc } from "./BalloonGFVSettingsDetails";

export type { AvailableModulesDataFragment } from "./AvailableModulesData";

export { AvailableModulesDataFragmentDoc } from "./AvailableModulesData";

export type { AutoplaySettingSpecsFragment } from "./AutoplaySettingSpecs";

export { AutoplaySettingSpecsFragmentDoc } from "./AutoplaySettingSpecs";

export type { AutoplayModuleWithPermissionsSpecsFragment } from "./AutoplayModuleWithPermissionsSpecs";

export { AutoplayModuleWithPermissionsSpecsFragmentDoc } from "./AutoplayModuleWithPermissionsSpecs";

export type { AutoplayModuleSpecsFragment } from "./AutoplayModuleSpecs";

export { AutoplayModuleSpecsFragmentDoc } from "./AutoplayModuleSpecs";

export type {
  AuthorData_CorporateCustomer_Fragment,
  AuthorData_ExternalBank_Fragment,
  AuthorData_Guarantor_Fragment,
  AuthorData_LocalCustomer_Fragment,
  AuthorData_PorscheRetain_Fragment,
  AuthorData_Salesforce_Fragment,
  AuthorData_SystemBank_Fragment,
  AuthorData_User_Fragment,
  AuthorDataFragment,
} from "./AuthorData";

export { AuthorDataFragmentDoc } from "./AuthorData";

export type { AuditTrailDetailsFragment } from "./AuditTrailDetails";

export { AuditTrailDetailsFragmentDoc } from "./AuditTrailDetails";

export type { AppointmentTimeSlotDataFragment } from "./AppointmentTimeSlotData";

export { AppointmentTimeSlotDataFragmentDoc } from "./AppointmentTimeSlotData";

export type { AppointmentModuleWithPermissionsSpecsFragment } from "./AppointmentModuleWithPermissionsSpecs";

export { AppointmentModuleWithPermissionsSpecsFragmentDoc } from "./AppointmentModuleWithPermissionsSpecs";

export type { AppointmentModuleSpecsFragment } from "./AppointmentModuleSpecs";

export { AppointmentModuleSpecsFragmentDoc } from "./AppointmentModuleSpecs";

export type { AppointmentModuleInDealerSpecsFragment } from "./AppointmentModuleInDealerSpecs";

export { AppointmentModuleInDealerSpecsFragmentDoc } from "./AppointmentModuleInDealerSpecs";

export type {
  AppointmentModuleEmailContentSpecsFragment,
  AppointmentModuleEmailContentFinderReservationSpecsFragment,
  AppointmentModuleEmailContentCustomerSpecsFragment,
  AppointmentModuleEmailContentSalesPersonSpecsFragment,
  AppointmentModuleEmailContentsSpecsFragment,
} from "./AppointmentModuleEmailContentsSpecs";

export {
  AppointmentModuleEmailContentSpecsFragmentDoc,
  AppointmentModuleEmailContentCustomerSpecsFragmentDoc,
  AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc,
  AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc,
  AppointmentModuleEmailContentsSpecsFragmentDoc,
} from "./AppointmentModuleEmailContentsSpecs";

export type { AppointmentModuleApplicationJourneyFragment } from "./AppointmentModuleApplicationJourney";

export { AppointmentModuleApplicationJourneyFragmentDoc } from "./AppointmentModuleApplicationJourney";

export type { ApplicationVariantSpecFragment } from "./ApplicationVehicleSpec";

export { ApplicationVariantSpecFragmentDoc } from "./ApplicationVehicleSpec";

export type { ApplicationTtbDepositDataFragment } from "./ApplicationTtbDepositData";

export { ApplicationTtbDepositDataFragmentDoc } from "./ApplicationTtbDepositData";

export type {
  ApplicationStageUserData_ConfiguratorApplication_Fragment,
  ApplicationStageUserData_EventApplication_Fragment,
  ApplicationStageUserData_FinderApplication_Fragment,
  ApplicationStageUserData_LaunchpadApplication_Fragment,
  ApplicationStageUserData_MobilityApplication_Fragment,
  ApplicationStageUserData_SalesOfferApplication_Fragment,
  ApplicationStageUserData_StandardApplication_Fragment,
  ApplicationStageUserDataFragment,
} from "./ApplicationStageUserData";

export { ApplicationStageUserDataFragmentDoc } from "./ApplicationStageUserData";

export type {
  ApplicationStageData_ConfiguratorApplication_Fragment,
  ApplicationStageData_EventApplication_Fragment,
  ApplicationStageData_FinderApplication_Fragment,
  ApplicationStageData_LaunchpadApplication_Fragment,
  ApplicationStageData_MobilityApplication_Fragment,
  ApplicationStageData_SalesOfferApplication_Fragment,
  ApplicationStageData_StandardApplication_Fragment,
  ApplicationStageDataFragment,
} from "./ApplicationStageData";

export { ApplicationStageDataFragmentDoc } from "./ApplicationStageData";

export type {
  ApplicationQuotationOptionDataFragment,
  ApplicationQuotationDataFragment,
} from "./ApplicationQuotationData";

export {
  ApplicationQuotationOptionDataFragmentDoc,
  ApplicationQuotationDataFragmentDoc,
} from "./ApplicationQuotationData";

export type { ApplicationPorscheDepositDataFragment } from "./ApplicationPorscheDepositData";

export { ApplicationPorscheDepositDataFragmentDoc } from "./ApplicationPorscheDepositData";

export type { ApplicationPayGateDepositDataFragment } from "./ApplicationPayGateDepositData";

export { ApplicationPayGateDepositDataFragmentDoc } from "./ApplicationPayGateDepositData";

export type { ApplicationModelSpecsFragment } from "./ApplicationModelSpec";

export { ApplicationModelSpecsFragmentDoc } from "./ApplicationModelSpec";

export type {
  ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment,
  ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment,
  ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment,
  ApplicationMarketTypeFragmentFragment,
} from "./ApplicationMarketTypeFragment";

export { ApplicationMarketTypeFragmentFragmentDoc } from "./ApplicationMarketTypeFragment";

export type { ApplicationListEndpointSpecsFragment } from "./ApplicationListEndpointSpecs";

export { ApplicationListEndpointSpecsFragmentDoc } from "./ApplicationListEndpointSpecs";

export type { ApplicationListEndpointContextDataFragment } from "./ApplicationListEndpointContextData";

export { ApplicationListEndpointContextDataFragmentDoc } from "./ApplicationListEndpointContextData";

export type {
  ApplicationListData_ConfiguratorApplication_Fragment,
  ApplicationListData_EventApplication_Fragment,
  ApplicationListData_FinderApplication_Fragment,
  ApplicationListData_LaunchpadApplication_Fragment,
  ApplicationListData_MobilityApplication_Fragment,
  ApplicationListData_SalesOfferApplication_Fragment,
  ApplicationListData_StandardApplication_Fragment,
  ApplicationListDataFragment,
} from "./ApplicationListData";

export { ApplicationListDataFragmentDoc } from "./ApplicationListData";

export type {
  ApplicationJourneyDeposit_ApplicationAdyenDeposit_Fragment,
  ApplicationJourneyDeposit_ApplicationFiservDeposit_Fragment,
  ApplicationJourneyDeposit_ApplicationPayGateDeposit_Fragment,
  ApplicationJourneyDeposit_ApplicationPorscheDeposit_Fragment,
  ApplicationJourneyDeposit_ApplicationTtbDeposit_Fragment,
  ApplicationJourneyDepositFragment,
} from "./ApplicationJourneyDeposit";

export { ApplicationJourneyDepositFragmentDoc } from "./ApplicationJourneyDeposit";

export type {
  ApplicationInsurancingData_DefaultApplicationInsurancing_Fragment,
  ApplicationInsurancingData_NewZealandApplicationInsurancing_Fragment,
  ApplicationInsurancingData_SingaporeApplicationInsurancing_Fragment,
  ApplicationInsurancingDataFragment,
} from "./ApplicationInsurancingData";

export { ApplicationInsurancingDataFragmentDoc } from "./ApplicationInsurancingData";

export type {
  ApplicationInCustomerDetails_ConfiguratorApplication_Fragment,
  ApplicationInCustomerDetails_EventApplication_Fragment,
  ApplicationInCustomerDetails_FinderApplication_Fragment,
  ApplicationInCustomerDetails_LaunchpadApplication_Fragment,
  ApplicationInCustomerDetails_MobilityApplication_Fragment,
  ApplicationInCustomerDetails_SalesOfferApplication_Fragment,
  ApplicationInCustomerDetails_StandardApplication_Fragment,
  ApplicationInCustomerDetailsFragment,
} from "./ApplicationInCustomerDetails";

export { ApplicationInCustomerDetailsFragmentDoc } from "./ApplicationInCustomerDetails";

export type { ApplicationFiservDepositDataFragment } from "./ApplicationFiservDepositData";

export { ApplicationFiservDepositDataFragmentDoc } from "./ApplicationFiservDepositData";

export type { ApplicationFinderVehicleSpecsFragment } from "./ApplicationFinderVehicleSpecs";

export { ApplicationFinderVehicleSpecsFragmentDoc } from "./ApplicationFinderVehicleSpecs";

export type {
  ApplicationFinancingListData_DefaultApplicationFinancing_Fragment,
  ApplicationFinancingListData_NewZealandApplicationFinancing_Fragment,
  ApplicationFinancingListData_SingaporeApplicationFinancing_Fragment,
  ApplicationFinancingListDataFragment,
} from "./ApplicationFinancingListData";

export { ApplicationFinancingListDataFragmentDoc } from "./ApplicationFinancingListData";

export type {
  ApplicationFinancingData_DefaultApplicationFinancing_Fragment,
  ApplicationFinancingData_NewZealandApplicationFinancing_Fragment,
  ApplicationFinancingData_SingaporeApplicationFinancing_Fragment,
  ApplicationFinancingDataFragment,
} from "./ApplicationFinancingData";

export { ApplicationFinancingDataFragmentDoc } from "./ApplicationFinancingData";

export type { ApplicationEventCustomizedFieldDataFragment } from "./ApplicationEventCustomizedFieldData";

export { ApplicationEventCustomizedFieldDataFragmentDoc } from "./ApplicationEventCustomizedFieldData";

export type { ApplicationDocumentDataFragment } from "./ApplicationDocumentData";

export { ApplicationDocumentDataFragmentDoc } from "./ApplicationDocumentData";

export type {
  ApplicationData_ConfiguratorApplication_Fragment,
  ApplicationData_EventApplication_Fragment,
  ApplicationData_FinderApplication_Fragment,
  ApplicationData_LaunchpadApplication_Fragment,
  ApplicationData_MobilityApplication_Fragment,
  ApplicationData_SalesOfferApplication_Fragment,
  ApplicationData_StandardApplication_Fragment,
  ApplicationDataFragment,
} from "./ApplicationData";

export { ApplicationDataFragmentDoc } from "./ApplicationData";

export type { ApplicationConfigurationDataFragment } from "./ApplicationConfigurationData";

export { ApplicationConfigurationDataFragmentDoc } from "./ApplicationConfigurationData";

export type {
  ApplicationAgreementData_CheckboxApplicationAgreement_Fragment,
  ApplicationAgreementData_MarketingApplicationAgreement_Fragment,
  ApplicationAgreementData_TextApplicationAgreement_Fragment,
  ApplicationAgreementDataFragment,
} from "./ApplicationAgreementData";

export { ApplicationAgreementDataFragmentDoc } from "./ApplicationAgreementData";

export type { ApplicationAdyenDepositDataFragment } from "./ApplicationAdyenDepositData";

export { ApplicationAdyenDepositDataFragmentDoc } from "./ApplicationAdyenDepositData";

export type { AdyenPaymentSettingsSpecFragment } from "./AdyenPaymentSettingsSpec";

export { AdyenPaymentSettingsSpecFragmentDoc } from "./AdyenPaymentSettingsSpec";

export type { AdyenPaymentModuleWithPermissionsSpecsFragment } from "./AdyenPaymentModuleWithPermissionsSpecs";

export { AdyenPaymentModuleWithPermissionsSpecsFragmentDoc } from "./AdyenPaymentModuleWithPermissionsSpecs";

export type { AdyenPaymentModuleSpecsFragment } from "./AdyenPaymentModuleSpecs";

export { AdyenPaymentModuleSpecsFragmentDoc } from "./AdyenPaymentModuleSpecs";

export type { AdvancedVersioningDataFragment } from "./AdvancedVersioningData";

export { AdvancedVersioningDataFragmentDoc } from "./AdvancedVersioningData";

export type { AdditionalDetailsSpecsFragment } from "./AdditionalDetailsSpecs";

export { AdditionalDetailsSpecsFragmentDoc } from "./AdditionalDetailsSpecs";
