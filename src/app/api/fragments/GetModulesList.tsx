import type * as SchemaTypes from '../types';

import type { ModuleListData_AdyenPaymentModule_Fragment, ModuleListData_AppointmentModule_Fragment, ModuleListData_AutoplayModule_Fragment, ModuleListData_BankModule_Fragment, ModuleListData_BasicSigningModule_Fragment, ModuleListData_CapModule_Fragment, ModuleListData_ConfiguratorModule_Fragment, ModuleListData_ConsentsAndDeclarationsModule_Fragment, ModuleListData_CtsModule_Fragment, ModuleListData_DocusignModule_Fragment, ModuleListData_EventApplicationModule_Fragment, ModuleListData_FinderApplicationPrivateModule_Fragment, ModuleListData_FinderApplicationPublicModule_Fragment, ModuleListData_FinderVehicleManagementModule_Fragment, ModuleListData_FiservPaymentModule_Fragment, ModuleListData_GiftVoucherModule_Fragment, ModuleListData_InsuranceModule_Fragment, ModuleListData_LabelsModule_Fragment, ModuleListData_LaunchPadModule_Fragment, ModuleListData_LocalCustomerManagementModule_Fragment, ModuleListData_MaintenanceModule_Fragment, ModuleListData_MarketingModule_Fragment, ModuleListData_MobilityModule_Fragment, ModuleListData_MyInfoModule_Fragment, ModuleListData_NamirialSigningModule_Fragment, ModuleListData_OidcModule_Fragment, ModuleListData_PayGatePaymentModule_Fragment, ModuleListData_PorscheIdModule_Fragment, ModuleListData_PorscheMasterDataModule_Fragment, ModuleListData_PorschePaymentModule_Fragment, ModuleListData_PorscheRetainModule_Fragment, ModuleListData_PromoCodeModule_Fragment, ModuleListData_SalesControlBoardModule_Fragment, ModuleListData_SalesOfferModule_Fragment, ModuleListData_SimpleVehicleManagementModule_Fragment, ModuleListData_StandardApplicationModule_Fragment, ModuleListData_TradeInModule_Fragment, ModuleListData_TtbPaymentModule_Fragment, ModuleListData_UserlikeChatbotModule_Fragment, ModuleListData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleListData_VisitAppointmentModule_Fragment, ModuleListData_WebsiteModule_Fragment, ModuleListData_WhatsappLiveChatModule_Fragment } from './ModuleListData';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import { gql } from '@apollo/client';
import { ModuleListDataFragmentDoc } from './ModuleListData';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
export type GetModulesList_AdyenPaymentModule_Fragment = (
  { __typename: 'AdyenPaymentModule' }
  & ModuleListData_AdyenPaymentModule_Fragment
);

export type GetModulesList_AppointmentModule_Fragment = (
  { __typename: 'AppointmentModule' }
  & ModuleListData_AppointmentModule_Fragment
);

export type GetModulesList_AutoplayModule_Fragment = (
  { __typename: 'AutoplayModule' }
  & ModuleListData_AutoplayModule_Fragment
);

export type GetModulesList_BankModule_Fragment = (
  { __typename: 'BankModule' }
  & ModuleListData_BankModule_Fragment
);

export type GetModulesList_BasicSigningModule_Fragment = (
  { __typename: 'BasicSigningModule' }
  & ModuleListData_BasicSigningModule_Fragment
);

export type GetModulesList_CapModule_Fragment = (
  { __typename: 'CapModule' }
  & ModuleListData_CapModule_Fragment
);

export type GetModulesList_ConfiguratorModule_Fragment = (
  { __typename: 'ConfiguratorModule' }
  & ModuleListData_ConfiguratorModule_Fragment
);

export type GetModulesList_ConsentsAndDeclarationsModule_Fragment = (
  { __typename: 'ConsentsAndDeclarationsModule' }
  & ModuleListData_ConsentsAndDeclarationsModule_Fragment
);

export type GetModulesList_CtsModule_Fragment = (
  { __typename: 'CtsModule' }
  & ModuleListData_CtsModule_Fragment
);

export type GetModulesList_DocusignModule_Fragment = (
  { __typename: 'DocusignModule' }
  & ModuleListData_DocusignModule_Fragment
);

export type GetModulesList_EventApplicationModule_Fragment = (
  { __typename: 'EventApplicationModule' }
  & ModuleListData_EventApplicationModule_Fragment
);

export type GetModulesList_FinderApplicationPrivateModule_Fragment = (
  { __typename: 'FinderApplicationPrivateModule' }
  & ModuleListData_FinderApplicationPrivateModule_Fragment
);

export type GetModulesList_FinderApplicationPublicModule_Fragment = (
  { __typename: 'FinderApplicationPublicModule' }
  & ModuleListData_FinderApplicationPublicModule_Fragment
);

export type GetModulesList_FinderVehicleManagementModule_Fragment = (
  { __typename: 'FinderVehicleManagementModule' }
  & ModuleListData_FinderVehicleManagementModule_Fragment
);

export type GetModulesList_FiservPaymentModule_Fragment = (
  { __typename: 'FiservPaymentModule' }
  & ModuleListData_FiservPaymentModule_Fragment
);

export type GetModulesList_GiftVoucherModule_Fragment = (
  { __typename: 'GiftVoucherModule' }
  & ModuleListData_GiftVoucherModule_Fragment
);

export type GetModulesList_InsuranceModule_Fragment = (
  { __typename: 'InsuranceModule' }
  & ModuleListData_InsuranceModule_Fragment
);

export type GetModulesList_LabelsModule_Fragment = (
  { __typename: 'LabelsModule' }
  & ModuleListData_LabelsModule_Fragment
);

export type GetModulesList_LaunchPadModule_Fragment = (
  { __typename: 'LaunchPadModule' }
  & ModuleListData_LaunchPadModule_Fragment
);

export type GetModulesList_LocalCustomerManagementModule_Fragment = (
  { __typename: 'LocalCustomerManagementModule' }
  & ModuleListData_LocalCustomerManagementModule_Fragment
);

export type GetModulesList_MaintenanceModule_Fragment = (
  { __typename: 'MaintenanceModule' }
  & ModuleListData_MaintenanceModule_Fragment
);

export type GetModulesList_MarketingModule_Fragment = (
  { __typename: 'MarketingModule' }
  & ModuleListData_MarketingModule_Fragment
);

export type GetModulesList_MobilityModule_Fragment = (
  { __typename: 'MobilityModule' }
  & { homeDelivery: (
    { __typename: 'MobilityHomeDelivery' }
    & Pick<SchemaTypes.MobilityHomeDelivery, 'isEnable'>
  ), locations: Array<(
    { __typename: 'MobilityLocation' }
    & MobilityLocationDataFragment
  )> }
  & ModuleListData_MobilityModule_Fragment
);

export type GetModulesList_MyInfoModule_Fragment = (
  { __typename: 'MyInfoModule' }
  & ModuleListData_MyInfoModule_Fragment
);

export type GetModulesList_NamirialSigningModule_Fragment = (
  { __typename: 'NamirialSigningModule' }
  & ModuleListData_NamirialSigningModule_Fragment
);

export type GetModulesList_OidcModule_Fragment = (
  { __typename: 'OIDCModule' }
  & ModuleListData_OidcModule_Fragment
);

export type GetModulesList_PayGatePaymentModule_Fragment = (
  { __typename: 'PayGatePaymentModule' }
  & ModuleListData_PayGatePaymentModule_Fragment
);

export type GetModulesList_PorscheIdModule_Fragment = (
  { __typename: 'PorscheIdModule' }
  & ModuleListData_PorscheIdModule_Fragment
);

export type GetModulesList_PorscheMasterDataModule_Fragment = (
  { __typename: 'PorscheMasterDataModule' }
  & ModuleListData_PorscheMasterDataModule_Fragment
);

export type GetModulesList_PorschePaymentModule_Fragment = (
  { __typename: 'PorschePaymentModule' }
  & ModuleListData_PorschePaymentModule_Fragment
);

export type GetModulesList_PorscheRetainModule_Fragment = (
  { __typename: 'PorscheRetainModule' }
  & ModuleListData_PorscheRetainModule_Fragment
);

export type GetModulesList_PromoCodeModule_Fragment = (
  { __typename: 'PromoCodeModule' }
  & ModuleListData_PromoCodeModule_Fragment
);

export type GetModulesList_SalesControlBoardModule_Fragment = (
  { __typename: 'SalesControlBoardModule' }
  & ModuleListData_SalesControlBoardModule_Fragment
);

export type GetModulesList_SalesOfferModule_Fragment = (
  { __typename: 'SalesOfferModule' }
  & ModuleListData_SalesOfferModule_Fragment
);

export type GetModulesList_SimpleVehicleManagementModule_Fragment = (
  { __typename: 'SimpleVehicleManagementModule' }
  & ModuleListData_SimpleVehicleManagementModule_Fragment
);

export type GetModulesList_StandardApplicationModule_Fragment = (
  { __typename: 'StandardApplicationModule' }
  & ModuleListData_StandardApplicationModule_Fragment
);

export type GetModulesList_TradeInModule_Fragment = (
  { __typename: 'TradeInModule' }
  & ModuleListData_TradeInModule_Fragment
);

export type GetModulesList_TtbPaymentModule_Fragment = (
  { __typename: 'TtbPaymentModule' }
  & ModuleListData_TtbPaymentModule_Fragment
);

export type GetModulesList_UserlikeChatbotModule_Fragment = (
  { __typename: 'UserlikeChatbotModule' }
  & ModuleListData_UserlikeChatbotModule_Fragment
);

export type GetModulesList_VehicleDataWithPorscheCodeIntegrationModule_Fragment = (
  { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
  & ModuleListData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
);

export type GetModulesList_VisitAppointmentModule_Fragment = (
  { __typename: 'VisitAppointmentModule' }
  & ModuleListData_VisitAppointmentModule_Fragment
);

export type GetModulesList_WebsiteModule_Fragment = (
  { __typename: 'WebsiteModule' }
  & ModuleListData_WebsiteModule_Fragment
);

export type GetModulesList_WhatsappLiveChatModule_Fragment = (
  { __typename: 'WhatsappLiveChatModule' }
  & ModuleListData_WhatsappLiveChatModule_Fragment
);

export type GetModulesListFragment = GetModulesList_AdyenPaymentModule_Fragment | GetModulesList_AppointmentModule_Fragment | GetModulesList_AutoplayModule_Fragment | GetModulesList_BankModule_Fragment | GetModulesList_BasicSigningModule_Fragment | GetModulesList_CapModule_Fragment | GetModulesList_ConfiguratorModule_Fragment | GetModulesList_ConsentsAndDeclarationsModule_Fragment | GetModulesList_CtsModule_Fragment | GetModulesList_DocusignModule_Fragment | GetModulesList_EventApplicationModule_Fragment | GetModulesList_FinderApplicationPrivateModule_Fragment | GetModulesList_FinderApplicationPublicModule_Fragment | GetModulesList_FinderVehicleManagementModule_Fragment | GetModulesList_FiservPaymentModule_Fragment | GetModulesList_GiftVoucherModule_Fragment | GetModulesList_InsuranceModule_Fragment | GetModulesList_LabelsModule_Fragment | GetModulesList_LaunchPadModule_Fragment | GetModulesList_LocalCustomerManagementModule_Fragment | GetModulesList_MaintenanceModule_Fragment | GetModulesList_MarketingModule_Fragment | GetModulesList_MobilityModule_Fragment | GetModulesList_MyInfoModule_Fragment | GetModulesList_NamirialSigningModule_Fragment | GetModulesList_OidcModule_Fragment | GetModulesList_PayGatePaymentModule_Fragment | GetModulesList_PorscheIdModule_Fragment | GetModulesList_PorscheMasterDataModule_Fragment | GetModulesList_PorschePaymentModule_Fragment | GetModulesList_PorscheRetainModule_Fragment | GetModulesList_PromoCodeModule_Fragment | GetModulesList_SalesControlBoardModule_Fragment | GetModulesList_SalesOfferModule_Fragment | GetModulesList_SimpleVehicleManagementModule_Fragment | GetModulesList_StandardApplicationModule_Fragment | GetModulesList_TradeInModule_Fragment | GetModulesList_TtbPaymentModule_Fragment | GetModulesList_UserlikeChatbotModule_Fragment | GetModulesList_VehicleDataWithPorscheCodeIntegrationModule_Fragment | GetModulesList_VisitAppointmentModule_Fragment | GetModulesList_WebsiteModule_Fragment | GetModulesList_WhatsappLiveChatModule_Fragment;

export const GetModulesListFragmentDoc = /*#__PURE__*/ gql`
    fragment GetModulesList on Module {
  ...ModuleListData
  ... on MobilityModule {
    homeDelivery {
      isEnable
    }
    locations {
      ...MobilityLocationData
    }
  }
}
    `;