import type * as SchemaTypes from '../types';

import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from './LocalCustomerManagementModuleKycFieldSpecs';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { TimeSlotDataFragment } from './TimeSlotData';
import { gql } from '@apollo/client';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from './LocalCustomerManagementModuleKycFieldSpecs';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
export type EventModuleData_AdyenPaymentModule_Fragment = (
  { __typename: 'AdyenPaymentModule' }
  & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_AppointmentModule_Fragment = (
  { __typename: 'AppointmentModule' }
  & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_AutoplayModule_Fragment = (
  { __typename: 'AutoplayModule' }
  & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_BankModule_Fragment = (
  { __typename: 'BankModule' }
  & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_BasicSigningModule_Fragment = (
  { __typename: 'BasicSigningModule' }
  & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_CapModule_Fragment = (
  { __typename: 'CapModule' }
  & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_ConfiguratorModule_Fragment = (
  { __typename: 'ConfiguratorModule' }
  & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_ConsentsAndDeclarationsModule_Fragment = (
  { __typename: 'ConsentsAndDeclarationsModule' }
  & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_CtsModule_Fragment = (
  { __typename: 'CtsModule' }
  & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_DocusignModule_Fragment = (
  { __typename: 'DocusignModule' }
  & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_EventApplicationModule_Fragment = (
  { __typename: 'EventApplicationModule' }
  & Pick<SchemaTypes.EventApplicationModule, 'showResetKYCButton' | 'showRemoteFlowButtonInKYCPage' | 'displayAppointmentDatepicker' | 'displayVisitAppointmentDatepicker' | 'vehicleModuleId' | 'liveChatSettingId' | 'capModuleId' | 'id' | 'displayName'>
  & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
    { __typename: 'LocalCustomerManagementModule' }
    & { kycFields: Array<(
      { __typename: 'LocalCustomerManagementModuleKycField' }
      & LocalCustomerManagementModuleKycFieldSpecsFragment
    )>, extraSettings: (
      { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
      & Pick<SchemaTypes.LocalCustomerManagementKycFieldsExtraConfig, 'mobileVerification'>
    ) }
  ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, agreementsModule: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id'>
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id'>
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id'>
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id'>
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id'>
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id'>
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id'>
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id'>
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id'>
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id'>
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id'>
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id'>
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id'>
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id'>
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id'>
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id'>
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id'>
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id'>
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id'>
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id'>
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id'>
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id'>
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id'>
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id'>
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id'>
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id'>
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id'>
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id'>
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id'>
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
  ), appointmentModule?: SchemaTypes.Maybe<(
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'unavailableDayOfWeek' | 'advancedBookingLimit' | 'maxAdvancedBookingLimit' | 'hasTestDriveProcess' | 'hasTestDriveSigning' | 'timeToSendReminder' | 'isReminderTimeEnabled' | 'id' | 'displayName'>
    & { bookingTimeSlot: Array<(
      { __typename: 'AppointmentTimeSlot' }
      & AppointmentTimeSlotDataFragment
    )>, bookingInformation?: SchemaTypes.Maybe<(
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  )>, visitAppointmentModule?: SchemaTypes.Maybe<(
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'unavailableDayOfWeek' | 'advancedBookingLimit' | 'maxAdvancedBookingLimit' | 'id' | 'displayName'>
    & { bookingTimeSlot: Array<(
      { __typename: 'TimeSlot' }
      & TimeSlotDataFragment
    )>, bookingInformation?: SchemaTypes.Maybe<(
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
      & CompanyInModuleOptionDataFragment
    ) }
  )>, dealerVehicles: Array<(
    { __typename: 'DealerVehicles' }
    & Pick<SchemaTypes.DealerVehicles, 'dealerId' | 'vehicleSuiteIds'>
  )>, liveChatSetting?: SchemaTypes.Maybe<(
    { __typename: 'UserlikeChatbotSetting' }
    & Pick<SchemaTypes.UserlikeChatbotSetting, 'script' | 'id'>
  ) | (
    { __typename: 'WhatsappLiveChatSetting' }
    & Pick<SchemaTypes.WhatsappLiveChatSetting, 'link' | 'id'>
  )>, company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_FinderApplicationPrivateModule_Fragment = (
  { __typename: 'FinderApplicationPrivateModule' }
  & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_FinderApplicationPublicModule_Fragment = (
  { __typename: 'FinderApplicationPublicModule' }
  & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_FinderVehicleManagementModule_Fragment = (
  { __typename: 'FinderVehicleManagementModule' }
  & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_FiservPaymentModule_Fragment = (
  { __typename: 'FiservPaymentModule' }
  & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_GiftVoucherModule_Fragment = (
  { __typename: 'GiftVoucherModule' }
  & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_InsuranceModule_Fragment = (
  { __typename: 'InsuranceModule' }
  & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_LabelsModule_Fragment = (
  { __typename: 'LabelsModule' }
  & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_LaunchPadModule_Fragment = (
  { __typename: 'LaunchPadModule' }
  & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_LocalCustomerManagementModule_Fragment = (
  { __typename: 'LocalCustomerManagementModule' }
  & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_MaintenanceModule_Fragment = (
  { __typename: 'MaintenanceModule' }
  & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_MarketingModule_Fragment = (
  { __typename: 'MarketingModule' }
  & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_MobilityModule_Fragment = (
  { __typename: 'MobilityModule' }
  & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_MyInfoModule_Fragment = (
  { __typename: 'MyInfoModule' }
  & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_NamirialSigningModule_Fragment = (
  { __typename: 'NamirialSigningModule' }
  & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_OidcModule_Fragment = (
  { __typename: 'OIDCModule' }
  & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_PayGatePaymentModule_Fragment = (
  { __typename: 'PayGatePaymentModule' }
  & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_PorscheIdModule_Fragment = (
  { __typename: 'PorscheIdModule' }
  & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_PorscheMasterDataModule_Fragment = (
  { __typename: 'PorscheMasterDataModule' }
  & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_PorschePaymentModule_Fragment = (
  { __typename: 'PorschePaymentModule' }
  & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_PorscheRetainModule_Fragment = (
  { __typename: 'PorscheRetainModule' }
  & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_PromoCodeModule_Fragment = (
  { __typename: 'PromoCodeModule' }
  & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_SalesControlBoardModule_Fragment = (
  { __typename: 'SalesControlBoardModule' }
  & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_SalesOfferModule_Fragment = (
  { __typename: 'SalesOfferModule' }
  & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_SimpleVehicleManagementModule_Fragment = (
  { __typename: 'SimpleVehicleManagementModule' }
  & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_StandardApplicationModule_Fragment = (
  { __typename: 'StandardApplicationModule' }
  & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_TradeInModule_Fragment = (
  { __typename: 'TradeInModule' }
  & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_TtbPaymentModule_Fragment = (
  { __typename: 'TtbPaymentModule' }
  & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_UserlikeChatbotModule_Fragment = (
  { __typename: 'UserlikeChatbotModule' }
  & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_VehicleDataWithPorscheCodeIntegrationModule_Fragment = (
  { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
  & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_VisitAppointmentModule_Fragment = (
  { __typename: 'VisitAppointmentModule' }
  & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_WebsiteModule_Fragment = (
  { __typename: 'WebsiteModule' }
  & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleData_WhatsappLiveChatModule_Fragment = (
  { __typename: 'WhatsappLiveChatModule' }
  & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export type EventModuleDataFragment = EventModuleData_AdyenPaymentModule_Fragment | EventModuleData_AppointmentModule_Fragment | EventModuleData_AutoplayModule_Fragment | EventModuleData_BankModule_Fragment | EventModuleData_BasicSigningModule_Fragment | EventModuleData_CapModule_Fragment | EventModuleData_ConfiguratorModule_Fragment | EventModuleData_ConsentsAndDeclarationsModule_Fragment | EventModuleData_CtsModule_Fragment | EventModuleData_DocusignModule_Fragment | EventModuleData_EventApplicationModule_Fragment | EventModuleData_FinderApplicationPrivateModule_Fragment | EventModuleData_FinderApplicationPublicModule_Fragment | EventModuleData_FinderVehicleManagementModule_Fragment | EventModuleData_FiservPaymentModule_Fragment | EventModuleData_GiftVoucherModule_Fragment | EventModuleData_InsuranceModule_Fragment | EventModuleData_LabelsModule_Fragment | EventModuleData_LaunchPadModule_Fragment | EventModuleData_LocalCustomerManagementModule_Fragment | EventModuleData_MaintenanceModule_Fragment | EventModuleData_MarketingModule_Fragment | EventModuleData_MobilityModule_Fragment | EventModuleData_MyInfoModule_Fragment | EventModuleData_NamirialSigningModule_Fragment | EventModuleData_OidcModule_Fragment | EventModuleData_PayGatePaymentModule_Fragment | EventModuleData_PorscheIdModule_Fragment | EventModuleData_PorscheMasterDataModule_Fragment | EventModuleData_PorschePaymentModule_Fragment | EventModuleData_PorscheRetainModule_Fragment | EventModuleData_PromoCodeModule_Fragment | EventModuleData_SalesControlBoardModule_Fragment | EventModuleData_SalesOfferModule_Fragment | EventModuleData_SimpleVehicleManagementModule_Fragment | EventModuleData_StandardApplicationModule_Fragment | EventModuleData_TradeInModule_Fragment | EventModuleData_TtbPaymentModule_Fragment | EventModuleData_UserlikeChatbotModule_Fragment | EventModuleData_VehicleDataWithPorscheCodeIntegrationModule_Fragment | EventModuleData_VisitAppointmentModule_Fragment | EventModuleData_WebsiteModule_Fragment | EventModuleData_WhatsappLiveChatModule_Fragment;

export const EventModuleDataFragmentDoc = /*#__PURE__*/ gql`
    fragment EventModuleData on Module {
  id
  displayName
  company {
    ...CompanyInModuleOptionData
    timeZone
  }
  ... on EventApplicationModule {
    showResetKYCButton
    customerModule {
      ... on LocalCustomerManagementModule {
        kycFields {
          ...LocalCustomerManagementModuleKycFieldSpecs
        }
        extraSettings {
          mobileVerification
        }
      }
    }
    agreementsModule {
      id
    }
    showRemoteFlowButtonInKYCPage
    appointmentModule {
      id
      displayName
      ... on AppointmentModule {
        unavailableDayOfWeek
        bookingTimeSlot {
          ...AppointmentTimeSlotData
        }
        advancedBookingLimit
        maxAdvancedBookingLimit
        bookingInformation {
          ...TranslatedStringData
        }
        hasTestDriveProcess
        hasTestDriveSigning
        timeToSendReminder
        isReminderTimeEnabled
      }
      company {
        ...CompanyInModuleOptionData
        timeZone
        countryCode
      }
    }
    visitAppointmentModule {
      id
      displayName
      ... on VisitAppointmentModule {
        unavailableDayOfWeek
        bookingTimeSlot {
          ...TimeSlotData
        }
        advancedBookingLimit
        maxAdvancedBookingLimit
        bookingInformation {
          ...TranslatedStringData
        }
      }
      company {
        ...CompanyInModuleOptionData
        timeZone
        countryCode
      }
    }
    displayAppointmentDatepicker
    displayVisitAppointmentDatepicker
    vehicleModuleId
    dealerVehicles {
      dealerId
      vehicleSuiteIds
    }
    liveChatSettingId
    liveChatSetting {
      id
      ... on WhatsappLiveChatSetting {
        link
      }
      ... on UserlikeChatbotSetting {
        script
      }
    }
    capModuleId
  }
}
    `;