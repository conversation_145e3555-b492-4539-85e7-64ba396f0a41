import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
export type FinderVehiclesListDataFragment = (
  { __typename: 'FinderVehicle' }
  & Pick<SchemaTypes.FinderVehicle, 'id' | 'status'>
  & { name: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'suiteId' | 'isLatest'>
  ), setting: (
    { __typename: 'FinderVehicleSetting' }
    & Pick<SchemaTypes.FinderVehicleSetting, 'isInspected' | 'isStateHidden' | 'functionality'>
  ), module: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | (
    { __typename: 'FinderVehicleManagementModule' }
    & { setting: (
      { __typename: 'FinderVehicleManagementSetting' }
      & Pick<SchemaTypes.FinderVehicleManagementSetting, 'allowLTA'>
    ), company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, listing: (
    { __typename: 'Listing' }
    & Pick<SchemaTypes.Listing, 'id'>
    & { seller: (
      { __typename: 'Seller' }
      & { name: (
        { __typename: 'LocalizedString' }
        & Pick<SchemaTypes.LocalizedString, 'localize'>
      ) }
    ), price: (
      { __typename: 'Price' }
      & Pick<SchemaTypes.Price, 'value'>
    ), vehicle: (
      { __typename: 'PorscheFinderVehicle' }
      & Pick<SchemaTypes.PorscheFinderVehicle, 'orderTypeCode'>
      & { exteriorColor: (
        { __typename: 'ExteriorColor' }
        & { name: (
          { __typename: 'LocalizedString' }
          & Pick<SchemaTypes.LocalizedString, 'localize'>
        ) }
      ), name: (
        { __typename: 'LocalizedString' }
        & Pick<SchemaTypes.LocalizedString, 'localize'>
      ), condition: (
        { __typename: 'LocalizedValue' }
        & Pick<SchemaTypes.LocalizedValue, 'value'>
      ) }
    ), warranty?: SchemaTypes.Maybe<(
      { __typename: 'Warranty' }
      & Pick<SchemaTypes.Warranty, 'porscheApproved'>
    )> }
  ), lta?: SchemaTypes.Maybe<(
    { __typename: 'LTAIntegration' }
    & Pick<SchemaTypes.LtaIntegration, 'make'>
  )> }
);

export const FinderVehiclesListDataFragmentDoc = /*#__PURE__*/ gql`
    fragment FinderVehiclesListData on FinderVehicle {
  id
  name {
    ...TranslatedStringData
  }
  versioning {
    suiteId
    isLatest
  }
  status
  setting {
    isInspected
    isStateHidden
    functionality
  }
  module {
    ... on FinderVehicleManagementModule {
      setting {
        allowLTA
      }
      company {
        id
        displayName
        currency
        roundings {
          amount {
            decimals
          }
          percentage {
            decimals
          }
        }
      }
    }
  }
  listing {
    id
    seller {
      name {
        localize
      }
    }
    price {
      value
    }
    vehicle {
      orderTypeCode
      exteriorColor {
        name {
          localize
        }
      }
      name {
        localize
      }
      condition {
        value
      }
    }
    warranty {
      porscheApproved
    }
  }
  lta {
    make
  }
}
    `;