import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
export type FinderVehicleDataFragment = (
  { __typename: 'FinderVehicle' }
  & Pick<SchemaTypes.FinderVehicle, 'id' | 'status' | 'url' | 'monthlyInstalment' | 'isDeleted' | 'isActive'>
  & { name: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'suiteId' | 'isLatest'>
  ), autoplay?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicleAutoplay' }
    & Pick<SchemaTypes.FinderVehicleAutoplay, 'listingId' | 'lastUpdated'>
  )>, setting: (
    { __typename: 'FinderVehicleSetting' }
    & Pick<SchemaTypes.FinderVehicleSetting, 'isInspected' | 'isStateHidden' | 'functionality' | 'isTestDriveButtonHidden'>
  ), module: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | (
    { __typename: 'FinderVehicleManagementModule' }
    & { setting: (
      { __typename: 'FinderVehicleManagementSetting' }
      & Pick<SchemaTypes.FinderVehicleManagementSetting, 'allowLTA'>
    ) }
  ) | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, lta?: SchemaTypes.Maybe<(
    { __typename: 'LTAIntegration' }
    & Pick<SchemaTypes.LtaIntegration, 'ownerId' | 'make' | 'coeExpiryDate' | 'originalRegistrationDate' | 'omv'>
  )>, listing: (
    { __typename: 'Listing' }
    & Pick<SchemaTypes.Listing, 'id'>
    & { seller: (
      { __typename: 'Seller' }
      & Pick<SchemaTypes.Seller, 'porschePartnerNumber'>
      & { name: (
        { __typename: 'LocalizedString' }
        & Pick<SchemaTypes.LocalizedString, 'localize'>
      ) }
    ), price: (
      { __typename: 'Price' }
      & Pick<SchemaTypes.Price, 'value'>
    ), vehicle: (
      { __typename: 'PorscheFinderVehicle' }
      & Pick<SchemaTypes.PorscheFinderVehicle, 'orderTypeCode' | 'numberOfPreviousOwners' | 'accidentFree'>
      & { interior?: SchemaTypes.Maybe<(
        { __typename: 'Interior' }
        & { name: (
          { __typename: 'LocalizedString' }
          & Pick<SchemaTypes.LocalizedString, 'localize'>
        ), color?: SchemaTypes.Maybe<(
          { __typename: 'InteriorColorAttributes' }
          & Pick<SchemaTypes.InteriorColorAttributes, 'cockpitColorHexCode' | 'cockpitStitchingColorHexCode' | 'seatColorHexCode' | 'seatStitchingColorHexCode'>
        )> }
      )>, exteriorColor: (
        { __typename: 'ExteriorColor' }
        & Pick<SchemaTypes.ExteriorColor, 'value'>
        & { name: (
          { __typename: 'LocalizedString' }
          & Pick<SchemaTypes.LocalizedString, 'localize'>
        ) }
      ), name: (
        { __typename: 'LocalizedString' }
        & Pick<SchemaTypes.LocalizedString, 'localize'>
      ), condition: (
        { __typename: 'LocalizedValue' }
        & Pick<SchemaTypes.LocalizedValue, 'value'>
      ), mileage: (
        { __typename: 'NumberUnit' }
        & Pick<SchemaTypes.NumberUnit, 'localize'>
      ), transmission: (
        { __typename: 'LocalizedValue' }
        & Pick<SchemaTypes.LocalizedValue, 'value' | 'localize'>
      ), images: (
        { __typename: 'VehicleImagesConnection' }
        & { edges: Array<(
          { __typename: 'VehicleImageTypeEdge' }
          & { node: (
            { __typename: 'VehicleImage' }
            & Pick<SchemaTypes.VehicleImage, 'id'>
            & { variants: Array<(
              { __typename: 'VehicleImageVariant' }
              & Pick<SchemaTypes.VehicleImageVariant, 'height' | 'url' | 'width'>
            )> }
          ) }
        )> }
      ), firstRegistrationDate?: SchemaTypes.Maybe<(
        { __typename: 'FormattedDate' }
        & Pick<SchemaTypes.FormattedDate, 'value' | 'localize'>
      )>, power?: SchemaTypes.Maybe<(
        { __typename: 'Power' }
        & { kilowatt: (
          { __typename: 'NumberUnit' }
          & Pick<SchemaTypes.NumberUnit, 'localize'>
        ), horsepower: (
          { __typename: 'NumberUnit' }
          & Pick<SchemaTypes.NumberUnit, 'localize'>
        ) }
      )>, engineType?: SchemaTypes.Maybe<(
        { __typename: 'LocalizedValue' }
        & Pick<SchemaTypes.LocalizedValue, 'localize' | 'value'>
      )>, displacement?: SchemaTypes.Maybe<(
        { __typename: 'Displacement' }
        & { cubicCentimeter: (
          { __typename: 'NumberUnit' }
          & Pick<SchemaTypes.NumberUnit, 'value'>
        ) }
      )>, modelSeries?: SchemaTypes.Maybe<(
        { __typename: 'LocalizedValue' }
        & Pick<SchemaTypes.LocalizedValue, 'value' | 'localize'>
      )>, modelCategory?: SchemaTypes.Maybe<(
        { __typename: 'LocalizedValue' }
        & Pick<SchemaTypes.LocalizedValue, 'value' | 'localize'>
      )> }
    ), warranty?: SchemaTypes.Maybe<(
      { __typename: 'Warranty' }
      & Pick<SchemaTypes.Warranty, 'porscheApproved'>
    )> }
  ) }
);

export const FinderVehicleDataFragmentDoc = /*#__PURE__*/ gql`
    fragment FinderVehicleData on FinderVehicle {
  __typename
  id
  name {
    ...TranslatedStringData
  }
  versioning {
    suiteId
    isLatest
  }
  autoplay {
    listingId
    lastUpdated
  }
  status
  setting {
    isInspected
    isStateHidden
    functionality
    isTestDriveButtonHidden
  }
  module {
    ... on FinderVehicleManagementModule {
      setting {
        allowLTA
      }
    }
  }
  lta {
    ownerId
  }
  listing(isRemote: true) {
    id
    seller {
      porschePartnerNumber
      name {
        localize
      }
    }
    price {
      value
    }
    vehicle {
      orderTypeCode
      interior {
        name {
          localize
        }
        color {
          cockpitColorHexCode
          cockpitStitchingColorHexCode
          seatColorHexCode
          seatStitchingColorHexCode
        }
      }
      exteriorColor {
        name {
          localize
        }
        value
      }
      name {
        localize
      }
      condition {
        value
      }
      mileage {
        localize
      }
      transmission {
        value
        localize
      }
      images {
        edges {
          node {
            id
            variants {
              height
              url
              width
            }
          }
        }
      }
      numberOfPreviousOwners
      firstRegistrationDate {
        value
        localize
      }
      power {
        kilowatt {
          localize
        }
        horsepower {
          localize
        }
      }
      accidentFree
      engineType {
        localize
        value
      }
      displacement {
        cubicCentimeter {
          value
        }
      }
      modelSeries {
        value
        localize
      }
      modelCategory {
        value
        localize
      }
    }
    warranty {
      porscheApproved
    }
  }
  lta {
    make
    coeExpiryDate
    originalRegistrationDate
    omv
  }
  url
  monthlyInstalment(
    bankModuleId: $bankModuleId
    applicationModuleIds: $applicationModuleIds
    dealerId: $dealerId
    assignedOnly: true
  )
  isDeleted
  isActive
}
    `;