import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type ApplicationQuotationOptionDataFragment = (
  { __typename: 'ApplicationQuotationOption' }
  & Pick<SchemaTypes.ApplicationQuotationOption, 'amountIncludingVat' | 'description' | 'isVatIncluded' | 'amount'>
);

export type ApplicationQuotationDataFragment = (
  { __typename: 'EnbdApplicationQuotation' }
  & Pick<SchemaTypes.EnbdApplicationQuotation, 'applicantName' | 'commissionNumber' | 'engineNumber' | 'chassisNumber' | 'exteriorColor' | 'downPaymentTo' | 'companyName' | 'financeManagerName' | 'vatRate' | 'source'>
  & { options: Array<(
    { __typename: 'ApplicationQuotationOption' }
    & ApplicationQuotationOptionDataFragment
  )> }
);

export const ApplicationQuotationOptionDataFragmentDoc = /*#__PURE__*/ gql`
    fragment ApplicationQuotationOptionData on ApplicationQuotationOption {
  amountIncludingVat
  description
  isVatIncluded
  amount
}
    `;
export const ApplicationQuotationDataFragmentDoc = /*#__PURE__*/ gql`
    fragment ApplicationQuotationData on ApplicationQuotation {
  source
  ... on EnbdApplicationQuotation {
    applicantName
    commissionNumber
    engineNumber
    chassisNumber
    exteriorColor
    options {
      ...ApplicationQuotationOptionData
    }
    downPaymentTo
    companyName
    financeManagerName
    vatRate
  }
}
    `;