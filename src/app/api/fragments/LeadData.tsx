import type * as SchemaTypes from '../types';

import type { StandardLeadDataFragment } from './StandardLeadData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { FinderLeadDataFragment } from './FinderLeadData';
import type { EventLeadDataFragment } from './EventLeadData';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { ApplicationEventCustomizedFieldDataFragment } from './ApplicationEventCustomizedFieldData';
import type { LaunchpadLeadDataFragment } from './LaunchpadLeadData';
import type { ConfiguratorLeadDataFragment } from './ConfiguratorLeadData';
import type { ConfiguratorJourneyBlocksData_ApplicationConfiguratorColorSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorOptionSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorPackageSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorTrimSetting_Fragment } from './ConfiguratorJourneyBlocksData';
import type { BlockDetails_ColorBlock_Fragment, BlockDetails_OptionsBlock_Fragment, BlockDetails_PackageBlock_Fragment, BlockDetails_TrimBlock_Fragment } from './BlockDetails';
import type { OptionSettingDetails_ComboOptionSettings_Fragment, OptionSettingDetails_DropdownOptionSettings_Fragment, OptionSettingDetails_MultiSelectOptionSettings_Fragment, OptionSettingDetails_SingleSelectOptionSettings_Fragment } from './OptionSettingDetails';
import type { MobilityLeadDataFragment } from './MobilityLeadData';
import type { CustomerSpecs_CorporateCustomer_Fragment, CustomerSpecs_Guarantor_Fragment, CustomerSpecs_LocalCustomer_Fragment } from './CustomerSpecs';
import type { LocalCustomerDataFragment } from './LocalCustomerData';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from './LocalCustomerFieldData';
import type { CorporateCustomerDataFragment } from './CorporateCustomerData';
import type { GuarantorDataFragment } from './GuarantorData';
import type { UsersOptionsDataFragment } from './UsersOptionsData';
import type { KycFieldSpecsFragment } from './KYCFieldSpecs';
import type { LaunchPadModuleSpecsFragment } from './LaunchPadModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { AppointmentModuleSpecsFragment } from './AppointmentModuleSpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { NamirialSigningModuleSpecsFragment } from './NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from './NamirialSettingsSpec';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { VisitAppointmentModuleSpecsFragment } from './VisitAppointmentModuleSpecs';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from './VisitAppointmentModuleEmailContentsSpecs';
import type { CounterSettingsSpecsFragment } from './CounterSettingsSpecs';
import type { DealerVehiclesSpecsFragment } from './DealerVehiclesSpecs';
import type { ApplicationAgreementData_CheckboxApplicationAgreement_Fragment, ApplicationAgreementData_MarketingApplicationAgreement_Fragment, ApplicationAgreementData_TextApplicationAgreement_Fragment } from './ApplicationAgreementData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './BaseConditionSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { MarketingPlatformSpecsFragment } from './MarketingPlatformSpecs';
import type { MarketingPlatformsAgreedSpecsFragment } from './MarketingPlatformsAgreedSpecs';
import type { TradeInVehicleDataFragment } from './TradeInVehicleData';
import type { DealerApplicationFragmentFragment } from './DealerApplicationFragment';
import type { DealerContactFragmentFragment } from './DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from './DealerSocialMediaFragment';
import type { DealerDisclaimersFragmentFragment } from './DealerDisclaimersFragment';
import type { DealerIntegrationDetailsFragmentFragment } from './DealerIntegrationDetailsFragment';
import type { ApplicationDocumentDataFragment } from './ApplicationDocumentData';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from './ReferenceApplicationData';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from './ApplicationStageData';
import type { SalesOfferSpecsFragment } from './SalesOfferSpecs';
import type { VehicleSalesOfferSpecsFragment } from './VehicleSalesOfferSpecs';
import type { PorscheVehicleDataSpecsFragment, PorscheVehicleDataFeatureSpecsFragment, PorscheVehicleImagesSpecsFragment } from './PorscheVehicleDataSpecs';
import type { LocalFittedOptionsSpecsFragment } from './LocalFittedOptionsSpecs';
import type { SalesOfferDocumentDataFragment } from './SalesOfferDocumentData';
import type { MainDetailsSalesOfferSpecsFragment } from './MainDetailsSalesOfferSpecs';
import type { TradeInSalesOfferSpecsFragment } from './TradeInSalesOfferSpecs';
import type { FinanceSalesOfferSpecsFragment } from './FinanceSalesOfferSpecs';
import type { ApplicationFinancingData_DefaultApplicationFinancing_Fragment, ApplicationFinancingData_NewZealandApplicationFinancing_Fragment, ApplicationFinancingData_SingaporeApplicationFinancing_Fragment } from './ApplicationFinancingData';
import type { InsuranceSalesOfferSpecsFragment } from './InsuranceSalesOfferSpecs';
import type { ApplicationInsurancingData_DefaultApplicationInsurancing_Fragment, ApplicationInsurancingData_NewZealandApplicationInsurancing_Fragment, ApplicationInsurancingData_SingaporeApplicationInsurancing_Fragment } from './ApplicationInsurancingData';
import type { DepositSalesOfferSpecsFragment } from './DepositSalesOfferSpecs';
import type { VsaSalesOfferSpecsFragment } from './VSASalesOfferSpecs';
import type { SalesOfferModuleSpecsFragment } from './SalesOfferModuleSpecs';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { BankDetailsDataFragment } from './BankDetailsData';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from './BankIntegrationData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from './FinanceProductDetailsData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from './PeriodData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import type { DealerFinanceProductsSpecsFragment } from './DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from './FinanceProductListData';
import type { DealerInsuranceProductsSpecsFragment } from './DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from './InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from './ErgoLookupTableSettingDetails';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from './SalesOfferModuleEmailContentsSpecs';
import type { SalesOfferKycPresetSpecsFragment } from './SalesOfferKYCPresetSpecs';
import type { SalesOfferConsentsSpecsFragment } from './SalesOfferConsentsSpecs';
import type { SalesOfferSigningsSpecsFragment } from './SalesOfferSigningsSpecs';
import type { NamirialSigningDataFragment } from './NamirialSigningData';
import { gql } from '@apollo/client';
import { StandardLeadDataFragmentDoc } from './StandardLeadData';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { FinderLeadDataFragmentDoc } from './FinderLeadData';
import { EventLeadDataFragmentDoc } from './EventLeadData';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { ApplicationEventCustomizedFieldDataFragmentDoc } from './ApplicationEventCustomizedFieldData';
import { LaunchpadLeadDataFragmentDoc } from './LaunchpadLeadData';
import { ConfiguratorLeadDataFragmentDoc } from './ConfiguratorLeadData';
import { ConfiguratorJourneyBlocksDataFragmentDoc } from './ConfiguratorJourneyBlocksData';
import { BlockDetailsFragmentDoc } from './BlockDetails';
import { OptionSettingDetailsFragmentDoc } from './OptionSettingDetails';
import { MobilityLeadDataFragmentDoc } from './MobilityLeadData';
import { CustomerSpecsFragmentDoc } from './CustomerSpecs';
import { LocalCustomerDataFragmentDoc } from './LocalCustomerData';
import { LocalCustomerFieldDataFragmentDoc } from './LocalCustomerFieldData';
import { CorporateCustomerDataFragmentDoc } from './CorporateCustomerData';
import { GuarantorDataFragmentDoc } from './GuarantorData';
import { UsersOptionsDataFragmentDoc } from './UsersOptionsData';
import { KycFieldSpecsFragmentDoc } from './KYCFieldSpecs';
import { LaunchPadModuleSpecsFragmentDoc } from './LaunchPadModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { AppointmentModuleSpecsFragmentDoc } from './AppointmentModuleSpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { NamirialSigningModuleSpecsFragmentDoc } from './NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from './NamirialSettingsSpec';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { VisitAppointmentModuleSpecsFragmentDoc } from './VisitAppointmentModuleSpecs';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from './VisitAppointmentModuleEmailContentsSpecs';
import { CounterSettingsSpecsFragmentDoc } from './CounterSettingsSpecs';
import { DealerVehiclesSpecsFragmentDoc } from './DealerVehiclesSpecs';
import { ApplicationAgreementDataFragmentDoc } from './ApplicationAgreementData';
import { ConditionSpecsFragmentDoc } from './ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from './BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { MarketingPlatformSpecsFragmentDoc } from './MarketingPlatformSpecs';
import { MarketingPlatformsAgreedSpecsFragmentDoc } from './MarketingPlatformsAgreedSpecs';
import { TradeInVehicleDataFragmentDoc } from './TradeInVehicleData';
import { DealerApplicationFragmentFragmentDoc } from './DealerApplicationFragment';
import { DealerContactFragmentFragmentDoc } from './DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from './DealerSocialMediaFragment';
import { DealerDisclaimersFragmentFragmentDoc } from './DealerDisclaimersFragment';
import { DealerIntegrationDetailsFragmentFragmentDoc } from './DealerIntegrationDetailsFragment';
import { ApplicationDocumentDataFragmentDoc } from './ApplicationDocumentData';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from './ReferenceApplicationData';
import { ApplicationStageDataFragmentDoc } from './ApplicationStageData';
import { SalesOfferSpecsFragmentDoc } from './SalesOfferSpecs';
import { VehicleSalesOfferSpecsFragmentDoc } from './VehicleSalesOfferSpecs';
import { PorscheVehicleDataSpecsFragmentDoc, PorscheVehicleDataFeatureSpecsFragmentDoc, PorscheVehicleImagesSpecsFragmentDoc } from './PorscheVehicleDataSpecs';
import { LocalFittedOptionsSpecsFragmentDoc } from './LocalFittedOptionsSpecs';
import { SalesOfferDocumentDataFragmentDoc } from './SalesOfferDocumentData';
import { MainDetailsSalesOfferSpecsFragmentDoc } from './MainDetailsSalesOfferSpecs';
import { TradeInSalesOfferSpecsFragmentDoc } from './TradeInSalesOfferSpecs';
import { FinanceSalesOfferSpecsFragmentDoc } from './FinanceSalesOfferSpecs';
import { ApplicationFinancingDataFragmentDoc } from './ApplicationFinancingData';
import { InsuranceSalesOfferSpecsFragmentDoc } from './InsuranceSalesOfferSpecs';
import { ApplicationInsurancingDataFragmentDoc } from './ApplicationInsurancingData';
import { DepositSalesOfferSpecsFragmentDoc } from './DepositSalesOfferSpecs';
import { VsaSalesOfferSpecsFragmentDoc } from './VSASalesOfferSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from './SalesOfferModuleSpecs';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { BankDetailsDataFragmentDoc } from './BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from './BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from './FinanceProductDetailsData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
import { DealerFinanceProductsSpecsFragmentDoc } from './DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from './FinanceProductListData';
import { DealerInsuranceProductsSpecsFragmentDoc } from './DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from './InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from './ErgoLookupTableSettingDetails';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from './SalesOfferModuleEmailContentsSpecs';
import { SalesOfferKycPresetSpecsFragmentDoc } from './SalesOfferKYCPresetSpecs';
import { SalesOfferConsentsSpecsFragmentDoc } from './SalesOfferConsentsSpecs';
import { SalesOfferSigningsSpecsFragmentDoc } from './SalesOfferSigningsSpecs';
import { NamirialSigningDataFragmentDoc } from './NamirialSigningData';
export type LeadData_ConfiguratorLead_Fragment = (
  { __typename: 'ConfiguratorLead' }
  & Pick<SchemaTypes.ConfiguratorLead, 'id' | 'dealerId' | 'identifier' | 'status' | 'isLead' | 'permissions' | 'customerId' | 'companyId' | 'moduleId' | 'originSalesConsultantId' | 'vehicleCondition' | 'purchaseIntention' | 'purposeOfVisit' | 'intentType' | 'isCustomerSearchPerformed'>
  & { customer: (
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  ), company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'timeZone'>
  ), assignee?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'id' | 'displayName'>
  )>, availableAssignees: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )>, campaignValues?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationCampaignValues' }
    & Pick<SchemaTypes.ApplicationCampaignValues, 'capCampaignId'>
  )>, capValues?: SchemaTypes.Maybe<(
    { __typename: 'CapValuesOnApplication' }
    & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId' | 'businessPartnerGuid' | 'leadId' | 'leadGuid' | 'salesPersonId' | 'salesPersonName'>
  )>, customerKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'capModuleId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & LaunchPadModuleSpecsFragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), leadModule?: SchemaTypes.Maybe<(
    { __typename: 'LaunchPadModule' }
    & LaunchPadModuleSpecsFragment
  )>, customerAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, tradeInVehicle: Array<(
    { __typename: 'TradeInVehicle' }
    & TradeInVehicleDataFragment
  )>, dealer: (
    { __typename: 'Dealer' }
    & DealerApplicationFragmentFragment
  ), documents: Array<(
    { __typename: 'ApplicationDocument' }
    & ApplicationDocumentDataFragment
  )>, originSalesConsultant?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'displayName'>
  )>, applications: Array<(
    { __typename: 'ConfiguratorApplication' }
    & ReferenceApplicationData_ConfiguratorApplication_Fragment
  ) | (
    { __typename: 'EventApplication' }
    & ReferenceApplicationData_EventApplication_Fragment
  ) | (
    { __typename: 'FinderApplication' }
    & ReferenceApplicationData_FinderApplication_Fragment
  ) | (
    { __typename: 'LaunchpadApplication' }
    & ReferenceApplicationData_LaunchpadApplication_Fragment
  ) | (
    { __typename: 'MobilityApplication' }
    & ReferenceApplicationData_MobilityApplication_Fragment
  ) | (
    { __typename: 'SalesOfferApplication' }
    & ReferenceApplicationData_SalesOfferApplication_Fragment
  ) | (
    { __typename: 'StandardApplication' }
    & ReferenceApplicationData_StandardApplication_Fragment
  )>, salesOffer?: SchemaTypes.Maybe<(
    { __typename: 'SalesOffer' }
    & SalesOfferSpecsFragment
  )> }
  & ConfiguratorLeadDataFragment
);

export type LeadData_EventLead_Fragment = (
  { __typename: 'EventLead' }
  & Pick<SchemaTypes.EventLead, 'id' | 'dealerId' | 'identifier' | 'status' | 'isLead' | 'permissions' | 'customerId' | 'companyId' | 'moduleId' | 'originSalesConsultantId' | 'vehicleCondition' | 'purchaseIntention' | 'purposeOfVisit' | 'intentType' | 'isCustomerSearchPerformed'>
  & { customer: (
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  ), company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'timeZone'>
  ), assignee?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'id' | 'displayName'>
  )>, availableAssignees: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )>, campaignValues?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationCampaignValues' }
    & Pick<SchemaTypes.ApplicationCampaignValues, 'capCampaignId'>
  )>, capValues?: SchemaTypes.Maybe<(
    { __typename: 'CapValuesOnApplication' }
    & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId' | 'businessPartnerGuid' | 'leadId' | 'leadGuid' | 'salesPersonId' | 'salesPersonName'>
  )>, customerKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'capModuleId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & LaunchPadModuleSpecsFragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), leadModule?: SchemaTypes.Maybe<(
    { __typename: 'LaunchPadModule' }
    & LaunchPadModuleSpecsFragment
  )>, customerAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, tradeInVehicle: Array<(
    { __typename: 'TradeInVehicle' }
    & TradeInVehicleDataFragment
  )>, dealer: (
    { __typename: 'Dealer' }
    & DealerApplicationFragmentFragment
  ), documents: Array<(
    { __typename: 'ApplicationDocument' }
    & ApplicationDocumentDataFragment
  )>, originSalesConsultant?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'displayName'>
  )>, applications: Array<(
    { __typename: 'ConfiguratorApplication' }
    & ReferenceApplicationData_ConfiguratorApplication_Fragment
  ) | (
    { __typename: 'EventApplication' }
    & ReferenceApplicationData_EventApplication_Fragment
  ) | (
    { __typename: 'FinderApplication' }
    & ReferenceApplicationData_FinderApplication_Fragment
  ) | (
    { __typename: 'LaunchpadApplication' }
    & ReferenceApplicationData_LaunchpadApplication_Fragment
  ) | (
    { __typename: 'MobilityApplication' }
    & ReferenceApplicationData_MobilityApplication_Fragment
  ) | (
    { __typename: 'SalesOfferApplication' }
    & ReferenceApplicationData_SalesOfferApplication_Fragment
  ) | (
    { __typename: 'StandardApplication' }
    & ReferenceApplicationData_StandardApplication_Fragment
  )>, salesOffer?: SchemaTypes.Maybe<(
    { __typename: 'SalesOffer' }
    & SalesOfferSpecsFragment
  )> }
  & EventLeadDataFragment
);

export type LeadData_FinderLead_Fragment = (
  { __typename: 'FinderLead' }
  & Pick<SchemaTypes.FinderLead, 'id' | 'dealerId' | 'identifier' | 'status' | 'isLead' | 'permissions' | 'customerId' | 'companyId' | 'moduleId' | 'originSalesConsultantId' | 'vehicleCondition' | 'purchaseIntention' | 'purposeOfVisit' | 'intentType' | 'isCustomerSearchPerformed'>
  & { customer: (
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  ), company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'timeZone'>
  ), assignee?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'id' | 'displayName'>
  )>, availableAssignees: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )>, campaignValues?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationCampaignValues' }
    & Pick<SchemaTypes.ApplicationCampaignValues, 'capCampaignId'>
  )>, capValues?: SchemaTypes.Maybe<(
    { __typename: 'CapValuesOnApplication' }
    & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId' | 'businessPartnerGuid' | 'leadId' | 'leadGuid' | 'salesPersonId' | 'salesPersonName'>
  )>, customerKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'capModuleId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & LaunchPadModuleSpecsFragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), leadModule?: SchemaTypes.Maybe<(
    { __typename: 'LaunchPadModule' }
    & LaunchPadModuleSpecsFragment
  )>, customerAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, tradeInVehicle: Array<(
    { __typename: 'TradeInVehicle' }
    & TradeInVehicleDataFragment
  )>, dealer: (
    { __typename: 'Dealer' }
    & DealerApplicationFragmentFragment
  ), documents: Array<(
    { __typename: 'ApplicationDocument' }
    & ApplicationDocumentDataFragment
  )>, originSalesConsultant?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'displayName'>
  )>, applications: Array<(
    { __typename: 'ConfiguratorApplication' }
    & ReferenceApplicationData_ConfiguratorApplication_Fragment
  ) | (
    { __typename: 'EventApplication' }
    & ReferenceApplicationData_EventApplication_Fragment
  ) | (
    { __typename: 'FinderApplication' }
    & ReferenceApplicationData_FinderApplication_Fragment
  ) | (
    { __typename: 'LaunchpadApplication' }
    & ReferenceApplicationData_LaunchpadApplication_Fragment
  ) | (
    { __typename: 'MobilityApplication' }
    & ReferenceApplicationData_MobilityApplication_Fragment
  ) | (
    { __typename: 'SalesOfferApplication' }
    & ReferenceApplicationData_SalesOfferApplication_Fragment
  ) | (
    { __typename: 'StandardApplication' }
    & ReferenceApplicationData_StandardApplication_Fragment
  )>, salesOffer?: SchemaTypes.Maybe<(
    { __typename: 'SalesOffer' }
    & SalesOfferSpecsFragment
  )> }
  & FinderLeadDataFragment
);

export type LeadData_LaunchpadLead_Fragment = (
  { __typename: 'LaunchpadLead' }
  & Pick<SchemaTypes.LaunchpadLead, 'id' | 'dealerId' | 'identifier' | 'status' | 'isLead' | 'permissions' | 'customerId' | 'companyId' | 'moduleId' | 'originSalesConsultantId' | 'vehicleCondition' | 'purchaseIntention' | 'purposeOfVisit' | 'intentType' | 'isCustomerSearchPerformed'>
  & { customer: (
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  ), company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'timeZone'>
  ), assignee?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'id' | 'displayName'>
  )>, availableAssignees: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )>, campaignValues?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationCampaignValues' }
    & Pick<SchemaTypes.ApplicationCampaignValues, 'capCampaignId'>
  )>, capValues?: SchemaTypes.Maybe<(
    { __typename: 'CapValuesOnApplication' }
    & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId' | 'businessPartnerGuid' | 'leadId' | 'leadGuid' | 'salesPersonId' | 'salesPersonName'>
  )>, customerKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'capModuleId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & LaunchPadModuleSpecsFragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), leadModule?: SchemaTypes.Maybe<(
    { __typename: 'LaunchPadModule' }
    & LaunchPadModuleSpecsFragment
  )>, customerAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, tradeInVehicle: Array<(
    { __typename: 'TradeInVehicle' }
    & TradeInVehicleDataFragment
  )>, dealer: (
    { __typename: 'Dealer' }
    & DealerApplicationFragmentFragment
  ), documents: Array<(
    { __typename: 'ApplicationDocument' }
    & ApplicationDocumentDataFragment
  )>, originSalesConsultant?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'displayName'>
  )>, applications: Array<(
    { __typename: 'ConfiguratorApplication' }
    & ReferenceApplicationData_ConfiguratorApplication_Fragment
  ) | (
    { __typename: 'EventApplication' }
    & ReferenceApplicationData_EventApplication_Fragment
  ) | (
    { __typename: 'FinderApplication' }
    & ReferenceApplicationData_FinderApplication_Fragment
  ) | (
    { __typename: 'LaunchpadApplication' }
    & ReferenceApplicationData_LaunchpadApplication_Fragment
  ) | (
    { __typename: 'MobilityApplication' }
    & ReferenceApplicationData_MobilityApplication_Fragment
  ) | (
    { __typename: 'SalesOfferApplication' }
    & ReferenceApplicationData_SalesOfferApplication_Fragment
  ) | (
    { __typename: 'StandardApplication' }
    & ReferenceApplicationData_StandardApplication_Fragment
  )>, salesOffer?: SchemaTypes.Maybe<(
    { __typename: 'SalesOffer' }
    & SalesOfferSpecsFragment
  )> }
  & LaunchpadLeadDataFragment
);

export type LeadData_MobilityLead_Fragment = (
  { __typename: 'MobilityLead' }
  & Pick<SchemaTypes.MobilityLead, 'id' | 'dealerId' | 'identifier' | 'status' | 'isLead' | 'permissions' | 'customerId' | 'companyId' | 'moduleId' | 'originSalesConsultantId' | 'vehicleCondition' | 'purchaseIntention' | 'purposeOfVisit' | 'intentType' | 'isCustomerSearchPerformed'>
  & { customer: (
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  ), company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'timeZone'>
  ), assignee?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'id' | 'displayName'>
  )>, availableAssignees: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )>, campaignValues?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationCampaignValues' }
    & Pick<SchemaTypes.ApplicationCampaignValues, 'capCampaignId'>
  )>, capValues?: SchemaTypes.Maybe<(
    { __typename: 'CapValuesOnApplication' }
    & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId' | 'businessPartnerGuid' | 'leadId' | 'leadGuid' | 'salesPersonId' | 'salesPersonName'>
  )>, customerKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'capModuleId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & LaunchPadModuleSpecsFragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), leadModule?: SchemaTypes.Maybe<(
    { __typename: 'LaunchPadModule' }
    & LaunchPadModuleSpecsFragment
  )>, customerAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, tradeInVehicle: Array<(
    { __typename: 'TradeInVehicle' }
    & TradeInVehicleDataFragment
  )>, dealer: (
    { __typename: 'Dealer' }
    & DealerApplicationFragmentFragment
  ), documents: Array<(
    { __typename: 'ApplicationDocument' }
    & ApplicationDocumentDataFragment
  )>, originSalesConsultant?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'displayName'>
  )>, applications: Array<(
    { __typename: 'ConfiguratorApplication' }
    & ReferenceApplicationData_ConfiguratorApplication_Fragment
  ) | (
    { __typename: 'EventApplication' }
    & ReferenceApplicationData_EventApplication_Fragment
  ) | (
    { __typename: 'FinderApplication' }
    & ReferenceApplicationData_FinderApplication_Fragment
  ) | (
    { __typename: 'LaunchpadApplication' }
    & ReferenceApplicationData_LaunchpadApplication_Fragment
  ) | (
    { __typename: 'MobilityApplication' }
    & ReferenceApplicationData_MobilityApplication_Fragment
  ) | (
    { __typename: 'SalesOfferApplication' }
    & ReferenceApplicationData_SalesOfferApplication_Fragment
  ) | (
    { __typename: 'StandardApplication' }
    & ReferenceApplicationData_StandardApplication_Fragment
  )>, salesOffer?: SchemaTypes.Maybe<(
    { __typename: 'SalesOffer' }
    & SalesOfferSpecsFragment
  )> }
  & MobilityLeadDataFragment
);

export type LeadData_StandardLead_Fragment = (
  { __typename: 'StandardLead' }
  & Pick<SchemaTypes.StandardLead, 'id' | 'dealerId' | 'identifier' | 'status' | 'isLead' | 'permissions' | 'customerId' | 'companyId' | 'moduleId' | 'originSalesConsultantId' | 'vehicleCondition' | 'purchaseIntention' | 'purposeOfVisit' | 'intentType' | 'isCustomerSearchPerformed'>
  & { customer: (
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  ), company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'timeZone'>
  ), assignee?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'id' | 'displayName'>
  )>, availableAssignees: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )>, campaignValues?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationCampaignValues' }
    & Pick<SchemaTypes.ApplicationCampaignValues, 'capCampaignId'>
  )>, capValues?: SchemaTypes.Maybe<(
    { __typename: 'CapValuesOnApplication' }
    & Pick<SchemaTypes.CapValuesOnApplication, 'businessPartnerId' | 'businessPartnerGuid' | 'leadId' | 'leadGuid' | 'salesPersonId' | 'salesPersonName'>
  )>, customerKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'capModuleId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & LaunchPadModuleSpecsFragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'capModuleId' | 'capPrequalification' | 'leadCampaignId' | 'vehicleModuleId' | 'displayName'>
    & { customerModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
      & { extraSettings: (
        { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
        & KycExtraSettingsSpecsFragment
      ) }
    ) | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, dealerVehicles: Array<(
      { __typename: 'DealerVehicles' }
      & DealerVehiclesSpecsFragment
    )>, company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'currency' | 'countryCode' | 'coe' | 'ppsr' | 'estFee'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), leadModule?: SchemaTypes.Maybe<(
    { __typename: 'LaunchPadModule' }
    & LaunchPadModuleSpecsFragment
  )>, customerAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, tradeInVehicle: Array<(
    { __typename: 'TradeInVehicle' }
    & TradeInVehicleDataFragment
  )>, dealer: (
    { __typename: 'Dealer' }
    & DealerApplicationFragmentFragment
  ), documents: Array<(
    { __typename: 'ApplicationDocument' }
    & ApplicationDocumentDataFragment
  )>, originSalesConsultant?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'displayName'>
  )>, applications: Array<(
    { __typename: 'ConfiguratorApplication' }
    & ReferenceApplicationData_ConfiguratorApplication_Fragment
  ) | (
    { __typename: 'EventApplication' }
    & ReferenceApplicationData_EventApplication_Fragment
  ) | (
    { __typename: 'FinderApplication' }
    & ReferenceApplicationData_FinderApplication_Fragment
  ) | (
    { __typename: 'LaunchpadApplication' }
    & ReferenceApplicationData_LaunchpadApplication_Fragment
  ) | (
    { __typename: 'MobilityApplication' }
    & ReferenceApplicationData_MobilityApplication_Fragment
  ) | (
    { __typename: 'SalesOfferApplication' }
    & ReferenceApplicationData_SalesOfferApplication_Fragment
  ) | (
    { __typename: 'StandardApplication' }
    & ReferenceApplicationData_StandardApplication_Fragment
  )>, salesOffer?: SchemaTypes.Maybe<(
    { __typename: 'SalesOffer' }
    & SalesOfferSpecsFragment
  )> }
  & StandardLeadDataFragment
);

export type LeadDataFragment = LeadData_ConfiguratorLead_Fragment | LeadData_EventLead_Fragment | LeadData_FinderLead_Fragment | LeadData_LaunchpadLead_Fragment | LeadData_MobilityLead_Fragment | LeadData_StandardLead_Fragment;

export const LeadDataFragmentDoc = /*#__PURE__*/ gql`
    fragment LeadData on Lead {
  ...StandardLeadData
  ...FinderLeadData
  ...EventLeadData
  ...LaunchpadLeadData
  ...ConfiguratorLeadData
  ...MobilityLeadData
  id
  dealerId
  identifier
  status
  isLead
  permissions
  customerId
  customer {
    ...CustomerSpecs
  }
  companyId
  company {
    id
    displayName
    timeZone
  }
  assignee {
    id
    displayName
  }
  availableAssignees {
    ...UsersOptionsData
  }
  campaignValues {
    capCampaignId
  }
  capValues {
    businessPartnerId
    businessPartnerGuid
    leadId
    leadGuid
    salesPersonId
    salesPersonName
  }
  customerKYC {
    ...KYCFieldSpecs
  }
  versioning {
    createdAt
    updatedAt
    createdBy {
      ...AuthorData
    }
    suiteId
  }
  moduleId
  module {
    id
    displayName
    company {
      id
      timeZone
      currency
      countryCode
      roundings {
        amount {
          decimals
        }
        percentage {
          decimals
        }
      }
      coe
      ppsr
      estFee
    }
    ... on LaunchPadModule {
      ...LaunchPadModuleSpecs
    }
    ... on StandardApplicationModule {
      id
      capModuleId
      capPrequalification
      leadCampaignId
      customerModule {
        ... on LocalCustomerManagementModule {
          id
          displayName
          extraSettings {
            ...KYCExtraSettingsSpecs
          }
        }
      }
      vehicleModuleId
      dealerVehicles {
        ...DealerVehiclesSpecs
      }
    }
    ... on FinderApplicationPrivateModule {
      id
      capModuleId
      capPrequalification
      leadCampaignId
      customerModule {
        ... on LocalCustomerManagementModule {
          id
          displayName
          extraSettings {
            ...KYCExtraSettingsSpecs
          }
        }
      }
      vehicleModuleId
      dealerVehicles {
        ...DealerVehiclesSpecs
      }
    }
    ... on FinderApplicationPublicModule {
      id
      capModuleId
      capPrequalification
      leadCampaignId
      customerModule {
        ... on LocalCustomerManagementModule {
          id
          displayName
          extraSettings {
            ...KYCExtraSettingsSpecs
          }
        }
      }
      vehicleModuleId
      dealerVehicles {
        ...DealerVehiclesSpecs
      }
    }
    ... on ConfiguratorModule {
      id
      capModuleId
      capPrequalification
      leadCampaignId
      customerModule {
        ... on LocalCustomerManagementModule {
          id
          displayName
          extraSettings {
            ...KYCExtraSettingsSpecs
          }
        }
      }
      vehicleModuleId
      dealerVehicles {
        ...DealerVehiclesSpecs
      }
    }
    ... on EventApplicationModule {
      id
      capModuleId
      customerModule {
        ... on LocalCustomerManagementModule {
          id
          displayName
          extraSettings {
            ...KYCExtraSettingsSpecs
          }
        }
      }
      vehicleModuleId
      dealerVehicles {
        ...DealerVehiclesSpecs
      }
    }
  }
  leadModule {
    ...LaunchPadModuleSpecs
  }
  customerAgreements {
    ...ApplicationAgreementData
  }
  tradeInVehicle {
    ...TradeInVehicleData
  }
  dealer {
    ...DealerApplicationFragment
  }
  documents {
    ...ApplicationDocumentData
  }
  originSalesConsultantId
  originSalesConsultant {
    displayName
  }
  applications {
    ...ReferenceApplicationData
  }
  vehicleCondition
  purchaseIntention
  purposeOfVisit
  intentType
  isCustomerSearchPerformed
  salesOffer {
    ...SalesOfferSpecs
  }
}
    `;