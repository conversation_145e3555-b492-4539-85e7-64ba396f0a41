import type * as SchemaTypes from '../types';

import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import { gql } from '@apollo/client';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
export type LaunchpadLeadDataFragment = (
  { __typename: 'LaunchpadLead' }
  & Pick<SchemaTypes.LaunchpadLead, 'contactedDate' | 'qualifiedDate' | 'mergedToLeadSuiteId' | 'mergedToLeadIdentifier'>
  & { vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & VehicleSpecs_FinderVehicle_Fragment
  ) | (
    { __typename: 'LocalMake' }
    & VehicleSpecs_LocalMake_Fragment
  ) | (
    { __typename: 'LocalModel' }
    & VehicleSpecs_LocalModel_Fragment
  ) | (
    { __typename: 'LocalVariant' }
    & VehicleSpecs_LocalVariant_Fragment
  )>, retainInfo?: SchemaTypes.Maybe<(
    { __typename: 'RetainInfo' }
    & { vehicleDetails?: SchemaTypes.Maybe<(
      { __typename: 'RetainVehicleDetail' }
      & Pick<SchemaTypes.RetainVehicleDetail, 'modelType' | 'modelLine' | 'vin'>
    )>, financingDetails?: SchemaTypes.Maybe<(
      { __typename: 'RetainFinancingDetail' }
      & Pick<SchemaTypes.RetainFinancingDetail, 'maturityDate' | 'rv' | 'customerRate' | 'firstPayment' | 'firstPaymentDate' | 'remainingLoan' | 'financingStatus' | 'contractTerm' | 'termRealized' | 'termRemaining' | 'netAmountFinanced' | 'deposit' | 'purchasePrice' | 'latestImportDate'>
    )> }
  )> }
);

export const LaunchpadLeadDataFragmentDoc = /*#__PURE__*/ gql`
    fragment LaunchpadLeadData on LaunchpadLead {
  vehicle {
    ...VehicleSpecs
  }
  retainInfo {
    vehicleDetails {
      modelType
      modelLine
      vin
    }
    financingDetails {
      maturityDate
      rv
      customerRate
      firstPayment
      firstPaymentDate
      remainingLoan
      financingStatus
      contractTerm
      termRealized
      termRemaining
      netAmountFinanced
      deposit
      purchasePrice
      latestImportDate
    }
  }
  contactedDate
  qualifiedDate
  mergedToLeadSuiteId
  mergedToLeadIdentifier
}
    `;