import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { ProductionBankDetailsFragment } from './ProductionBankDetails';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { ProductionBankDetailsFragmentDoc } from './ProductionBankDetails';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
export type ProductionFinanceProductDetails_LocalDeferredPrincipal_Fragment = (
  { __typename: 'LocalDeferredPrincipal' }
  & Pick<SchemaTypes.LocalDeferredPrincipal, 'id' | 'type' | 'allVariantSuiteIds' | 'order' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & ProductionBankDetailsFragment
  ), payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'DeferredPrincipalTermSettings' }
    & TermSettingsDetails_DeferredPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type ProductionFinanceProductDetails_LocalHirePurchase_Fragment = (
  { __typename: 'LocalHirePurchase' }
  & Pick<SchemaTypes.LocalHirePurchase, 'id' | 'type' | 'allVariantSuiteIds' | 'order' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & ProductionBankDetailsFragment
  ), payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type ProductionFinanceProductDetails_LocalHirePurchaseWithBalloon_Fragment = (
  { __typename: 'LocalHirePurchaseWithBalloon' }
  & Pick<SchemaTypes.LocalHirePurchaseWithBalloon, 'id' | 'type' | 'allVariantSuiteIds' | 'order' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier'>
  & { balloon: (
    { __typename: 'BalloonRangeSettings' }
    & BalloonSettingsDetails_BalloonRangeSettings_Fragment
  ) | (
    { __typename: 'BalloonTableSettings' }
    & BalloonSettingsDetails_BalloonTableSettings_Fragment
  ), legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & ProductionBankDetailsFragment
  ), payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type ProductionFinanceProductDetails_LocalHirePurchaseWithBalloonGfv_Fragment = (
  { __typename: 'LocalHirePurchaseWithBalloonGFV' }
  & Pick<SchemaTypes.LocalHirePurchaseWithBalloonGfv, 'id' | 'type' | 'allVariantSuiteIds' | 'order' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier'>
  & { balloon: (
    { __typename: 'BalloonGFVSettings' }
    & BalloonGfvSettingsDetailsFragment
  ), legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & ProductionBankDetailsFragment
  ), payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type ProductionFinanceProductDetails_LocalLease_Fragment = (
  { __typename: 'LocalLease' }
  & Pick<SchemaTypes.LocalLease, 'id' | 'type' | 'allVariantSuiteIds' | 'order' | 'deriveMethod' | 'bankReferenceIdentifier'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & ProductionBankDetailsFragment
  ), lease: (
    { __typename: 'LeaseSettings' }
    & LeaseSettingsDetailsFragment
  ), term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), deposit: (
    { __typename: 'DepositRangeSettings' }
    & DepositSettingsDetails_DepositRangeSettings_Fragment
  ) | (
    { __typename: 'DepositTableSettings' }
    & DepositSettingsDetails_DepositTableSettings_Fragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type ProductionFinanceProductDetails_LocalLeasePurchase_Fragment = (
  { __typename: 'LocalLeasePurchase' }
  & Pick<SchemaTypes.LocalLeasePurchase, 'id' | 'type' | 'allVariantSuiteIds' | 'order' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & ProductionBankDetailsFragment
  ), payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), residualValue: (
    { __typename: 'ResidualValueSettings' }
    & ResidualValueSettingsDetailsFragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
);

export type ProductionFinanceProductDetails_LocalUcclLeasing_Fragment = (
  { __typename: 'LocalUcclLeasing' }
  & Pick<SchemaTypes.LocalUcclLeasing, 'id' | 'type' | 'allVariantSuiteIds' | 'order' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), bank: (
    { __typename: 'SystemBank' }
    & ProductionBankDetailsFragment
  ), payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'currency'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'isLatest' | 'suiteId'>
  ) }
  & LocalUcclLeasingOnlyDetailsFragment
);

export type ProductionFinanceProductDetailsFragment = ProductionFinanceProductDetails_LocalDeferredPrincipal_Fragment | ProductionFinanceProductDetails_LocalHirePurchase_Fragment | ProductionFinanceProductDetails_LocalHirePurchaseWithBalloon_Fragment | ProductionFinanceProductDetails_LocalHirePurchaseWithBalloonGfv_Fragment | ProductionFinanceProductDetails_LocalLease_Fragment | ProductionFinanceProductDetails_LocalLeasePurchase_Fragment | ProductionFinanceProductDetails_LocalUcclLeasing_Fragment;

export const ProductionFinanceProductDetailsFragmentDoc = /*#__PURE__*/ gql`
    fragment ProductionFinanceProductDetails on FinanceProduct {
  ... on LocalHirePurchase {
    id
    type
    allVariantSuiteIds
    order
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    bank {
      ...ProductionBankDetails
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    module {
      id
      company {
        id
        currency
        roundings {
          amount {
            decimals
          }
          percentage {
            decimals
          }
        }
      }
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalHirePurchaseWithBalloon {
    id
    type
    allVariantSuiteIds
    order
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    balloon {
      ...BalloonSettingsDetails
    }
    legalName {
      ...TranslatedStringData
    }
    bank {
      ...ProductionBankDetails
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    module {
      id
      company {
        id
        currency
        roundings {
          amount {
            decimals
          }
          percentage {
            decimals
          }
        }
      }
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalHirePurchaseWithBalloonGFV {
    id
    type
    allVariantSuiteIds
    order
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    balloon {
      ...BalloonGFVSettingsDetails
    }
    legalName {
      ...TranslatedStringData
    }
    bank {
      ...ProductionBankDetails
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    module {
      id
      company {
        id
        currency
        roundings {
          amount {
            decimals
          }
          percentage {
            decimals
          }
        }
      }
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalLease {
    id
    type
    allVariantSuiteIds
    order
    deriveMethod
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    bank {
      ...ProductionBankDetails
    }
    lease {
      ...LeaseSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    deposit {
      ...DepositSettingsDetails
    }
    module {
      id
      company {
        id
        currency
        roundings {
          amount {
            decimals
          }
          percentage {
            decimals
          }
        }
      }
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalLeasePurchase {
    id
    type
    allVariantSuiteIds
    order
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    bank {
      ...ProductionBankDetails
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    module {
      id
      company {
        id
        currency
        roundings {
          amount {
            decimals
          }
          percentage {
            decimals
          }
        }
      }
    }
    residualValue {
      ...ResidualValueSettingsDetails
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalDeferredPrincipal {
    id
    type
    allVariantSuiteIds
    order
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    bank {
      ...ProductionBankDetails
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    module {
      id
      company {
        id
        currency
        roundings {
          amount {
            decimals
          }
          percentage {
            decimals
          }
        }
      }
    }
    versioning {
      isLatest
      suiteId
    }
  }
  ... on LocalUcclLeasing {
    id
    type
    allVariantSuiteIds
    order
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    legalName {
      ...TranslatedStringData
    }
    bank {
      ...ProductionBankDetails
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    ...LocalUcclLeasingOnlyDetails
    module {
      id
      company {
        id
        currency
        roundings {
          amount {
            decimals
          }
          percentage {
            decimals
          }
        }
      }
    }
    versioning {
      isLatest
      suiteId
    }
  }
}
    `;