import type * as SchemaTypes from '../types';

import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from './ApplicationStageData';
import type { CustomerSpecs_CorporateCustomer_Fragment, CustomerSpecs_Guarantor_Fragment, CustomerSpecs_LocalCustomer_Fragment } from './CustomerSpecs';
import type { LocalCustomerDataFragment } from './LocalCustomerData';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from './LocalCustomerFieldData';
import type { CorporateCustomerDataFragment } from './CorporateCustomerData';
import type { GuarantorDataFragment } from './GuarantorData';
import type { DraftFlowConfigurationSpecFragment } from './DraftFlowConfigurationSpec';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { LaunchpadModuleSpecsForApplicationFragment } from './LaunchpadModuleSpecsForApplication';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { LaunchpadApplicationConfigurationDataFragment } from './LaunchpadApplicationConfigurationData';
import type { TradeInVehicleDataFragment } from './TradeInVehicleData';
import type { ApplicationDocumentDataFragment } from './ApplicationDocumentData';
import type { UsersOptionsDataFragment } from './UsersOptionsData';
import type { DealerApplicationFragmentFragment } from './DealerApplicationFragment';
import type { DealerContactFragmentFragment } from './DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from './DealerSocialMediaFragment';
import type { DealerDisclaimersFragmentFragment } from './DealerDisclaimersFragment';
import type { DealerIntegrationDetailsFragmentFragment } from './DealerIntegrationDetailsFragment';
import type { EndpointContextData_ApplicationListEndpoint_Fragment, EndpointContextData_ConfiguratorApplicationEntrypoint_Fragment, EndpointContextData_CustomerListEndpoint_Fragment, EndpointContextData_DummyPrivatePageEndpoint_Fragment, EndpointContextData_DummyWelcomePageEndpoint_Fragment, EndpointContextData_EventApplicationEntrypoint_Fragment, EndpointContextData_FinderApplicationEntrypoint_Fragment, EndpointContextData_FinderApplicationPublicAccessEntrypoint_Fragment, EndpointContextData_LaunchPadApplicationEntrypoint_Fragment, EndpointContextData_LeadListEndpoint_Fragment, EndpointContextData_MobilityApplicationEntrypoint_Fragment, EndpointContextData_StandardApplicationEntrypoint_Fragment, EndpointContextData_StandardApplicationPublicAccessEntrypoint_Fragment, EndpointContextData_WebPageEndpoint_Fragment } from './EndpointContextData';
import type { DummyPrivatePageEndpointContextDataFragment } from './DummyPrivatePageEndpointContextData';
import type { StandardApplicationEntrypointContextDataFragment } from './StandardApplicationEntrypointContextData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { AppointmentModuleSpecsFragment } from './AppointmentModuleSpecs';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { NamirialSigningModuleSpecsFragment } from './NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from './NamirialSettingsSpec';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { VisitAppointmentModuleSpecsFragment } from './VisitAppointmentModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from './VisitAppointmentModuleEmailContentsSpecs';
import type { InsurerEntrypointContextDataFragment } from './InsurerEntrypointContextData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { FlexibleDiscountDataFragment } from './FlexibleDiscountData';
import type { ApplicationListEndpointContextDataFragment } from './ApplicationListEndpointContextData';
import type { LeadListEndpointContextDataFragment } from './LeadListEndpointContextData';
import type { EventApplicationEntrypointContextDataFragment } from './EventApplicationEntrypointContextData';
import type { LaunchPadApplicationEntrypointContextDataFragment } from './LaunchPadApplicationEntrypointContextData';
import type { LaunchPadModuleSpecsFragment } from './LaunchPadModuleSpecs';
import type { CounterSettingsSpecsFragment } from './CounterSettingsSpecs';
import type { ConfiguratorApplicationEntrypointContextDataFragment } from './ConfiguratorApplicationEntrypointContextData';
import type { MyInfoSettingSpecFragment } from './MyInfoSettingSpec';
import type { DepositAmountDataFragment } from './DepositAmountData';
import type { CustomerListEndpointContextDataFragment } from './CustomerListEndpointContextData';
import type { MobilityApplicationEntrypointContextDataFragment } from './MobilityApplicationEntrypointContextData';
import type { DealerBookingCodeDataFragment } from './DealerBookingCodeData';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { DateUnitDataFragment } from './DateUnitData';
import type { WebpageEndpointContextDataFragment } from './WebpageEndpointContextData';
import type { WebPageEndpointSpecsFragment } from './WebPageEndpointSpecs';
import type { WebPagePathDataFragment } from './WebPagePathData';
import type { StandardApplicationPublicAccessEntrypointContextDataFragment } from './StandardApplicationPublicAccessEntrypointContextData';
import type { FinderApplicationPublicAccessEntrypointContextDataFragment } from './FinderApplicationPublicAccessEntrypointContextData';
import type { EntrypointFinderApplicationPublicModuleFragment } from './EntrypointFinderApplicationPublicModule';
import type { FinderApplicationModuleEmailContentSpecsFragment } from './FinderApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { ModuleDisclaimersDataFragment } from './ModuleDisclaimersData';
import type { FinderApplicationEntrypointContextDataFragment } from './FinderApplicationEntrypointContextData';
import type { EntrypointFinderApplicationPrivateModuleFragment } from './EntrypointFinderApplicationPrivateModule';
import type { KycFieldSpecsFragment } from './KYCFieldSpecs';
import type { ApplicationAgreementData_CheckboxApplicationAgreement_Fragment, ApplicationAgreementData_MarketingApplicationAgreement_Fragment, ApplicationAgreementData_TextApplicationAgreement_Fragment } from './ApplicationAgreementData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './BaseConditionSpecs';
import type { MarketingPlatformSpecsFragment } from './MarketingPlatformSpecs';
import type { MarketingPlatformsAgreedSpecsFragment } from './MarketingPlatformsAgreedSpecs';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from './ReferenceApplicationData';
import type { LeadData_ConfiguratorLead_Fragment, LeadData_EventLead_Fragment, LeadData_FinderLead_Fragment, LeadData_LaunchpadLead_Fragment, LeadData_MobilityLead_Fragment, LeadData_StandardLead_Fragment } from './LeadData';
import type { StandardLeadDataFragment } from './StandardLeadData';
import type { FinderLeadDataFragment } from './FinderLeadData';
import type { EventLeadDataFragment } from './EventLeadData';
import type { ApplicationEventCustomizedFieldDataFragment } from './ApplicationEventCustomizedFieldData';
import type { LaunchpadLeadDataFragment } from './LaunchpadLeadData';
import type { ConfiguratorLeadDataFragment } from './ConfiguratorLeadData';
import type { ConfiguratorJourneyBlocksData_ApplicationConfiguratorColorSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorOptionSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorPackageSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorTrimSetting_Fragment } from './ConfiguratorJourneyBlocksData';
import type { BlockDetails_ColorBlock_Fragment, BlockDetails_OptionsBlock_Fragment, BlockDetails_PackageBlock_Fragment, BlockDetails_TrimBlock_Fragment } from './BlockDetails';
import type { OptionSettingDetails_ComboOptionSettings_Fragment, OptionSettingDetails_DropdownOptionSettings_Fragment, OptionSettingDetails_MultiSelectOptionSettings_Fragment, OptionSettingDetails_SingleSelectOptionSettings_Fragment } from './OptionSettingDetails';
import type { MobilityLeadDataFragment } from './MobilityLeadData';
import type { DealerVehiclesSpecsFragment } from './DealerVehiclesSpecs';
import type { SalesOfferSpecsFragment } from './SalesOfferSpecs';
import type { VehicleSalesOfferSpecsFragment } from './VehicleSalesOfferSpecs';
import type { PorscheVehicleDataSpecsFragment, PorscheVehicleDataFeatureSpecsFragment, PorscheVehicleImagesSpecsFragment } from './PorscheVehicleDataSpecs';
import type { LocalFittedOptionsSpecsFragment } from './LocalFittedOptionsSpecs';
import type { SalesOfferDocumentDataFragment } from './SalesOfferDocumentData';
import type { MainDetailsSalesOfferSpecsFragment } from './MainDetailsSalesOfferSpecs';
import type { TradeInSalesOfferSpecsFragment } from './TradeInSalesOfferSpecs';
import type { FinanceSalesOfferSpecsFragment } from './FinanceSalesOfferSpecs';
import type { ApplicationFinancingData_DefaultApplicationFinancing_Fragment, ApplicationFinancingData_NewZealandApplicationFinancing_Fragment, ApplicationFinancingData_SingaporeApplicationFinancing_Fragment } from './ApplicationFinancingData';
import type { InsuranceSalesOfferSpecsFragment } from './InsuranceSalesOfferSpecs';
import type { ApplicationInsurancingData_DefaultApplicationInsurancing_Fragment, ApplicationInsurancingData_NewZealandApplicationInsurancing_Fragment, ApplicationInsurancingData_SingaporeApplicationInsurancing_Fragment } from './ApplicationInsurancingData';
import type { DepositSalesOfferSpecsFragment } from './DepositSalesOfferSpecs';
import type { VsaSalesOfferSpecsFragment } from './VSASalesOfferSpecs';
import type { SalesOfferModuleSpecsFragment } from './SalesOfferModuleSpecs';
import type { BankDetailsDataFragment } from './BankDetailsData';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from './BankIntegrationData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from './FinanceProductDetailsData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from './PeriodData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import type { DealerFinanceProductsSpecsFragment } from './DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from './FinanceProductListData';
import type { DealerInsuranceProductsSpecsFragment } from './DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from './InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from './ErgoLookupTableSettingDetails';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from './SalesOfferModuleEmailContentsSpecs';
import type { SalesOfferKycPresetSpecsFragment } from './SalesOfferKYCPresetSpecs';
import type { SalesOfferConsentsSpecsFragment } from './SalesOfferConsentsSpecs';
import type { SalesOfferSigningsSpecsFragment } from './SalesOfferSigningsSpecs';
import type { NamirialSigningDataFragment } from './NamirialSigningData';
import type { FinanceProductDetails_LocalDeferredPrincipal_Fragment, FinanceProductDetails_LocalHirePurchase_Fragment, FinanceProductDetails_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetails_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetails_LocalLease_Fragment, FinanceProductDetails_LocalLeasePurchase_Fragment, FinanceProductDetails_LocalUcclLeasing_Fragment } from './FinanceProductDetails';
import type { LocalHirePurchaseDetailsFragment } from './LocalHirePurchaseDetails';
import type { LocalHirePurchaseWithBalloonDetailsFragment } from './LocalHirePurchaseWithBalloonDetails';
import type { LocalHirePurchaseWithBalloonGfvDetailsFragment } from './LocalHirePurchaseWithBalloonGFVDetails';
import type { LocalLeaseDetailsFragment } from './LocalLeaseDetails';
import type { LocalLeasePurchaseDetailsFragment } from './LocalLeasePurchaseDetails';
import type { LocalDeferredPrincipalDetailsFragment } from './LocalDeferredPrincipalDetails';
import type { LocalUcclLeasingDetailsFragment } from './LocalUcclLeasingDetails';
import type { PromoCodeDataFragment } from './PromoCodeData';
import type { DealerFragmentFragment } from './DealerFragment';
import type { CompanyContextDataFragment } from './CompanyContextData';
import type { LanguagePackContextDataFragment } from './LanguagePackContextData';
import type { AvailableModulesDataFragment } from './AvailableModulesData';
import type { CompanyDealerDataFragment } from './CompanyDealerData';
import type { MaintenanceUpdateFragment } from './MaintenanceUpdate';
import type { UserAvatarSpecsFragment } from './UserAvatarSpecs';
import type { EdmEmailFooterPublicDataFragment } from './EdmEmailFooterPublicData';
import type { EdmSocialMediaDataFragment } from './EdmSocialMediaData';
import type { ModuleInDealerSpecs_AdyenPaymentModule_Fragment, ModuleInDealerSpecs_AppointmentModule_Fragment, ModuleInDealerSpecs_AutoplayModule_Fragment, ModuleInDealerSpecs_BankModule_Fragment, ModuleInDealerSpecs_BasicSigningModule_Fragment, ModuleInDealerSpecs_CapModule_Fragment, ModuleInDealerSpecs_ConfiguratorModule_Fragment, ModuleInDealerSpecs_ConsentsAndDeclarationsModule_Fragment, ModuleInDealerSpecs_CtsModule_Fragment, ModuleInDealerSpecs_DocusignModule_Fragment, ModuleInDealerSpecs_EventApplicationModule_Fragment, ModuleInDealerSpecs_FinderApplicationPrivateModule_Fragment, ModuleInDealerSpecs_FinderApplicationPublicModule_Fragment, ModuleInDealerSpecs_FinderVehicleManagementModule_Fragment, ModuleInDealerSpecs_FiservPaymentModule_Fragment, ModuleInDealerSpecs_GiftVoucherModule_Fragment, ModuleInDealerSpecs_InsuranceModule_Fragment, ModuleInDealerSpecs_LabelsModule_Fragment, ModuleInDealerSpecs_LaunchPadModule_Fragment, ModuleInDealerSpecs_LocalCustomerManagementModule_Fragment, ModuleInDealerSpecs_MaintenanceModule_Fragment, ModuleInDealerSpecs_MarketingModule_Fragment, ModuleInDealerSpecs_MobilityModule_Fragment, ModuleInDealerSpecs_MyInfoModule_Fragment, ModuleInDealerSpecs_NamirialSigningModule_Fragment, ModuleInDealerSpecs_OidcModule_Fragment, ModuleInDealerSpecs_PayGatePaymentModule_Fragment, ModuleInDealerSpecs_PorscheIdModule_Fragment, ModuleInDealerSpecs_PorscheMasterDataModule_Fragment, ModuleInDealerSpecs_PorschePaymentModule_Fragment, ModuleInDealerSpecs_PorscheRetainModule_Fragment, ModuleInDealerSpecs_PromoCodeModule_Fragment, ModuleInDealerSpecs_SalesControlBoardModule_Fragment, ModuleInDealerSpecs_SalesOfferModule_Fragment, ModuleInDealerSpecs_SimpleVehicleManagementModule_Fragment, ModuleInDealerSpecs_StandardApplicationModule_Fragment, ModuleInDealerSpecs_TradeInModule_Fragment, ModuleInDealerSpecs_TtbPaymentModule_Fragment, ModuleInDealerSpecs_UserlikeChatbotModule_Fragment, ModuleInDealerSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleInDealerSpecs_VisitAppointmentModule_Fragment, ModuleInDealerSpecs_WebsiteModule_Fragment, ModuleInDealerSpecs_WhatsappLiveChatModule_Fragment } from './ModuleInDealerSpecs';
import type { StandardApplicationModuleInDealerSpecsFragment } from './StandardApplicationModuleInDealerSpecs';
import type { DealerPriceDisclaimerDataFragment } from './DealerPriceDisclaimerData';
import type { StandardApplicationModuleEmailContentsSpecsFragment, StandardApplicationModuleEmailContentCustomerSpecsFragment, StandardApplicationModuleEmailContentShareSubmissionSpecsFragment, StandardApplicationModuleEmailContentSpecsFragment, StandardApplicationModuleEmailContentSalesPersonSpecsFragment } from './StandardApplicationModuleEmailContentsSpecs';
import type { DealerUploadedFileWithPreviewDataFragment } from './DealerUploadedFileWithPreview';
import type { EventApplicationModuleInDealerSpecsFragment } from './EventApplicationModuleInDealerSpecs';
import type { EventApplicationModuleEmailContentSpecsFragment } from './EventApplicationModuleEmailContentSpecs';
import type { ConfiguratorModuleInDealerSpecsFragment } from './ConfiguratorModuleInDealerSpecs';
import type { ConfiguratorModuleEmailContentSpecsFragment } from './ConfiguratorModuleEmailContentSpecs';
import type { MobilityModuleInDealerSpecsFragment } from './MobilityModuleInDealerSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from './MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from './MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from './MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from './MobilityOperatorEmailContentData';
import type { DealerBookingCodeSpecsFragment } from './DealerBookingCodeSpecs';
import type { MobilityHomeDeliveryDataFragment } from './MobilityHomeDeliveryData';
import type { FinderApplicationPublicModuleInDealerSpecsFragment } from './FinderApplicationPublicModuleInDealerSpecs';
import type { FinderApplicationPrivateModuleInDealerSpecsFragment } from './FinderApplicationPrivateModuleInDealerSpecs';
import type { AppointmentModuleInDealerSpecsFragment } from './AppointmentModuleInDealerSpecs';
import type { VisitAppointmentModuleInDealerSpecsFragment } from './VisitAppointmentModuleInDealerSpecs';
import type { GiftVoucherModuleInDealerSpecsFragment } from './GiftVoucherModuleInDealerSpecs';
import type { GiftVoucherModuleEmailContentsSpecsFragment, GiftVoucherModuleEmailContentCustomerSpecsFragment, GiftVoucherModuleEmailDataFragment } from './GiftVoucherModuleEmailContentsSpecs';
import type { LaunchPadModuleInDealerSpecsFragment } from './LaunchPadModuleInDealerSpecs';
import type { SalesOfferModuleInDealerSpecsFragment } from './SalesOfferModuleInDealerSpecs';
import type { SalesControlBoardModuleInDealerSpecsFragment } from './SalesControlBoardModuleInDealerSpecs';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from './DealerIntData';
import type { GiftPromoTypeDataFragment } from './GiftPromoTypeData';
import type { DiscountPromoTypeDataFragment } from './DiscountPromoTypeData';
import { gql } from '@apollo/client';
import { ApplicationStageDataFragmentDoc } from './ApplicationStageData';
import { CustomerSpecsFragmentDoc } from './CustomerSpecs';
import { LocalCustomerDataFragmentDoc } from './LocalCustomerData';
import { LocalCustomerFieldDataFragmentDoc } from './LocalCustomerFieldData';
import { CorporateCustomerDataFragmentDoc } from './CorporateCustomerData';
import { GuarantorDataFragmentDoc } from './GuarantorData';
import { DraftFlowConfigurationSpecFragmentDoc } from './DraftFlowConfigurationSpec';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { LaunchpadModuleSpecsForApplicationFragmentDoc } from './LaunchpadModuleSpecsForApplication';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { LaunchpadApplicationConfigurationDataFragmentDoc } from './LaunchpadApplicationConfigurationData';
import { TradeInVehicleDataFragmentDoc } from './TradeInVehicleData';
import { ApplicationDocumentDataFragmentDoc } from './ApplicationDocumentData';
import { UsersOptionsDataFragmentDoc } from './UsersOptionsData';
import { DealerApplicationFragmentFragmentDoc } from './DealerApplicationFragment';
import { DealerContactFragmentFragmentDoc } from './DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from './DealerSocialMediaFragment';
import { DealerDisclaimersFragmentFragmentDoc } from './DealerDisclaimersFragment';
import { DealerIntegrationDetailsFragmentFragmentDoc } from './DealerIntegrationDetailsFragment';
import { EndpointContextDataFragmentDoc } from './EndpointContextData';
import { DummyPrivatePageEndpointContextDataFragmentDoc } from './DummyPrivatePageEndpointContextData';
import { StandardApplicationEntrypointContextDataFragmentDoc } from './StandardApplicationEntrypointContextData';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { AppointmentModuleSpecsFragmentDoc } from './AppointmentModuleSpecs';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { NamirialSigningModuleSpecsFragmentDoc } from './NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from './NamirialSettingsSpec';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { VisitAppointmentModuleSpecsFragmentDoc } from './VisitAppointmentModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from './VisitAppointmentModuleEmailContentsSpecs';
import { InsurerEntrypointContextDataFragmentDoc } from './InsurerEntrypointContextData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { FlexibleDiscountDataFragmentDoc } from './FlexibleDiscountData';
import { ApplicationListEndpointContextDataFragmentDoc } from './ApplicationListEndpointContextData';
import { LeadListEndpointContextDataFragmentDoc } from './LeadListEndpointContextData';
import { EventApplicationEntrypointContextDataFragmentDoc } from './EventApplicationEntrypointContextData';
import { LaunchPadApplicationEntrypointContextDataFragmentDoc } from './LaunchPadApplicationEntrypointContextData';
import { LaunchPadModuleSpecsFragmentDoc } from './LaunchPadModuleSpecs';
import { CounterSettingsSpecsFragmentDoc } from './CounterSettingsSpecs';
import { ConfiguratorApplicationEntrypointContextDataFragmentDoc } from './ConfiguratorApplicationEntrypointContextData';
import { MyInfoSettingSpecFragmentDoc } from './MyInfoSettingSpec';
import { DepositAmountDataFragmentDoc } from './DepositAmountData';
import { CustomerListEndpointContextDataFragmentDoc } from './CustomerListEndpointContextData';
import { MobilityApplicationEntrypointContextDataFragmentDoc } from './MobilityApplicationEntrypointContextData';
import { DealerBookingCodeDataFragmentDoc } from './DealerBookingCodeData';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { DateUnitDataFragmentDoc } from './DateUnitData';
import { WebpageEndpointContextDataFragmentDoc } from './WebpageEndpointContextData';
import { WebPageEndpointSpecsFragmentDoc } from './WebPageEndpointSpecs';
import { WebPagePathDataFragmentDoc } from './WebPagePathData';
import { StandardApplicationPublicAccessEntrypointContextDataFragmentDoc } from './StandardApplicationPublicAccessEntrypointContextData';
import { FinderApplicationPublicAccessEntrypointContextDataFragmentDoc } from './FinderApplicationPublicAccessEntrypointContextData';
import { EntrypointFinderApplicationPublicModuleFragmentDoc } from './EntrypointFinderApplicationPublicModule';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from './FinderApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { ModuleDisclaimersDataFragmentDoc } from './ModuleDisclaimersData';
import { FinderApplicationEntrypointContextDataFragmentDoc } from './FinderApplicationEntrypointContextData';
import { EntrypointFinderApplicationPrivateModuleFragmentDoc } from './EntrypointFinderApplicationPrivateModule';
import { KycFieldSpecsFragmentDoc } from './KYCFieldSpecs';
import { ApplicationAgreementDataFragmentDoc } from './ApplicationAgreementData';
import { ConditionSpecsFragmentDoc } from './ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from './BaseConditionSpecs';
import { MarketingPlatformSpecsFragmentDoc } from './MarketingPlatformSpecs';
import { MarketingPlatformsAgreedSpecsFragmentDoc } from './MarketingPlatformsAgreedSpecs';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from './ReferenceApplicationData';
import { LeadDataFragmentDoc } from './LeadData';
import { StandardLeadDataFragmentDoc } from './StandardLeadData';
import { FinderLeadDataFragmentDoc } from './FinderLeadData';
import { EventLeadDataFragmentDoc } from './EventLeadData';
import { ApplicationEventCustomizedFieldDataFragmentDoc } from './ApplicationEventCustomizedFieldData';
import { LaunchpadLeadDataFragmentDoc } from './LaunchpadLeadData';
import { ConfiguratorLeadDataFragmentDoc } from './ConfiguratorLeadData';
import { ConfiguratorJourneyBlocksDataFragmentDoc } from './ConfiguratorJourneyBlocksData';
import { BlockDetailsFragmentDoc } from './BlockDetails';
import { OptionSettingDetailsFragmentDoc } from './OptionSettingDetails';
import { MobilityLeadDataFragmentDoc } from './MobilityLeadData';
import { DealerVehiclesSpecsFragmentDoc } from './DealerVehiclesSpecs';
import { SalesOfferSpecsFragmentDoc } from './SalesOfferSpecs';
import { VehicleSalesOfferSpecsFragmentDoc } from './VehicleSalesOfferSpecs';
import { PorscheVehicleDataSpecsFragmentDoc, PorscheVehicleDataFeatureSpecsFragmentDoc, PorscheVehicleImagesSpecsFragmentDoc } from './PorscheVehicleDataSpecs';
import { LocalFittedOptionsSpecsFragmentDoc } from './LocalFittedOptionsSpecs';
import { SalesOfferDocumentDataFragmentDoc } from './SalesOfferDocumentData';
import { MainDetailsSalesOfferSpecsFragmentDoc } from './MainDetailsSalesOfferSpecs';
import { TradeInSalesOfferSpecsFragmentDoc } from './TradeInSalesOfferSpecs';
import { FinanceSalesOfferSpecsFragmentDoc } from './FinanceSalesOfferSpecs';
import { ApplicationFinancingDataFragmentDoc } from './ApplicationFinancingData';
import { InsuranceSalesOfferSpecsFragmentDoc } from './InsuranceSalesOfferSpecs';
import { ApplicationInsurancingDataFragmentDoc } from './ApplicationInsurancingData';
import { DepositSalesOfferSpecsFragmentDoc } from './DepositSalesOfferSpecs';
import { VsaSalesOfferSpecsFragmentDoc } from './VSASalesOfferSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from './SalesOfferModuleSpecs';
import { BankDetailsDataFragmentDoc } from './BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from './BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from './FinanceProductDetailsData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
import { DealerFinanceProductsSpecsFragmentDoc } from './DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from './FinanceProductListData';
import { DealerInsuranceProductsSpecsFragmentDoc } from './DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from './InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from './ErgoLookupTableSettingDetails';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from './SalesOfferModuleEmailContentsSpecs';
import { SalesOfferKycPresetSpecsFragmentDoc } from './SalesOfferKYCPresetSpecs';
import { SalesOfferConsentsSpecsFragmentDoc } from './SalesOfferConsentsSpecs';
import { SalesOfferSigningsSpecsFragmentDoc } from './SalesOfferSigningsSpecs';
import { NamirialSigningDataFragmentDoc } from './NamirialSigningData';
import { FinanceProductDetailsFragmentDoc } from './FinanceProductDetails';
import { LocalHirePurchaseDetailsFragmentDoc } from './LocalHirePurchaseDetails';
import { LocalHirePurchaseWithBalloonDetailsFragmentDoc } from './LocalHirePurchaseWithBalloonDetails';
import { LocalHirePurchaseWithBalloonGfvDetailsFragmentDoc } from './LocalHirePurchaseWithBalloonGFVDetails';
import { LocalLeaseDetailsFragmentDoc } from './LocalLeaseDetails';
import { LocalLeasePurchaseDetailsFragmentDoc } from './LocalLeasePurchaseDetails';
import { LocalDeferredPrincipalDetailsFragmentDoc } from './LocalDeferredPrincipalDetails';
import { LocalUcclLeasingDetailsFragmentDoc } from './LocalUcclLeasingDetails';
import { PromoCodeDataFragmentDoc } from './PromoCodeData';
import { DealerFragmentFragmentDoc } from './DealerFragment';
import { CompanyContextDataFragmentDoc } from './CompanyContextData';
import { LanguagePackContextDataFragmentDoc } from './LanguagePackContextData';
import { AvailableModulesDataFragmentDoc } from './AvailableModulesData';
import { CompanyDealerDataFragmentDoc } from './CompanyDealerData';
import { MaintenanceUpdateFragmentDoc } from './MaintenanceUpdate';
import { UserAvatarSpecsFragmentDoc } from './UserAvatarSpecs';
import { EdmEmailFooterPublicDataFragmentDoc } from './EdmEmailFooterPublicData';
import { EdmSocialMediaDataFragmentDoc } from './EdmSocialMediaData';
import { ModuleInDealerSpecsFragmentDoc } from './ModuleInDealerSpecs';
import { StandardApplicationModuleInDealerSpecsFragmentDoc } from './StandardApplicationModuleInDealerSpecs';
import { DealerPriceDisclaimerDataFragmentDoc } from './DealerPriceDisclaimerData';
import { StandardApplicationModuleEmailContentsSpecsFragmentDoc, StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc, StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc, StandardApplicationModuleEmailContentSpecsFragmentDoc, StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc } from './StandardApplicationModuleEmailContentsSpecs';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from './DealerUploadedFileWithPreview';
import { EventApplicationModuleInDealerSpecsFragmentDoc } from './EventApplicationModuleInDealerSpecs';
import { EventApplicationModuleEmailContentSpecsFragmentDoc } from './EventApplicationModuleEmailContentSpecs';
import { ConfiguratorModuleInDealerSpecsFragmentDoc } from './ConfiguratorModuleInDealerSpecs';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from './ConfiguratorModuleEmailContentSpecs';
import { MobilityModuleInDealerSpecsFragmentDoc } from './MobilityModuleInDealerSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from './MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from './MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from './MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from './MobilityOperatorEmailContentData';
import { DealerBookingCodeSpecsFragmentDoc } from './DealerBookingCodeSpecs';
import { MobilityHomeDeliveryDataFragmentDoc } from './MobilityHomeDeliveryData';
import { FinderApplicationPublicModuleInDealerSpecsFragmentDoc } from './FinderApplicationPublicModuleInDealerSpecs';
import { FinderApplicationPrivateModuleInDealerSpecsFragmentDoc } from './FinderApplicationPrivateModuleInDealerSpecs';
import { AppointmentModuleInDealerSpecsFragmentDoc } from './AppointmentModuleInDealerSpecs';
import { VisitAppointmentModuleInDealerSpecsFragmentDoc } from './VisitAppointmentModuleInDealerSpecs';
import { GiftVoucherModuleInDealerSpecsFragmentDoc } from './GiftVoucherModuleInDealerSpecs';
import { GiftVoucherModuleEmailContentsSpecsFragmentDoc, GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc, GiftVoucherModuleEmailDataFragmentDoc } from './GiftVoucherModuleEmailContentsSpecs';
import { LaunchPadModuleInDealerSpecsFragmentDoc } from './LaunchPadModuleInDealerSpecs';
import { SalesOfferModuleInDealerSpecsFragmentDoc } from './SalesOfferModuleInDealerSpecs';
import { SalesControlBoardModuleInDealerSpecsFragmentDoc } from './SalesControlBoardModuleInDealerSpecs';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from './DealerIntData';
import { GiftPromoTypeDataFragmentDoc } from './GiftPromoTypeData';
import { DiscountPromoTypeDataFragmentDoc } from './DiscountPromoTypeData';
export type LaunchpadApplicationDataFragment = (
  { __typename: 'LaunchpadApplication' }
  & Pick<SchemaTypes.LaunchpadApplication, 'vehicleId' | 'routerId' | 'bankId'>
  & { applicant: (
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  ), guarantor?: SchemaTypes.Maybe<(
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  )>, draftFlow: (
    { __typename: 'StandardApplicationDraftFlow' }
    & DraftFlowConfigurationSpecFragment
  ), vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & VehicleSpecs_FinderVehicle_Fragment
  ) | (
    { __typename: 'LocalMake' }
    & VehicleSpecs_LocalMake_Fragment
  ) | (
    { __typename: 'LocalModel' }
    & VehicleSpecs_LocalModel_Fragment
  ) | (
    { __typename: 'LocalVariant' }
    & VehicleSpecs_LocalVariant_Fragment
  )>, otherVehicleInformation?: SchemaTypes.Maybe<(
    { __typename: 'OtherVehicleInformation' }
    & Pick<SchemaTypes.OtherVehicleInformation, 'chassisNo' | 'engineNo'>
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
    & LaunchpadModuleSpecsForApplicationFragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'timeZone'>
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ), configuration: (
    { __typename: 'LaunchpadApplicationConfiguration' }
    & LaunchpadApplicationConfigurationDataFragment
  ), tradeInVehicle: Array<(
    { __typename: 'TradeInVehicle' }
    & TradeInVehicleDataFragment
  )>, documents: Array<(
    { __typename: 'ApplicationDocument' }
    & ApplicationDocumentDataFragment
  )>, assignee?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )>, availableAssignees: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )>, dealer: (
    { __typename: 'Dealer' }
    & DealerApplicationFragmentFragment
  ), router?: SchemaTypes.Maybe<(
    { __typename: 'Router' }
    & Pick<SchemaTypes.Router, 'id' | 'pathname'>
  )>, endpoint?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationListEndpoint' }
    & EndpointContextData_ApplicationListEndpoint_Fragment
  ) | (
    { __typename: 'ConfiguratorApplicationEntrypoint' }
    & EndpointContextData_ConfiguratorApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'CustomerListEndpoint' }
    & EndpointContextData_CustomerListEndpoint_Fragment
  ) | (
    { __typename: 'DummyPrivatePageEndpoint' }
    & EndpointContextData_DummyPrivatePageEndpoint_Fragment
  ) | (
    { __typename: 'DummyWelcomePageEndpoint' }
    & EndpointContextData_DummyWelcomePageEndpoint_Fragment
  ) | (
    { __typename: 'EventApplicationEntrypoint' }
    & EndpointContextData_EventApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'FinderApplicationEntrypoint' }
    & EndpointContextData_FinderApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicAccessEntrypoint' }
    & EndpointContextData_FinderApplicationPublicAccessEntrypoint_Fragment
  ) | (
    { __typename: 'LaunchPadApplicationEntrypoint' }
    & EndpointContextData_LaunchPadApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'LeadListEndpoint' }
    & EndpointContextData_LeadListEndpoint_Fragment
  ) | (
    { __typename: 'MobilityApplicationEntrypoint' }
    & EndpointContextData_MobilityApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'StandardApplicationEntrypoint' }
    & EndpointContextData_StandardApplicationEntrypoint_Fragment
  ) | (
    { __typename: 'StandardApplicationPublicAccessEntrypoint' }
    & EndpointContextData_StandardApplicationPublicAccessEntrypoint_Fragment
  ) | (
    { __typename: 'WebPageEndpoint' }
    & EndpointContextData_WebPageEndpoint_Fragment
  )>, applicantKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, applicantAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, corporateKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, referenceApplications: Array<(
    { __typename: 'ConfiguratorApplication' }
    & ReferenceApplicationData_ConfiguratorApplication_Fragment
  ) | (
    { __typename: 'EventApplication' }
    & ReferenceApplicationData_EventApplication_Fragment
  ) | (
    { __typename: 'FinderApplication' }
    & ReferenceApplicationData_FinderApplication_Fragment
  ) | (
    { __typename: 'LaunchpadApplication' }
    & ReferenceApplicationData_LaunchpadApplication_Fragment
  ) | (
    { __typename: 'MobilityApplication' }
    & ReferenceApplicationData_MobilityApplication_Fragment
  ) | (
    { __typename: 'SalesOfferApplication' }
    & ReferenceApplicationData_SalesOfferApplication_Fragment
  ) | (
    { __typename: 'StandardApplication' }
    & ReferenceApplicationData_StandardApplication_Fragment
  )>, testDriveAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, showroomVisitAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, lead: (
    { __typename: 'ConfiguratorLead' }
    & LeadData_ConfiguratorLead_Fragment
  ) | (
    { __typename: 'EventLead' }
    & LeadData_EventLead_Fragment
  ) | (
    { __typename: 'FinderLead' }
    & LeadData_FinderLead_Fragment
  ) | (
    { __typename: 'LaunchpadLead' }
    & LeadData_LaunchpadLead_Fragment
  ) | (
    { __typename: 'MobilityLead' }
    & LeadData_MobilityLead_Fragment
  ) | (
    { __typename: 'StandardLead' }
    & LeadData_StandardLead_Fragment
  ), bank?: SchemaTypes.Maybe<(
    { __typename: 'SystemBank' }
    & BankDetailsDataFragment
  )>, financing?: SchemaTypes.Maybe<(
    { __typename: 'DefaultApplicationFinancing' }
    & ApplicationFinancingData_DefaultApplicationFinancing_Fragment
  ) | (
    { __typename: 'NewZealandApplicationFinancing' }
    & ApplicationFinancingData_NewZealandApplicationFinancing_Fragment
  ) | (
    { __typename: 'SingaporeApplicationFinancing' }
    & ApplicationFinancingData_SingaporeApplicationFinancing_Fragment
  )>, financeProduct?: SchemaTypes.Maybe<(
    { __typename: 'LocalDeferredPrincipal' }
    & FinanceProductDetails_LocalDeferredPrincipal_Fragment
  ) | (
    { __typename: 'LocalHirePurchase' }
    & FinanceProductDetails_LocalHirePurchase_Fragment
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloon' }
    & FinanceProductDetails_LocalHirePurchaseWithBalloon_Fragment
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloonGFV' }
    & FinanceProductDetails_LocalHirePurchaseWithBalloonGfv_Fragment
  ) | (
    { __typename: 'LocalLease' }
    & FinanceProductDetails_LocalLease_Fragment
  ) | (
    { __typename: 'LocalLeasePurchase' }
    & FinanceProductDetails_LocalLeasePurchase_Fragment
  ) | (
    { __typename: 'LocalUcclLeasing' }
    & FinanceProductDetails_LocalUcclLeasing_Fragment
  )>, guarantorKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, guarantorAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, promoCode?: SchemaTypes.Maybe<(
    { __typename: 'PromoCode' }
    & PromoCodeDataFragment
  )> }
  & ApplicationStageData_LaunchpadApplication_Fragment
);

export const LaunchpadApplicationDataFragmentDoc = /*#__PURE__*/ gql`
    fragment LaunchpadApplicationData on LaunchpadApplication {
  ...ApplicationStageData
  applicant {
    ...CustomerSpecs
  }
  guarantor {
    ...CustomerSpecs
  }
  draftFlow {
    ...DraftFlowConfigurationSpec
  }
  vehicleId
  vehicle {
    ...VehicleSpecs
  }
  otherVehicleInformation {
    chassisNo
    engineNo
  }
  module {
    ...LaunchpadModuleSpecsForApplication
    company {
      timeZone
    }
  }
  versioning {
    ...AdvancedVersioningData
  }
  configuration {
    ...LaunchpadApplicationConfigurationData
  }
  tradeInVehicle {
    ...TradeInVehicleData
  }
  documents {
    ...ApplicationDocumentData
  }
  assignee {
    ...UsersOptionsData
  }
  availableAssignees {
    ...UsersOptionsData
  }
  dealer {
    ...DealerApplicationFragment
  }
  routerId
  router {
    id
    pathname
  }
  endpoint {
    ...EndpointContextData
  }
  applicantKYC {
    ...KYCFieldSpecs
  }
  applicantAgreements {
    ...ApplicationAgreementData
  }
  corporateKYC {
    ...KYCFieldSpecs
  }
  referenceApplications {
    ...ReferenceApplicationData
  }
  testDriveAgreements {
    ...ApplicationAgreementData
  }
  showroomVisitAgreements {
    ...ApplicationAgreementData
  }
  lead {
    ...LeadData
  }
  bankId
  bank {
    ...BankDetailsData
  }
  financing {
    ...ApplicationFinancingData
  }
  financeProduct {
    ...FinanceProductDetails
  }
  guarantorKYC {
    ...KYCFieldSpecs
  }
  guarantorAgreements {
    ...ApplicationAgreementData
  }
  promoCode {
    ...PromoCodeData
  }
}
    `;