import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from './PeriodData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
export type FinanceProductDetailsData_LocalDeferredPrincipal_Fragment = (
  { __typename: 'LocalDeferredPrincipal' }
  & Pick<SchemaTypes.LocalDeferredPrincipal, 'id' | 'identifier' | 'type' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'isDeleted' | 'displayName' | 'bankId' | 'webCalcSettingId' | 'variantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), variants: Array<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'DeferredPrincipalTermSettings' }
    & TermSettingsDetails_DeferredPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
);

export type FinanceProductDetailsData_LocalHirePurchase_Fragment = (
  { __typename: 'LocalHirePurchase' }
  & Pick<SchemaTypes.LocalHirePurchase, 'id' | 'identifier' | 'type' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'isDeleted' | 'displayName' | 'bankId' | 'webCalcSettingId' | 'variantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), variants: Array<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
);

export type FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment = (
  { __typename: 'LocalHirePurchaseWithBalloon' }
  & Pick<SchemaTypes.LocalHirePurchaseWithBalloon, 'id' | 'identifier' | 'type' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'isDeleted' | 'displayName' | 'bankId' | 'webCalcSettingId' | 'variantSuiteIds'>
  & { balloon: (
    { __typename: 'BalloonRangeSettings' }
    & BalloonSettingsDetails_BalloonRangeSettings_Fragment
  ) | (
    { __typename: 'BalloonTableSettings' }
    & BalloonSettingsDetails_BalloonTableSettings_Fragment
  ), legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), variants: Array<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
);

export type FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment = (
  { __typename: 'LocalHirePurchaseWithBalloonGFV' }
  & Pick<SchemaTypes.LocalHirePurchaseWithBalloonGfv, 'id' | 'identifier' | 'type' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'isDeleted' | 'displayName' | 'bankId' | 'webCalcSettingId' | 'variantSuiteIds'>
  & { balloon: (
    { __typename: 'BalloonGFVSettings' }
    & BalloonGfvSettingsDetailsFragment
  ), legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), variants: Array<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
);

export type FinanceProductDetailsData_LocalLease_Fragment = (
  { __typename: 'LocalLease' }
  & Pick<SchemaTypes.LocalLease, 'id' | 'identifier' | 'type' | 'deriveMethod' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'isDeleted' | 'displayName' | 'bankId' | 'webCalcSettingId' | 'variantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), variants: Array<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, lease: (
    { __typename: 'LeaseSettings' }
    & LeaseSettingsDetailsFragment
  ), term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), deposit: (
    { __typename: 'DepositRangeSettings' }
    & DepositSettingsDetails_DepositRangeSettings_Fragment
  ) | (
    { __typename: 'DepositTableSettings' }
    & DepositSettingsDetails_DepositTableSettings_Fragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
);

export type FinanceProductDetailsData_LocalLeasePurchase_Fragment = (
  { __typename: 'LocalLeasePurchase' }
  & Pick<SchemaTypes.LocalLeasePurchase, 'id' | 'identifier' | 'type' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'isDeleted' | 'displayName' | 'bankId' | 'webCalcSettingId' | 'variantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), variants: Array<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ), residualValue: (
    { __typename: 'ResidualValueSettings' }
    & ResidualValueSettingsDetailsFragment
  ) }
);

export type FinanceProductDetailsData_LocalUcclLeasing_Fragment = (
  { __typename: 'LocalUcclLeasing' }
  & Pick<SchemaTypes.LocalUcclLeasing, 'id' | 'identifier' | 'type' | 'basedOn' | 'deriveMethod' | 'calculationMode' | 'bankReferenceIdentifier' | 'order' | 'isActive' | 'isDeleted' | 'displayName' | 'bankId' | 'webCalcSettingId' | 'variantSuiteIds'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModulesCompanyTimezoneData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModulesCompanyTimezoneData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModulesCompanyTimezoneData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModulesCompanyTimezoneData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModulesCompanyTimezoneData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModulesCompanyTimezoneData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModulesCompanyTimezoneData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModulesCompanyTimezoneData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModulesCompanyTimezoneData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModulesCompanyTimezoneData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModulesCompanyTimezoneData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModulesCompanyTimezoneData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModulesCompanyTimezoneData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModulesCompanyTimezoneData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModulesCompanyTimezoneData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModulesCompanyTimezoneData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModulesCompanyTimezoneData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModulesCompanyTimezoneData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModulesCompanyTimezoneData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModulesCompanyTimezoneData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModulesCompanyTimezoneData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModulesCompanyTimezoneData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModulesCompanyTimezoneData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModulesCompanyTimezoneData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModulesCompanyTimezoneData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModulesCompanyTimezoneData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModulesCompanyTimezoneData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModulesCompanyTimezoneData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModulesCompanyTimezoneData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment
  ), period: (
    { __typename: 'Period' }
    & PeriodDataFragment
  ), vehicleReferenceParameters: (
    { __typename: 'VehicleReferenceParameters' }
    & VehicleReferenceParametersDataFragment
  ), variants: Array<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'id'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, payment: (
    { __typename: 'PaymentSettings' }
    & PaymentSettingsDetailsFragment
  ), loan?: SchemaTypes.Maybe<(
    { __typename: 'LoanSettings' }
    & LoanSettingsDetailsFragment
  )>, term: (
    { __typename: 'GenericPrincipalTermSettings' }
    & TermSettingsDetails_GenericPrincipalTermSettings_Fragment
  ), interestRate: (
    { __typename: 'InterestRateFixedSettings' }
    & InterestRateSettingsDetails_InterestRateFixedSettings_Fragment
  ) | (
    { __typename: 'InterestRateRangeSettings' }
    & InterestRateSettingsDetails_InterestRateRangeSettings_Fragment
  ) | (
    { __typename: 'InterestRateTableSettings' }
    & InterestRateSettingsDetails_InterestRateTableSettings_Fragment
  ), downPayment: (
    { __typename: 'DownPaymentRangeSettings' }
    & DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment
  ) | (
    { __typename: 'DownPaymentTableSettings' }
    & DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ) }
  & LocalUcclLeasingOnlyDetailsFragment
);

export type FinanceProductDetailsDataFragment = FinanceProductDetailsData_LocalDeferredPrincipal_Fragment | FinanceProductDetailsData_LocalHirePurchase_Fragment | FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment | FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment | FinanceProductDetailsData_LocalLease_Fragment | FinanceProductDetailsData_LocalLeasePurchase_Fragment | FinanceProductDetailsData_LocalUcclLeasing_Fragment;

export const FinanceProductDetailsDataFragmentDoc = /*#__PURE__*/ gql`
    fragment FinanceProductDetailsData on FinanceProduct {
  ... on LocalHirePurchase {
    id
    identifier
    type
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    order
    isActive
    isDeleted
    displayName
    legalName {
      ...TranslatedStringData
    }
    description {
      ...TranslatedStringData
    }
    bankId
    webCalcSettingId
    module {
      ...ModulesCompanyTimezoneData
    }
    period {
      ...PeriodData
    }
    variantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    variants {
      id
      name {
        ...TranslatedStringData
      }
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    versioning {
      ...AdvancedVersioningData
    }
  }
  ... on LocalHirePurchaseWithBalloon {
    id
    identifier
    type
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    balloon {
      ...BalloonSettingsDetails
    }
    order
    isActive
    isDeleted
    displayName
    legalName {
      ...TranslatedStringData
    }
    description {
      ...TranslatedStringData
    }
    bankId
    webCalcSettingId
    module {
      ...ModulesCompanyTimezoneData
    }
    period {
      ...PeriodData
    }
    variantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    variants {
      id
      name {
        ...TranslatedStringData
      }
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    versioning {
      ...AdvancedVersioningData
    }
  }
  ... on LocalHirePurchaseWithBalloonGFV {
    id
    identifier
    type
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    balloon {
      ...BalloonGFVSettingsDetails
    }
    order
    isActive
    isDeleted
    displayName
    legalName {
      ...TranslatedStringData
    }
    description {
      ...TranslatedStringData
    }
    bankId
    webCalcSettingId
    module {
      ...ModulesCompanyTimezoneData
    }
    period {
      ...PeriodData
    }
    variantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    variants {
      id
      name {
        ...TranslatedStringData
      }
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    versioning {
      ...AdvancedVersioningData
    }
  }
  ... on LocalLease {
    id
    identifier
    type
    deriveMethod
    bankReferenceIdentifier
    order
    isActive
    isDeleted
    displayName
    legalName {
      ...TranslatedStringData
    }
    description {
      ...TranslatedStringData
    }
    bankId
    webCalcSettingId
    module {
      ...ModulesCompanyTimezoneData
    }
    period {
      ...PeriodData
    }
    variantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    variants {
      id
      name {
        ...TranslatedStringData
      }
    }
    lease {
      ...LeaseSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    deposit {
      ...DepositSettingsDetails
    }
    versioning {
      ...AdvancedVersioningData
    }
  }
  ... on LocalLeasePurchase {
    id
    identifier
    type
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    order
    isActive
    isDeleted
    displayName
    legalName {
      ...TranslatedStringData
    }
    description {
      ...TranslatedStringData
    }
    bankId
    webCalcSettingId
    module {
      ...ModulesCompanyTimezoneData
    }
    period {
      ...PeriodData
    }
    variantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    variants {
      id
      name {
        ...TranslatedStringData
      }
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    versioning {
      ...AdvancedVersioningData
    }
    residualValue {
      ...ResidualValueSettingsDetails
    }
  }
  ... on LocalDeferredPrincipal {
    id
    identifier
    type
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    order
    isActive
    isDeleted
    displayName
    legalName {
      ...TranslatedStringData
    }
    description {
      ...TranslatedStringData
    }
    bankId
    webCalcSettingId
    module {
      ...ModulesCompanyTimezoneData
    }
    period {
      ...PeriodData
    }
    variantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    variants {
      id
      name {
        ...TranslatedStringData
      }
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    versioning {
      ...AdvancedVersioningData
    }
  }
  ... on LocalUcclLeasing {
    id
    identifier
    type
    basedOn
    deriveMethod
    calculationMode
    bankReferenceIdentifier
    order
    isActive
    isDeleted
    displayName
    legalName {
      ...TranslatedStringData
    }
    description {
      ...TranslatedStringData
    }
    bankId
    webCalcSettingId
    module {
      ...ModulesCompanyTimezoneData
    }
    period {
      ...PeriodData
    }
    variantSuiteIds
    vehicleReferenceParameters {
      ...VehicleReferenceParametersData
    }
    variants {
      id
      name {
        ...TranslatedStringData
      }
    }
    payment {
      ...PaymentSettingsDetails
    }
    loan {
      ...LoanSettingsDetails
    }
    term {
      ...TermSettingsDetails
    }
    interestRate {
      ...InterestRateSettingsDetails
    }
    downPayment {
      ...DownPaymentSettingsDetails
    }
    versioning {
      ...AdvancedVersioningData
    }
    ...LocalUcclLeasingOnlyDetails
  }
}
    `;