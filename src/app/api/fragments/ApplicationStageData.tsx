import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type ApplicationStageData_ConfiguratorApplication_Fragment = (
  { __typename: 'ConfiguratorApplication' }
  & Pick<SchemaTypes.ConfiguratorApplication, 'stages'>
  & { mobilityStage?: SchemaTypes.Maybe<(
    { __typename: 'MobilityStage' }
    & Pick<SchemaTypes.MobilityStage, 'identifier' | 'status' | 'assigneeId'>
  )>, reservationStage?: SchemaTypes.Maybe<(
    { __typename: 'ReservationStage' }
    & Pick<SchemaTypes.ReservationStage, 'identifier' | 'status' | 'assigneeId'>
  )>, financingStage?: SchemaTypes.Maybe<(
    { __typename: 'FinancingStage' }
    & Pick<SchemaTypes.FinancingStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, visitAppointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'VisitAppointmentStage' }
    & Pick<SchemaTypes.VisitAppointmentStage, 'identifier' | 'status' | 'assigneeId'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), visitAppointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ) }
  )>, appointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'AppointmentStage' }
    & Pick<SchemaTypes.AppointmentStage, 'identifier' | 'status' | 'assigneeId' | 'checkInTime' | 'checkOutTime' | 'registrationNumber' | 'hasChangedVehicle'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), appointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'hasTestDriveProcess' | 'hasTestDriveSigning' | 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ), mileage?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentMileage' }
      & Pick<SchemaTypes.AppointmentMileage, 'end' | 'start'>
    )>, changedVehicle?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentChangedVehicleLocal' }
      & Pick<SchemaTypes.AppointmentChangedVehicleLocal, 'vehicleId'>
    ) | (
      { __typename: 'AppointmentChangedVehicleText' }
      & Pick<SchemaTypes.AppointmentChangedVehicleText, 'model' | 'subModel' | 'variant'>
    )> }
  )>, insuranceStage?: SchemaTypes.Maybe<(
    { __typename: 'InsuranceStage' }
    & Pick<SchemaTypes.InsuranceStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, tradeInStage?: SchemaTypes.Maybe<(
    { __typename: 'TradeInStage' }
    & Pick<SchemaTypes.TradeInStage, 'identifier' | 'status' | 'assigneeId' | 'isQuoted' | 'lastQuotedAt'>
  )>, followUpStage?: SchemaTypes.Maybe<(
    { __typename: 'FollowUpStage' }
    & Pick<SchemaTypes.FollowUpStage, 'identifier' | 'status' | 'assigneeId' | 'remarks' | 'scheduledDate'>
  )> }
);

export type ApplicationStageData_EventApplication_Fragment = (
  { __typename: 'EventApplication' }
  & Pick<SchemaTypes.EventApplication, 'stages'>
  & { mobilityStage?: SchemaTypes.Maybe<(
    { __typename: 'MobilityStage' }
    & Pick<SchemaTypes.MobilityStage, 'identifier' | 'status' | 'assigneeId'>
  )>, reservationStage?: SchemaTypes.Maybe<(
    { __typename: 'ReservationStage' }
    & Pick<SchemaTypes.ReservationStage, 'identifier' | 'status' | 'assigneeId'>
  )>, financingStage?: SchemaTypes.Maybe<(
    { __typename: 'FinancingStage' }
    & Pick<SchemaTypes.FinancingStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, visitAppointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'VisitAppointmentStage' }
    & Pick<SchemaTypes.VisitAppointmentStage, 'identifier' | 'status' | 'assigneeId'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), visitAppointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ) }
  )>, appointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'AppointmentStage' }
    & Pick<SchemaTypes.AppointmentStage, 'identifier' | 'status' | 'assigneeId' | 'checkInTime' | 'checkOutTime' | 'registrationNumber' | 'hasChangedVehicle'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), appointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'hasTestDriveProcess' | 'hasTestDriveSigning' | 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ), mileage?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentMileage' }
      & Pick<SchemaTypes.AppointmentMileage, 'end' | 'start'>
    )>, changedVehicle?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentChangedVehicleLocal' }
      & Pick<SchemaTypes.AppointmentChangedVehicleLocal, 'vehicleId'>
    ) | (
      { __typename: 'AppointmentChangedVehicleText' }
      & Pick<SchemaTypes.AppointmentChangedVehicleText, 'model' | 'subModel' | 'variant'>
    )> }
  )>, insuranceStage?: SchemaTypes.Maybe<(
    { __typename: 'InsuranceStage' }
    & Pick<SchemaTypes.InsuranceStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, tradeInStage?: SchemaTypes.Maybe<(
    { __typename: 'TradeInStage' }
    & Pick<SchemaTypes.TradeInStage, 'identifier' | 'status' | 'assigneeId' | 'isQuoted' | 'lastQuotedAt'>
  )>, followUpStage?: SchemaTypes.Maybe<(
    { __typename: 'FollowUpStage' }
    & Pick<SchemaTypes.FollowUpStage, 'identifier' | 'status' | 'assigneeId' | 'remarks' | 'scheduledDate'>
  )> }
);

export type ApplicationStageData_FinderApplication_Fragment = (
  { __typename: 'FinderApplication' }
  & Pick<SchemaTypes.FinderApplication, 'stages'>
  & { mobilityStage?: SchemaTypes.Maybe<(
    { __typename: 'MobilityStage' }
    & Pick<SchemaTypes.MobilityStage, 'identifier' | 'status' | 'assigneeId'>
  )>, reservationStage?: SchemaTypes.Maybe<(
    { __typename: 'ReservationStage' }
    & Pick<SchemaTypes.ReservationStage, 'identifier' | 'status' | 'assigneeId'>
  )>, financingStage?: SchemaTypes.Maybe<(
    { __typename: 'FinancingStage' }
    & Pick<SchemaTypes.FinancingStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, visitAppointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'VisitAppointmentStage' }
    & Pick<SchemaTypes.VisitAppointmentStage, 'identifier' | 'status' | 'assigneeId'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), visitAppointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ) }
  )>, appointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'AppointmentStage' }
    & Pick<SchemaTypes.AppointmentStage, 'identifier' | 'status' | 'assigneeId' | 'checkInTime' | 'checkOutTime' | 'registrationNumber' | 'hasChangedVehicle'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), appointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'hasTestDriveProcess' | 'hasTestDriveSigning' | 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ), mileage?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentMileage' }
      & Pick<SchemaTypes.AppointmentMileage, 'end' | 'start'>
    )>, changedVehicle?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentChangedVehicleLocal' }
      & Pick<SchemaTypes.AppointmentChangedVehicleLocal, 'vehicleId'>
    ) | (
      { __typename: 'AppointmentChangedVehicleText' }
      & Pick<SchemaTypes.AppointmentChangedVehicleText, 'model' | 'subModel' | 'variant'>
    )> }
  )>, insuranceStage?: SchemaTypes.Maybe<(
    { __typename: 'InsuranceStage' }
    & Pick<SchemaTypes.InsuranceStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, tradeInStage?: SchemaTypes.Maybe<(
    { __typename: 'TradeInStage' }
    & Pick<SchemaTypes.TradeInStage, 'identifier' | 'status' | 'assigneeId' | 'isQuoted' | 'lastQuotedAt'>
  )>, followUpStage?: SchemaTypes.Maybe<(
    { __typename: 'FollowUpStage' }
    & Pick<SchemaTypes.FollowUpStage, 'identifier' | 'status' | 'assigneeId' | 'remarks' | 'scheduledDate'>
  )> }
);

export type ApplicationStageData_LaunchpadApplication_Fragment = (
  { __typename: 'LaunchpadApplication' }
  & Pick<SchemaTypes.LaunchpadApplication, 'stages'>
  & { mobilityStage?: SchemaTypes.Maybe<(
    { __typename: 'MobilityStage' }
    & Pick<SchemaTypes.MobilityStage, 'identifier' | 'status' | 'assigneeId'>
  )>, reservationStage?: SchemaTypes.Maybe<(
    { __typename: 'ReservationStage' }
    & Pick<SchemaTypes.ReservationStage, 'identifier' | 'status' | 'assigneeId'>
  )>, financingStage?: SchemaTypes.Maybe<(
    { __typename: 'FinancingStage' }
    & Pick<SchemaTypes.FinancingStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, visitAppointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'VisitAppointmentStage' }
    & Pick<SchemaTypes.VisitAppointmentStage, 'identifier' | 'status' | 'assigneeId'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), visitAppointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ) }
  )>, appointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'AppointmentStage' }
    & Pick<SchemaTypes.AppointmentStage, 'identifier' | 'status' | 'assigneeId' | 'checkInTime' | 'checkOutTime' | 'registrationNumber' | 'hasChangedVehicle'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), appointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'hasTestDriveProcess' | 'hasTestDriveSigning' | 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ), mileage?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentMileage' }
      & Pick<SchemaTypes.AppointmentMileage, 'end' | 'start'>
    )>, changedVehicle?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentChangedVehicleLocal' }
      & Pick<SchemaTypes.AppointmentChangedVehicleLocal, 'vehicleId'>
    ) | (
      { __typename: 'AppointmentChangedVehicleText' }
      & Pick<SchemaTypes.AppointmentChangedVehicleText, 'model' | 'subModel' | 'variant'>
    )> }
  )>, insuranceStage?: SchemaTypes.Maybe<(
    { __typename: 'InsuranceStage' }
    & Pick<SchemaTypes.InsuranceStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, tradeInStage?: SchemaTypes.Maybe<(
    { __typename: 'TradeInStage' }
    & Pick<SchemaTypes.TradeInStage, 'identifier' | 'status' | 'assigneeId' | 'isQuoted' | 'lastQuotedAt'>
  )>, followUpStage?: SchemaTypes.Maybe<(
    { __typename: 'FollowUpStage' }
    & Pick<SchemaTypes.FollowUpStage, 'identifier' | 'status' | 'assigneeId' | 'remarks' | 'scheduledDate'>
  )> }
);

export type ApplicationStageData_MobilityApplication_Fragment = (
  { __typename: 'MobilityApplication' }
  & Pick<SchemaTypes.MobilityApplication, 'stages'>
  & { mobilityStage?: SchemaTypes.Maybe<(
    { __typename: 'MobilityStage' }
    & Pick<SchemaTypes.MobilityStage, 'identifier' | 'status' | 'assigneeId'>
  )>, reservationStage?: SchemaTypes.Maybe<(
    { __typename: 'ReservationStage' }
    & Pick<SchemaTypes.ReservationStage, 'identifier' | 'status' | 'assigneeId'>
  )>, financingStage?: SchemaTypes.Maybe<(
    { __typename: 'FinancingStage' }
    & Pick<SchemaTypes.FinancingStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, visitAppointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'VisitAppointmentStage' }
    & Pick<SchemaTypes.VisitAppointmentStage, 'identifier' | 'status' | 'assigneeId'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), visitAppointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ) }
  )>, appointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'AppointmentStage' }
    & Pick<SchemaTypes.AppointmentStage, 'identifier' | 'status' | 'assigneeId' | 'checkInTime' | 'checkOutTime' | 'registrationNumber' | 'hasChangedVehicle'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), appointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'hasTestDriveProcess' | 'hasTestDriveSigning' | 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ), mileage?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentMileage' }
      & Pick<SchemaTypes.AppointmentMileage, 'end' | 'start'>
    )>, changedVehicle?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentChangedVehicleLocal' }
      & Pick<SchemaTypes.AppointmentChangedVehicleLocal, 'vehicleId'>
    ) | (
      { __typename: 'AppointmentChangedVehicleText' }
      & Pick<SchemaTypes.AppointmentChangedVehicleText, 'model' | 'subModel' | 'variant'>
    )> }
  )>, insuranceStage?: SchemaTypes.Maybe<(
    { __typename: 'InsuranceStage' }
    & Pick<SchemaTypes.InsuranceStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, tradeInStage?: SchemaTypes.Maybe<(
    { __typename: 'TradeInStage' }
    & Pick<SchemaTypes.TradeInStage, 'identifier' | 'status' | 'assigneeId' | 'isQuoted' | 'lastQuotedAt'>
  )>, followUpStage?: SchemaTypes.Maybe<(
    { __typename: 'FollowUpStage' }
    & Pick<SchemaTypes.FollowUpStage, 'identifier' | 'status' | 'assigneeId' | 'remarks' | 'scheduledDate'>
  )> }
);

export type ApplicationStageData_SalesOfferApplication_Fragment = (
  { __typename: 'SalesOfferApplication' }
  & Pick<SchemaTypes.SalesOfferApplication, 'stages'>
  & { mobilityStage?: SchemaTypes.Maybe<(
    { __typename: 'MobilityStage' }
    & Pick<SchemaTypes.MobilityStage, 'identifier' | 'status' | 'assigneeId'>
  )>, reservationStage?: SchemaTypes.Maybe<(
    { __typename: 'ReservationStage' }
    & Pick<SchemaTypes.ReservationStage, 'identifier' | 'status' | 'assigneeId'>
  )>, financingStage?: SchemaTypes.Maybe<(
    { __typename: 'FinancingStage' }
    & Pick<SchemaTypes.FinancingStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, visitAppointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'VisitAppointmentStage' }
    & Pick<SchemaTypes.VisitAppointmentStage, 'identifier' | 'status' | 'assigneeId'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), visitAppointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ) }
  )>, appointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'AppointmentStage' }
    & Pick<SchemaTypes.AppointmentStage, 'identifier' | 'status' | 'assigneeId' | 'checkInTime' | 'checkOutTime' | 'registrationNumber' | 'hasChangedVehicle'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), appointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'hasTestDriveProcess' | 'hasTestDriveSigning' | 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ), mileage?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentMileage' }
      & Pick<SchemaTypes.AppointmentMileage, 'end' | 'start'>
    )>, changedVehicle?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentChangedVehicleLocal' }
      & Pick<SchemaTypes.AppointmentChangedVehicleLocal, 'vehicleId'>
    ) | (
      { __typename: 'AppointmentChangedVehicleText' }
      & Pick<SchemaTypes.AppointmentChangedVehicleText, 'model' | 'subModel' | 'variant'>
    )> }
  )>, insuranceStage?: SchemaTypes.Maybe<(
    { __typename: 'InsuranceStage' }
    & Pick<SchemaTypes.InsuranceStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, tradeInStage?: SchemaTypes.Maybe<(
    { __typename: 'TradeInStage' }
    & Pick<SchemaTypes.TradeInStage, 'identifier' | 'status' | 'assigneeId' | 'isQuoted' | 'lastQuotedAt'>
  )>, followUpStage?: SchemaTypes.Maybe<(
    { __typename: 'FollowUpStage' }
    & Pick<SchemaTypes.FollowUpStage, 'identifier' | 'status' | 'assigneeId' | 'remarks' | 'scheduledDate'>
  )> }
);

export type ApplicationStageData_StandardApplication_Fragment = (
  { __typename: 'StandardApplication' }
  & Pick<SchemaTypes.StandardApplication, 'stages'>
  & { mobilityStage?: SchemaTypes.Maybe<(
    { __typename: 'MobilityStage' }
    & Pick<SchemaTypes.MobilityStage, 'identifier' | 'status' | 'assigneeId'>
  )>, reservationStage?: SchemaTypes.Maybe<(
    { __typename: 'ReservationStage' }
    & Pick<SchemaTypes.ReservationStage, 'identifier' | 'status' | 'assigneeId'>
  )>, financingStage?: SchemaTypes.Maybe<(
    { __typename: 'FinancingStage' }
    & Pick<SchemaTypes.FinancingStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, visitAppointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'VisitAppointmentStage' }
    & Pick<SchemaTypes.VisitAppointmentStage, 'identifier' | 'status' | 'assigneeId'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), visitAppointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ) }
  )>, appointmentStage?: SchemaTypes.Maybe<(
    { __typename: 'AppointmentStage' }
    & Pick<SchemaTypes.AppointmentStage, 'identifier' | 'status' | 'assigneeId' | 'checkInTime' | 'checkOutTime' | 'registrationNumber' | 'hasChangedVehicle'>
    & { bookingTimeSlot: (
      { __typename: 'AppointmentBookingTimeSlot' }
      & Pick<SchemaTypes.AppointmentBookingTimeSlot, 'slot'>
    ), appointmentModule: (
      { __typename: 'AdyenPaymentModule' }
      & Pick<SchemaTypes.AdyenPaymentModule, 'id'>
    ) | (
      { __typename: 'AppointmentModule' }
      & Pick<SchemaTypes.AppointmentModule, 'hasTestDriveProcess' | 'hasTestDriveSigning' | 'id'>
    ) | (
      { __typename: 'AutoplayModule' }
      & Pick<SchemaTypes.AutoplayModule, 'id'>
    ) | (
      { __typename: 'BankModule' }
      & Pick<SchemaTypes.BankModule, 'id'>
    ) | (
      { __typename: 'BasicSigningModule' }
      & Pick<SchemaTypes.BasicSigningModule, 'id'>
    ) | (
      { __typename: 'CapModule' }
      & Pick<SchemaTypes.CapModule, 'id'>
    ) | (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id'>
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id'>
    ) | (
      { __typename: 'CtsModule' }
      & Pick<SchemaTypes.CtsModule, 'id'>
    ) | (
      { __typename: 'DocusignModule' }
      & Pick<SchemaTypes.DocusignModule, 'id'>
    ) | (
      { __typename: 'EventApplicationModule' }
      & Pick<SchemaTypes.EventApplicationModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id'>
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & Pick<SchemaTypes.FinderApplicationPublicModule, 'id'>
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & Pick<SchemaTypes.FinderVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'FiservPaymentModule' }
      & Pick<SchemaTypes.FiservPaymentModule, 'id'>
    ) | (
      { __typename: 'GiftVoucherModule' }
      & Pick<SchemaTypes.GiftVoucherModule, 'id'>
    ) | (
      { __typename: 'InsuranceModule' }
      & Pick<SchemaTypes.InsuranceModule, 'id'>
    ) | (
      { __typename: 'LabelsModule' }
      & Pick<SchemaTypes.LabelsModule, 'id'>
    ) | (
      { __typename: 'LaunchPadModule' }
      & Pick<SchemaTypes.LaunchPadModule, 'id'>
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & Pick<SchemaTypes.LocalCustomerManagementModule, 'id'>
    ) | (
      { __typename: 'MaintenanceModule' }
      & Pick<SchemaTypes.MaintenanceModule, 'id'>
    ) | (
      { __typename: 'MarketingModule' }
      & Pick<SchemaTypes.MarketingModule, 'id'>
    ) | (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id'>
    ) | (
      { __typename: 'MyInfoModule' }
      & Pick<SchemaTypes.MyInfoModule, 'id'>
    ) | (
      { __typename: 'NamirialSigningModule' }
      & Pick<SchemaTypes.NamirialSigningModule, 'id'>
    ) | (
      { __typename: 'OIDCModule' }
      & Pick<SchemaTypes.OidcModule, 'id'>
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & Pick<SchemaTypes.PayGatePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheIdModule' }
      & Pick<SchemaTypes.PorscheIdModule, 'id'>
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & Pick<SchemaTypes.PorscheMasterDataModule, 'id'>
    ) | (
      { __typename: 'PorschePaymentModule' }
      & Pick<SchemaTypes.PorschePaymentModule, 'id'>
    ) | (
      { __typename: 'PorscheRetainModule' }
      & Pick<SchemaTypes.PorscheRetainModule, 'id'>
    ) | (
      { __typename: 'PromoCodeModule' }
      & Pick<SchemaTypes.PromoCodeModule, 'id'>
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & Pick<SchemaTypes.SalesControlBoardModule, 'id'>
    ) | (
      { __typename: 'SalesOfferModule' }
      & Pick<SchemaTypes.SalesOfferModule, 'id'>
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id'>
    ) | (
      { __typename: 'StandardApplicationModule' }
      & Pick<SchemaTypes.StandardApplicationModule, 'id'>
    ) | (
      { __typename: 'TradeInModule' }
      & Pick<SchemaTypes.TradeInModule, 'id'>
    ) | (
      { __typename: 'TtbPaymentModule' }
      & Pick<SchemaTypes.TtbPaymentModule, 'id'>
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & Pick<SchemaTypes.UserlikeChatbotModule, 'id'>
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id'>
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & Pick<SchemaTypes.VisitAppointmentModule, 'id'>
    ) | (
      { __typename: 'WebsiteModule' }
      & Pick<SchemaTypes.WebsiteModule, 'id'>
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & Pick<SchemaTypes.WhatsappLiveChatModule, 'id'>
    ), mileage?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentMileage' }
      & Pick<SchemaTypes.AppointmentMileage, 'end' | 'start'>
    )>, changedVehicle?: SchemaTypes.Maybe<(
      { __typename: 'AppointmentChangedVehicleLocal' }
      & Pick<SchemaTypes.AppointmentChangedVehicleLocal, 'vehicleId'>
    ) | (
      { __typename: 'AppointmentChangedVehicleText' }
      & Pick<SchemaTypes.AppointmentChangedVehicleText, 'model' | 'subModel' | 'variant'>
    )> }
  )>, insuranceStage?: SchemaTypes.Maybe<(
    { __typename: 'InsuranceStage' }
    & Pick<SchemaTypes.InsuranceStage, 'identifier' | 'status' | 'assigneeId' | 'isDraft'>
  )>, tradeInStage?: SchemaTypes.Maybe<(
    { __typename: 'TradeInStage' }
    & Pick<SchemaTypes.TradeInStage, 'identifier' | 'status' | 'assigneeId' | 'isQuoted' | 'lastQuotedAt'>
  )>, followUpStage?: SchemaTypes.Maybe<(
    { __typename: 'FollowUpStage' }
    & Pick<SchemaTypes.FollowUpStage, 'identifier' | 'status' | 'assigneeId' | 'remarks' | 'scheduledDate'>
  )> }
);

export type ApplicationStageDataFragment = ApplicationStageData_ConfiguratorApplication_Fragment | ApplicationStageData_EventApplication_Fragment | ApplicationStageData_FinderApplication_Fragment | ApplicationStageData_LaunchpadApplication_Fragment | ApplicationStageData_MobilityApplication_Fragment | ApplicationStageData_SalesOfferApplication_Fragment | ApplicationStageData_StandardApplication_Fragment;

export const ApplicationStageDataFragmentDoc = /*#__PURE__*/ gql`
    fragment ApplicationStageData on Application {
  stages
  mobilityStage {
    identifier
    status
    assigneeId
  }
  reservationStage {
    identifier
    status
    assigneeId
  }
  financingStage {
    identifier
    status
    assigneeId
    isDraft
  }
  visitAppointmentStage {
    identifier
    status
    bookingTimeSlot {
      slot
    }
    assigneeId
    visitAppointmentModule {
      id
    }
  }
  appointmentStage {
    identifier
    status
    bookingTimeSlot {
      slot
    }
    assigneeId
    appointmentModule {
      id
      ... on AppointmentModule {
        hasTestDriveProcess
        hasTestDriveSigning
      }
    }
    mileage {
      end
      start
    }
    checkInTime
    checkOutTime
    registrationNumber
    hasChangedVehicle
    changedVehicle {
      ... on AppointmentChangedVehicleLocal {
        vehicleId
      }
      ... on AppointmentChangedVehicleText {
        model
        subModel
        variant
      }
    }
  }
  insuranceStage {
    identifier
    status
    assigneeId
    isDraft
  }
  tradeInStage {
    identifier
    status
    assigneeId
    isQuoted
    lastQuotedAt
  }
  followUpStage {
    identifier
    status
    assigneeId
    remarks
    scheduledDate
  }
}
    `;