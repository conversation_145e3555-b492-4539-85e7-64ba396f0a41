import type * as SchemaTypes from '../types';

import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { ApplicationEventCustomizedFieldDataFragment } from './ApplicationEventCustomizedFieldData';
import { gql } from '@apollo/client';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { ApplicationEventCustomizedFieldDataFragmentDoc } from './ApplicationEventCustomizedFieldData';
export type EventLeadDataFragment = (
  { __typename: 'EventLead' }
  & Pick<SchemaTypes.EventLead, 'eventId' | 'mergedToLeadSuiteId' | 'mergedToLeadIdentifier'>
  & { vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & VehicleSpecs_FinderVehicle_Fragment
  ) | (
    { __typename: 'LocalMake' }
    & VehicleSpecs_LocalMake_Fragment
  ) | (
    { __typename: 'LocalModel' }
    & VehicleSpecs_LocalModel_Fragment
  ) | (
    { __typename: 'LocalVariant' }
    & VehicleSpecs_LocalVariant_Fragment
  )>, event?: SchemaTypes.Maybe<(
    { __typename: 'Event' }
    & Pick<SchemaTypes.Event, 'capPrequalification' | 'isCapEnabled'>
    & { kycExtraSettings: (
      { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
      & KycExtraSettingsSpecsFragment
    ), utmParametersSettings: (
      { __typename: 'EventUtmParameters' }
      & { defaultValue: (
        { __typename: 'EventUtmParametersDefaultValue' }
        & Pick<SchemaTypes.EventUtmParametersDefaultValue, 'capCampaignId' | 'capLeadSource' | 'capLeadOrigin'>
      ), overrides: Array<(
        { __typename: 'EventUtmParametersOverride' }
        & Pick<SchemaTypes.EventUtmParametersOverride, 'capCampaignId' | 'capLeadSource' | 'capLeadOrigin' | 'utmUrl'>
      )> }
    ) }
  )>, customizedFields: Array<(
    { __typename: 'EventCustomizedField' }
    & ApplicationEventCustomizedFieldDataFragment
  )> }
);

export const EventLeadDataFragmentDoc = /*#__PURE__*/ gql`
    fragment EventLeadData on EventLead {
  vehicle {
    ...VehicleSpecs
  }
  event {
    capPrequalification
    isCapEnabled
    kycExtraSettings {
      ...KYCExtraSettingsSpecs
    }
    utmParametersSettings {
      defaultValue {
        capCampaignId
        capLeadSource
        capLeadOrigin
      }
      overrides {
        capCampaignId
        capLeadSource
        capLeadOrigin
        utmUrl
      }
    }
  }
  customizedFields {
    ...ApplicationEventCustomizedFieldData
  }
  eventId
  mergedToLeadSuiteId
  mergedToLeadIdentifier
}
    `;