fragment CompanySpecs on Company {
    id
    displayName
    companyName {
        ...TranslatedStringData
    }
    legalName {
        ...TranslatedStringData
    }
    countryCode
    marketCode
    coe
    ppsr
    estFee
    currency
    isActive
    email
    description {
        ...TranslatedStringData
    }
    color
    theme
    timeZone
    copyright {
        ...TranslatedStringData
    }
    address

    phone {
        value
        prefix
    }

    roundings {
        amount {
            decimals
        }
        percentage {
            decimals
        }
    }

    calculationRounding

    vatRateSettings {
        vatRateTable {
            startDate
            value
        }
        appliedVATRate
    }

    emailSettings {
        provider

        ... on SMTPEmailSettings {
            from
            user
            password
            host
            isAuthenticationRequired
            isSslEnabled
            port
            certificate
        }
    }

    smsSettings {
        provider

        ... on TwilioSmsSettings {
            account
            token
            sender {
                prefix
                value
            }
        }
    }

    languages {
        id
    }

    logo {
        ...UploadFileWithPreviewFormData
    }

    logoNonWhiteBackground {
        ...UploadFileWithPreviewFormData
    }

    mobileLogo {
        ...UploadFileWithPreviewFormData
    }

    favicon {
        ...UploadFileWithPreviewFormData
    }

    font {
        ...UploadFileFormData
    }

    fontBold {
        ...UploadFileFormData
    }

    sessionTimeout
    passwordConfiguration

    mfaSettings {
        type
    }

    edmEmailFooter {
        connectText {
            ...TranslatedStringData
        }
        disclaimerText {
            ...TranslatedStringData
        }
        privacyPolicyUrl
        legalNoticeUrl
        arrowIcon {
            ...UploadFileWithPreviewFormData
        }
        emailIcon {
            ...UploadFileWithPreviewFormData
        }
        phoneIcon {
            ...UploadFileWithPreviewFormData
        }
        copyRight {
            ...TranslatedStringData
        }
        socialMedia {
            id
            altText
            url
            icon {
                ...UploadFileWithPreviewFormData
            }
        }
    }

    versioning {
        ...SimpleVersioningData
    }

    mask {
        count
        direction
    }

    isDataPurgeEnabled
    dataPurgeAfter
    enableContentRefinement
    isInstantApprovalStatsEnabled
    allowLimitDealerFeature
    addressAutofill
    shouldSendCalendarInvite
    findNearbyDealer
}
