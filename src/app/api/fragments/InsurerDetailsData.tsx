import type * as SchemaTypes from '../types';

import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { InsurerIntegrationData_EazyInsurerIntegration_Fragment, InsurerIntegrationData_EmailInsurerIntegration_Fragment } from './InsurerIntegrationData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { InsuranceProductDetails_Eazy_Fragment, InsuranceProductDetails_ErgoLookupTable_Fragment } from './InsuranceProductDetails';
import type { EazyDetailsFragment } from './EazyDetails';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from './PeriodData';
import type { ErgoLookupTableDetailsFragment } from './ErgoLookupTableDetails';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import { gql } from '@apollo/client';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { InsurerIntegrationDataFragmentDoc } from './InsurerIntegrationData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { InsuranceProductDetailsFragmentDoc } from './InsuranceProductDetails';
import { EazyDetailsFragmentDoc } from './EazyDetails';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { ErgoLookupTableDetailsFragmentDoc } from './ErgoLookupTableDetails';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
export type InsurerDetailsDataFragment = (
  { __typename: 'Insurer' }
  & Pick<SchemaTypes.Insurer, 'id' | 'displayName' | 'moduleId' | 'email' | 'order' | 'sendEmailToCustomer' | 'sendPDFToCustomer' | 'showCommentsField' | 'isActive' | 'submissionApprovalModuleId' | 'reSubmissionApprovalModuleId' | 'insuranceProductTypes' | 'permissions'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'companyId'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone'>
    ) }
  ), legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  ), integration: (
    { __typename: 'EazyInsurerIntegration' }
    & InsurerIntegrationData_EazyInsurerIntegration_Fragment
  ) | (
    { __typename: 'EmailInsurerIntegration' }
    & InsurerIntegrationData_EmailInsurerIntegration_Fragment
  ), insuranceDisclaimer?: SchemaTypes.Maybe<(
    { __typename: 'DealerDisclaimersConfigurator' }
    & DealerDisclaimersConfiguratorDataFragment
  )>, insuranceProducts: Array<(
    { __typename: 'Eazy' }
    & InsuranceProductDetails_Eazy_Fragment
  ) | (
    { __typename: 'ErgoLookupTable' }
    & InsuranceProductDetails_ErgoLookupTable_Fragment
  )>, versioning: (
    { __typename: 'SimpleVersioning' }
    & SimpleVersioningDataFragment
  ) }
);

export const InsurerDetailsDataFragmentDoc = /*#__PURE__*/ gql`
    fragment InsurerDetailsData on Insurer {
  id
  displayName
  moduleId
  module {
    id
    companyId
    company {
      id
      timeZone
    }
  }
  legalName {
    ...TranslatedStringSpecs
  }
  email
  order
  integration {
    ...InsurerIntegrationData
  }
  sendEmailToCustomer
  sendPDFToCustomer
  showCommentsField
  isActive
  insuranceDisclaimer {
    ...DealerDisclaimersConfiguratorData
  }
  submissionApprovalModuleId
  reSubmissionApprovalModuleId
  insuranceProducts {
    ... on Eazy {
      ...InsuranceProductDetails
    }
    ... on ErgoLookupTable {
      ...InsuranceProductDetails
    }
  }
  insuranceProductTypes
  permissions
  versioning {
    ...SimpleVersioningData
  }
}
    `;