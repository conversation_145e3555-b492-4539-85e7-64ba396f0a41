import type * as SchemaTypes from '../types';

import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from './InsuranceProductListData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from './PeriodData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { ErgoLookupTableSettingsDetailsFragment } from './ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import { gql } from '@apollo/client';
import { InsuranceProductListDataFragmentDoc } from './InsuranceProductListData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from './ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
export type DealerInsuranceProductsSpecsFragment = (
  { __typename: 'DealerInsuranceProducts' }
  & Pick<SchemaTypes.DealerInsuranceProducts, 'dealerId' | 'insuranceProductSuiteIds'>
  & { insuranceProducts: Array<(
    { __typename: 'Eazy' }
    & InsuranceProductListData_Eazy_Fragment
  ) | (
    { __typename: 'ErgoLookupTable' }
    & InsuranceProductListData_ErgoLookupTable_Fragment
  )>, dealer: (
    { __typename: 'Dealer' }
    & Pick<SchemaTypes.Dealer, 'id'>
    & { legalName: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) }
);

export const DealerInsuranceProductsSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment DealerInsuranceProductsSpecs on DealerInsuranceProducts {
  dealerId
  insuranceProductSuiteIds
  insuranceProducts {
    ... on Eazy {
      ...InsuranceProductListData
    }
    ... on ErgoLookupTable {
      ...InsuranceProductListData
    }
  }
  dealer {
    id
    legalName {
      ...TranslatedStringData
    }
  }
}
    `;