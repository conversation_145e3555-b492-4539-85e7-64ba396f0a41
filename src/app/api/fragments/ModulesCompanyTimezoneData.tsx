import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment = (
  { __typename: 'AdyenPaymentModule' }
  & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_AppointmentModule_Fragment = (
  { __typename: 'AppointmentModule' }
  & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_AutoplayModule_Fragment = (
  { __typename: 'AutoplayModule' }
  & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_BankModule_Fragment = (
  { __typename: 'BankModule' }
  & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_BasicSigningModule_Fragment = (
  { __typename: 'BasicSigningModule' }
  & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_CapModule_Fragment = (
  { __typename: 'CapModule' }
  & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_ConfiguratorModule_Fragment = (
  { __typename: 'ConfiguratorModule' }
  & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment = (
  { __typename: 'ConsentsAndDeclarationsModule' }
  & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_CtsModule_Fragment = (
  { __typename: 'CtsModule' }
  & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_DocusignModule_Fragment = (
  { __typename: 'DocusignModule' }
  & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_EventApplicationModule_Fragment = (
  { __typename: 'EventApplicationModule' }
  & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment = (
  { __typename: 'FinderApplicationPrivateModule' }
  & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment = (
  { __typename: 'FinderApplicationPublicModule' }
  & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment = (
  { __typename: 'FinderVehicleManagementModule' }
  & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_FiservPaymentModule_Fragment = (
  { __typename: 'FiservPaymentModule' }
  & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_GiftVoucherModule_Fragment = (
  { __typename: 'GiftVoucherModule' }
  & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_InsuranceModule_Fragment = (
  { __typename: 'InsuranceModule' }
  & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_LabelsModule_Fragment = (
  { __typename: 'LabelsModule' }
  & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_LaunchPadModule_Fragment = (
  { __typename: 'LaunchPadModule' }
  & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment = (
  { __typename: 'LocalCustomerManagementModule' }
  & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_MaintenanceModule_Fragment = (
  { __typename: 'MaintenanceModule' }
  & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_MarketingModule_Fragment = (
  { __typename: 'MarketingModule' }
  & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_MobilityModule_Fragment = (
  { __typename: 'MobilityModule' }
  & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_MyInfoModule_Fragment = (
  { __typename: 'MyInfoModule' }
  & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_NamirialSigningModule_Fragment = (
  { __typename: 'NamirialSigningModule' }
  & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_OidcModule_Fragment = (
  { __typename: 'OIDCModule' }
  & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment = (
  { __typename: 'PayGatePaymentModule' }
  & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_PorscheIdModule_Fragment = (
  { __typename: 'PorscheIdModule' }
  & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment = (
  { __typename: 'PorscheMasterDataModule' }
  & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_PorschePaymentModule_Fragment = (
  { __typename: 'PorschePaymentModule' }
  & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_PorscheRetainModule_Fragment = (
  { __typename: 'PorscheRetainModule' }
  & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_PromoCodeModule_Fragment = (
  { __typename: 'PromoCodeModule' }
  & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment = (
  { __typename: 'SalesControlBoardModule' }
  & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_SalesOfferModule_Fragment = (
  { __typename: 'SalesOfferModule' }
  & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment = (
  { __typename: 'SimpleVehicleManagementModule' }
  & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_StandardApplicationModule_Fragment = (
  { __typename: 'StandardApplicationModule' }
  & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_TradeInModule_Fragment = (
  { __typename: 'TradeInModule' }
  & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_TtbPaymentModule_Fragment = (
  { __typename: 'TtbPaymentModule' }
  & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment = (
  { __typename: 'UserlikeChatbotModule' }
  & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment = (
  { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
  & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment = (
  { __typename: 'VisitAppointmentModule' }
  & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_WebsiteModule_Fragment = (
  { __typename: 'WebsiteModule' }
  & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment = (
  { __typename: 'WhatsappLiveChatModule' }
  & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'displayName'>
  ) }
);

export type ModulesCompanyTimezoneDataFragment = ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment | ModulesCompanyTimezoneData_AppointmentModule_Fragment | ModulesCompanyTimezoneData_AutoplayModule_Fragment | ModulesCompanyTimezoneData_BankModule_Fragment | ModulesCompanyTimezoneData_BasicSigningModule_Fragment | ModulesCompanyTimezoneData_CapModule_Fragment | ModulesCompanyTimezoneData_ConfiguratorModule_Fragment | ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment | ModulesCompanyTimezoneData_CtsModule_Fragment | ModulesCompanyTimezoneData_DocusignModule_Fragment | ModulesCompanyTimezoneData_EventApplicationModule_Fragment | ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment | ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment | ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment | ModulesCompanyTimezoneData_FiservPaymentModule_Fragment | ModulesCompanyTimezoneData_GiftVoucherModule_Fragment | ModulesCompanyTimezoneData_InsuranceModule_Fragment | ModulesCompanyTimezoneData_LabelsModule_Fragment | ModulesCompanyTimezoneData_LaunchPadModule_Fragment | ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment | ModulesCompanyTimezoneData_MaintenanceModule_Fragment | ModulesCompanyTimezoneData_MarketingModule_Fragment | ModulesCompanyTimezoneData_MobilityModule_Fragment | ModulesCompanyTimezoneData_MyInfoModule_Fragment | ModulesCompanyTimezoneData_NamirialSigningModule_Fragment | ModulesCompanyTimezoneData_OidcModule_Fragment | ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment | ModulesCompanyTimezoneData_PorscheIdModule_Fragment | ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment | ModulesCompanyTimezoneData_PorschePaymentModule_Fragment | ModulesCompanyTimezoneData_PorscheRetainModule_Fragment | ModulesCompanyTimezoneData_PromoCodeModule_Fragment | ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment | ModulesCompanyTimezoneData_SalesOfferModule_Fragment | ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment | ModulesCompanyTimezoneData_StandardApplicationModule_Fragment | ModulesCompanyTimezoneData_TradeInModule_Fragment | ModulesCompanyTimezoneData_TtbPaymentModule_Fragment | ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment | ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment | ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment | ModulesCompanyTimezoneData_WebsiteModule_Fragment | ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment;

export const ModulesCompanyTimezoneDataFragmentDoc = /*#__PURE__*/ gql`
    fragment ModulesCompanyTimezoneData on Module {
  id
  displayName
  company {
    id
    timeZone
    displayName
  }
}
    `;