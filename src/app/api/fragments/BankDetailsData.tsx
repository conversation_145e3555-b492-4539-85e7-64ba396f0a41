import type * as SchemaTypes from '../types';

import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from './BankIntegrationData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from './FinanceProductDetailsData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from './PeriodData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import { gql } from '@apollo/client';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { BankIntegrationDataFragmentDoc } from './BankIntegrationData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from './FinanceProductDetailsData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
export type BankDetailsDataFragment = (
  { __typename: 'SystemBank' }
  & Pick<SchemaTypes.SystemBank, 'id' | 'displayName' | 'moduleId' | 'email' | 'color' | 'order' | 'remoteFlowAcknowledgmentInfo' | 'availableFinanceProductTypes' | 'showMarginOfFinance' | 'submissionApprovalModuleId' | 'reSubmissionApprovalModuleId' | 'sendEmailToCustomer' | 'sendPDFToCustomer' | 'isActive' | 'hasVSOUpload' | 'hasUploadDocuments' | 'estFee' | 'allowVariantCodeMapping' | 'showCommentsField'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'companyId' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'timeZone' | 'countryCode'>
    ) }
  ), legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  ), financingDisclaimer?: SchemaTypes.Maybe<(
    { __typename: 'DealerDisclaimersConfigurator' }
    & DealerDisclaimersConfiguratorDataFragment
  )>, integration: (
    { __typename: 'DbsBankIntegration' }
    & BankIntegrationData_DbsBankIntegration_Fragment
  ) | (
    { __typename: 'EmailBankIntegration' }
    & BankIntegrationData_EmailBankIntegration_Fragment
  ) | (
    { __typename: 'EnbdBankIntegration' }
    & BankIntegrationData_EnbdBankIntegration_Fragment
  ) | (
    { __typename: 'HlfBankIntegration' }
    & BankIntegrationData_HlfBankIntegration_Fragment
  ) | (
    { __typename: 'HlfBankV2Integration' }
    & BankIntegrationData_HlfBankV2Integration_Fragment
  ) | (
    { __typename: 'MaybankIntegration' }
    & BankIntegrationData_MaybankIntegration_Fragment
  ) | (
    { __typename: 'UobBankIntegration' }
    & BankIntegrationData_UobBankIntegration_Fragment
  ), logo?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, font?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFile' }
    & UploadFileFormDataFragment
  )>, financeProducts: Array<(
    { __typename: 'LocalDeferredPrincipal' }
    & FinanceProductDetailsData_LocalDeferredPrincipal_Fragment
  ) | (
    { __typename: 'LocalHirePurchase' }
    & FinanceProductDetailsData_LocalHirePurchase_Fragment
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloon' }
    & FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloonGFV' }
    & FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment
  ) | (
    { __typename: 'LocalLease' }
    & FinanceProductDetailsData_LocalLease_Fragment
  ) | (
    { __typename: 'LocalLeasePurchase' }
    & FinanceProductDetailsData_LocalLeasePurchase_Fragment
  ) | (
    { __typename: 'LocalUcclLeasing' }
    & FinanceProductDetailsData_LocalUcclLeasing_Fragment
  )>, versioning: (
    { __typename: 'SimpleVersioning' }
    & SimpleVersioningDataFragment
  ) }
);

export const BankDetailsDataFragmentDoc = /*#__PURE__*/ gql`
    fragment BankDetailsData on SystemBank {
  id
  displayName
  moduleId
  module {
    id
    companyId
    company {
      id
      timeZone
      countryCode
    }
    displayName
  }
  legalName {
    ...TranslatedStringSpecs
  }
  email
  color
  order
  remoteFlowAcknowledgmentInfo
  financingDisclaimer {
    ...DealerDisclaimersConfiguratorData
  }
  integration {
    ...BankIntegrationData
  }
  logo {
    ...UploadFileWithPreviewFormData
  }
  font {
    ...UploadFileFormData
  }
  availableFinanceProductTypes
  financeProducts {
    ... on FinanceProduct {
      ...FinanceProductDetailsData
    }
  }
  showMarginOfFinance
  submissionApprovalModuleId
  reSubmissionApprovalModuleId
  sendEmailToCustomer
  sendPDFToCustomer
  isActive
  hasVSOUpload
  hasUploadDocuments
  estFee
  allowVariantCodeMapping
  showCommentsField
  versioning {
    ...SimpleVersioningData
  }
}
    `;