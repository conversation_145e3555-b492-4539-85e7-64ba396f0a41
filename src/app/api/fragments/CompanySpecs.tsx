import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
export type CompanySpecsFragment = (
  { __typename: 'Company' }
  & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'countryCode' | 'marketCode' | 'coe' | 'ppsr' | 'estFee' | 'currency' | 'isActive' | 'email' | 'color' | 'theme' | 'timeZone' | 'address' | 'calculationRounding' | 'sessionTimeout' | 'passwordConfiguration' | 'isDataPurgeEnabled' | 'dataPurgeAfter' | 'enableContentRefinement' | 'isInstantApprovalStatsEnabled' | 'allowLimitDealerFeature' | 'addressAutofill' | 'shouldSendCalendarInvite' | 'findNearbyDealer'>
  & { companyName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), description?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, copyright: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), phone?: SchemaTypes.Maybe<(
    { __typename: 'Phone' }
    & Pick<SchemaTypes.Phone, 'value' | 'prefix'>
  )>, roundings: (
    { __typename: 'Roundings' }
    & { amount: (
      { __typename: 'Rounding' }
      & Pick<SchemaTypes.Rounding, 'decimals'>
    ), percentage: (
      { __typename: 'Rounding' }
      & Pick<SchemaTypes.Rounding, 'decimals'>
    ) }
  ), vatRateSettings?: SchemaTypes.Maybe<(
    { __typename: 'VATRateSettings' }
    & Pick<SchemaTypes.VatRateSettings, 'appliedVATRate'>
    & { vatRateTable: Array<(
      { __typename: 'VATRateSettingItem' }
      & Pick<SchemaTypes.VatRateSettingItem, 'startDate' | 'value'>
    )> }
  )>, emailSettings: (
    { __typename: 'SMTPEmailSettings' }
    & Pick<SchemaTypes.SmtpEmailSettings, 'from' | 'user' | 'password' | 'host' | 'isAuthenticationRequired' | 'isSslEnabled' | 'port' | 'certificate' | 'provider'>
  ) | (
    { __typename: 'SystemEmailSettings' }
    & Pick<SchemaTypes.SystemEmailSettings, 'provider'>
  ), smsSettings: (
    { __typename: 'SystemSmsSettings' }
    & Pick<SchemaTypes.SystemSmsSettings, 'provider'>
  ) | (
    { __typename: 'TwilioSmsSettings' }
    & Pick<SchemaTypes.TwilioSmsSettings, 'account' | 'token' | 'provider'>
    & { sender: (
      { __typename: 'Phone' }
      & Pick<SchemaTypes.Phone, 'prefix' | 'value'>
    ) }
  ), languages: Array<(
    { __typename: 'LanguagePack' }
    & Pick<SchemaTypes.LanguagePack, 'id'>
  )>, logo?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, logoNonWhiteBackground?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, mobileLogo?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, favicon?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, font?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFile' }
    & UploadFileFormDataFragment
  )>, fontBold?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFile' }
    & UploadFileFormDataFragment
  )>, mfaSettings?: SchemaTypes.Maybe<(
    { __typename: 'MFASettings' }
    & Pick<SchemaTypes.MfaSettings, 'type'>
  )>, edmEmailFooter: (
    { __typename: 'EdmEmailFooter' }
    & Pick<SchemaTypes.EdmEmailFooter, 'privacyPolicyUrl' | 'legalNoticeUrl'>
    & { connectText: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ), disclaimerText: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ), arrowIcon?: SchemaTypes.Maybe<(
      { __typename: 'UploadedFileWithPreview' }
      & UploadFileWithPreviewFormDataFragment
    )>, emailIcon?: SchemaTypes.Maybe<(
      { __typename: 'UploadedFileWithPreview' }
      & UploadFileWithPreviewFormDataFragment
    )>, phoneIcon?: SchemaTypes.Maybe<(
      { __typename: 'UploadedFileWithPreview' }
      & UploadFileWithPreviewFormDataFragment
    )>, copyRight: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ), socialMedia: Array<(
      { __typename: 'EdmEmailSocialMedia' }
      & Pick<SchemaTypes.EdmEmailSocialMedia, 'id' | 'altText' | 'url'>
      & { icon?: SchemaTypes.Maybe<(
        { __typename: 'UploadedFileWithPreview' }
        & UploadFileWithPreviewFormDataFragment
      )> }
    )> }
  ), versioning: (
    { __typename: 'SimpleVersioning' }
    & SimpleVersioningDataFragment
  ), mask: (
    { __typename: 'MaskSettings' }
    & Pick<SchemaTypes.MaskSettings, 'count' | 'direction'>
  ) }
);

export const CompanySpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment CompanySpecs on Company {
  id
  displayName
  companyName {
    ...TranslatedStringData
  }
  legalName {
    ...TranslatedStringData
  }
  countryCode
  marketCode
  coe
  ppsr
  estFee
  currency
  isActive
  email
  description {
    ...TranslatedStringData
  }
  color
  theme
  timeZone
  copyright {
    ...TranslatedStringData
  }
  address
  phone {
    value
    prefix
  }
  roundings {
    amount {
      decimals
    }
    percentage {
      decimals
    }
  }
  calculationRounding
  vatRateSettings {
    vatRateTable {
      startDate
      value
    }
    appliedVATRate
  }
  emailSettings {
    provider
    ... on SMTPEmailSettings {
      from
      user
      password
      host
      isAuthenticationRequired
      isSslEnabled
      port
      certificate
    }
  }
  smsSettings {
    provider
    ... on TwilioSmsSettings {
      account
      token
      sender {
        prefix
        value
      }
    }
  }
  languages {
    id
  }
  logo {
    ...UploadFileWithPreviewFormData
  }
  logoNonWhiteBackground {
    ...UploadFileWithPreviewFormData
  }
  mobileLogo {
    ...UploadFileWithPreviewFormData
  }
  favicon {
    ...UploadFileWithPreviewFormData
  }
  font {
    ...UploadFileFormData
  }
  fontBold {
    ...UploadFileFormData
  }
  sessionTimeout
  passwordConfiguration
  mfaSettings {
    type
  }
  edmEmailFooter {
    connectText {
      ...TranslatedStringData
    }
    disclaimerText {
      ...TranslatedStringData
    }
    privacyPolicyUrl
    legalNoticeUrl
    arrowIcon {
      ...UploadFileWithPreviewFormData
    }
    emailIcon {
      ...UploadFileWithPreviewFormData
    }
    phoneIcon {
      ...UploadFileWithPreviewFormData
    }
    copyRight {
      ...TranslatedStringData
    }
    socialMedia {
      id
      altText
      url
      icon {
        ...UploadFileWithPreviewFormData
      }
    }
  }
  versioning {
    ...SimpleVersioningData
  }
  mask {
    count
    direction
  }
  isDataPurgeEnabled
  dataPurgeAfter
  enableContentRefinement
  isInstantApprovalStatsEnabled
  allowLimitDealerFeature
  addressAutofill
  shouldSendCalendarInvite
  findNearbyDealer
}
    `;