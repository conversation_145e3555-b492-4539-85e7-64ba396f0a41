/* eslint-disable */

      export interface PossibleTypesResultData {
        possibleTypes: {
          [key: string]: string[]
        }
      }
      const result: PossibleTypesResultData = {
  "possibleTypes": {
    "Application": [
      "ConfiguratorApplication",
      "EventApplication",
      "FinderApplication",
      "LaunchpadApplication",
      "MobilityApplication",
      "SalesOfferApplication",
      "StandardApplication"
    ],
    "ApplicationAgreement": [
      "CheckboxApplicationAgreement",
      "MarketingApplicationAgreement",
      "TextApplicationAgreement"
    ],
    "ApplicationConfiguratorBlock": [
      "ApplicationConfiguratorColorSetting",
      "ApplicationConfiguratorOptionSetting",
      "ApplicationConfiguratorPackageSetting",
      "ApplicationConfiguratorTrimSetting"
    ],
    "ApplicationDeposit": [
      "ApplicationAdyenDeposit",
      "ApplicationFiservDeposit",
      "ApplicationPayGateDeposit",
      "ApplicationPorscheDeposit",
      "ApplicationTtbDeposit"
    ],
    "ApplicationFinancing": [
      "DefaultApplicationFinancing",
      "NewZealandApplicationFinancing",
      "SingaporeApplicationFinancing"
    ],
    "ApplicationInsurancing": [
      "DefaultApplicationInsurancing",
      "NewZealandApplicationInsurancing",
      "SingaporeApplicationInsurancing"
    ],
    "ApplicationMarketType": [
      "DefaultApplicationMarket",
      "NewZealandApplicationMarket",
      "SingaporeApplicationMarket"
    ],
    "ApplicationOptionSettings": [
      "ApplicationComboSettings",
      "DropdownOptionSettings",
      "MultiSelectOptionSettings",
      "SingleSelectOptionSettings"
    ],
    "ApplicationQuotation": [
      "EnbdApplicationQuotation"
    ],
    "ApplicationSigning": [
      "ApplicationNamirialSigning",
      "ApplicationOTPSigning"
    ],
    "ApplicationStageDetails": [
      "AppointmentStage",
      "FinancingStage",
      "FollowUpStage",
      "InsuranceStage",
      "MobilityStage",
      "ReservationStage",
      "TradeInStage",
      "VisitAppointmentStage"
    ],
    "AppointmentChangedVehicle": [
      "AppointmentChangedVehicleLocal",
      "AppointmentChangedVehicleText"
    ],
    "AuthenticationResponse": [
      "AuthenticationRequiresNewPassword",
      "AuthenticationRequiresSmsOTP",
      "AuthenticationRequiresTOTP",
      "AuthenticationSuccessful"
    ],
    "Author": [
      "CorporateCustomer",
      "ExternalBank",
      "Guarantor",
      "LocalCustomer",
      "PorscheRetain",
      "Salesforce",
      "SystemBank",
      "User"
    ],
    "BalloonSettings": [
      "BalloonRangeSettings",
      "BalloonTableSettings"
    ],
    "Bank": [
      "ExternalBank",
      "SystemBank"
    ],
    "BankIntegration": [
      "DbsBankIntegration",
      "EmailBankIntegration",
      "EnbdBankIntegration",
      "HlfBankIntegration",
      "HlfBankV2Integration",
      "MaybankIntegration",
      "UobBankIntegration"
    ],
    "Block": [
      "ColorBlock",
      "OptionsBlock",
      "PackageBlock",
      "TrimBlock"
    ],
    "CalculatorResult": [
      "CalculatorDeferredPrincipalResult",
      "CalculatorHirePurchaseResult",
      "CalculatorHirePurchaseWithBalloonGFVResult",
      "CalculatorHirePurchaseWithBalloonResult",
      "CalculatorLeasePurchaseResult",
      "CalculatorLeaseResult",
      "CalculatorUcclLeasingResult"
    ],
    "CapMetadata": [
      "CapVehicleMakeMetadata",
      "CapVehicleModelMetadata"
    ],
    "Condition": [
      "ApplicationModuleCondition",
      "BankCondition",
      "ContextualCondition",
      "DealerCondition",
      "GiftVoucherCondition",
      "InsurerCondition",
      "LocationCondition",
      "LogicCondition",
      "SalesOfferAgreementsCondition"
    ],
    "Configurator": [
      "ModelConfigurator",
      "VariantConfigurator"
    ],
    "ConsentsAndDeclarations": [
      "CheckboxConsentsAndDeclarations",
      "GroupConsentsAndDeclarations",
      "MarketingConsentsAndDeclarations",
      "TextConsentsAndDeclarations"
    ],
    "Customer": [
      "CorporateCustomer",
      "Guarantor",
      "LocalCustomer"
    ],
    "DealershipSetting": [
      "DealershipMyInfoSetting",
      "DealershipPaymentSetting",
      "DealershipPublicSalesPerson"
    ],
    "DepositSettings": [
      "DepositRangeSettings",
      "DepositTableSettings"
    ],
    "DiscountCode": [
      "GiftVoucher",
      "PromoCode"
    ],
    "DownPaymentSettings": [
      "DownPaymentRangeSettings",
      "DownPaymentTableSettings"
    ],
    "EmailSettings": [
      "SMTPEmailSettings",
      "SystemEmailSettings"
    ],
    "Endpoint": [
      "ApplicationListEndpoint",
      "ConfiguratorApplicationEntrypoint",
      "CustomerListEndpoint",
      "DummyPrivatePageEndpoint",
      "DummyWelcomePageEndpoint",
      "EventApplicationEntrypoint",
      "FinderApplicationEntrypoint",
      "FinderApplicationPublicAccessEntrypoint",
      "LaunchPadApplicationEntrypoint",
      "LeadListEndpoint",
      "MobilityApplicationEntrypoint",
      "StandardApplicationEntrypoint",
      "StandardApplicationPublicAccessEntrypoint",
      "WebPageEndpoint"
    ],
    "ExternalLink": [
      "AdyenRedirectionLink",
      "ApplyNewRedirectionLink",
      "CTSFinderRedirectionLink",
      "ConfiguratorApplicationLink",
      "CreateNewUserLink",
      "EventApplicationLink",
      "FinderApplicationLink",
      "FiservPaymentRedirectionLink",
      "GiftVoucherAdyenRedirectionLink",
      "GiftVoucherFiservPaymentRedirectionLink",
      "GiftVoucherPayGatePaymentRedirectionLink",
      "GiftVoucherPorschePaymentRedirectionLink",
      "GiftVoucherTtbPaymentRedirectionLink",
      "MobilityApplicationAmendmentLink",
      "MobilityApplicationCancellationLink",
      "MyInfoCallbackLink",
      "NamirialSigningLink",
      "PayGatePaymentRedirectionLink",
      "PorscheIdCallbackLink",
      "PorschePaymentRedirectionLink",
      "ProceedWithCustomerLink",
      "ResetPasswordLink",
      "SalesOfferNamirialSigningLink",
      "SendSalesOfferLink",
      "StandardApplicationLink",
      "TestDriveProcessRedirectionLink",
      "TtbPaymentRedirectionLink",
      "VerifyEmailUpdateLink"
    ],
    "FinanceProduct": [
      "LocalDeferredPrincipal",
      "LocalHirePurchase",
      "LocalHirePurchaseWithBalloon",
      "LocalHirePurchaseWithBalloonGFV",
      "LocalLease",
      "LocalLeasePurchase",
      "LocalUcclLeasing"
    ],
    "FpTables": [
      "ImportedBalloonTableCell",
      "ImportedDepositTableCell",
      "ImportedDisplacementTableCell",
      "ImportedDownPaymentTableCell",
      "ImportedErgoLookupTableCell",
      "ImportedInsuranceFeeTableCell",
      "ImportedInterestRateTableCell",
      "ImportedLeaseTableCell",
      "ImportedResidualValueTableCell"
    ],
    "ICurrencyType": [
      "Currency",
      "DownPayment",
      "Price",
      "Rate"
    ],
    "ILocalizedValue": [
      "LocalizedColorGroup",
      "LocalizedInteriorColorGroup",
      "LocalizedModelCategory",
      "LocalizedModelGeneration",
      "LocalizedValue"
    ],
    "ImportDealerResponse": [
      "ImportDealerFail",
      "ImportDealerSuccess"
    ],
    "ImportInventoryResponse": [
      "ImportInventoryFail",
      "ImportInventorySuccess"
    ],
    "InsuranceProduct": [
      "Eazy",
      "ErgoLookupTable"
    ],
    "InsurerIntegration": [
      "EazyInsurerIntegration",
      "EmailInsurerIntegration"
    ],
    "InterestRateSettings": [
      "InterestRateFixedSettings",
      "InterestRateRangeSettings",
      "InterestRateTableSettings"
    ],
    "Inventory": [
      "ConfiguratorInventory",
      "MobilityInventory"
    ],
    "Layout": [
      "BasicLayout",
      "BasicProLayout",
      "PorscheV3Layout"
    ],
    "Lead": [
      "ConfiguratorLead",
      "EventLead",
      "FinderLead",
      "LaunchpadLead",
      "MobilityLead",
      "StandardLead"
    ],
    "LiveChatSetting": [
      "UserlikeChatbotSetting",
      "WhatsappLiveChatSetting"
    ],
    "LocalCustomerField": [
      "LocalCustomerArrayStringField",
      "LocalCustomerDateField",
      "LocalCustomerDrivingLicenseField",
      "LocalCustomerNumberField",
      "LocalCustomerPhoneField",
      "LocalCustomerReferenceDetailSetField",
      "LocalCustomerSalaryTransferredBankSetField",
      "LocalCustomerStringDescriptionField",
      "LocalCustomerStringField",
      "LocalCustomerUAEIdentitySetField",
      "LocalCustomerUploadsField",
      "LocalCustomerVerifiedPhoneField"
    ],
    "MenuItem": [
      "MenuCustomPathItem",
      "MenuEndpointItem",
      "MenuLogoutActionItem"
    ],
    "Mobility": [
      "MobilityAdditionalInfo",
      "MobilityAddon"
    ],
    "MobilityBookingLocation": [
      "MobilityBookingLocationHome",
      "MobilityBookingLocationPickup"
    ],
    "MobilitySnapshot": [
      "MobilityAdditionalInfoSnapshot",
      "MobilityAddonSnapshot"
    ],
    "Module": [
      "AdyenPaymentModule",
      "AppointmentModule",
      "AutoplayModule",
      "BankModule",
      "BasicSigningModule",
      "CapModule",
      "ConfiguratorModule",
      "ConsentsAndDeclarationsModule",
      "CtsModule",
      "DocusignModule",
      "EventApplicationModule",
      "FinderApplicationPrivateModule",
      "FinderApplicationPublicModule",
      "FinderVehicleManagementModule",
      "FiservPaymentModule",
      "GiftVoucherModule",
      "InsuranceModule",
      "LabelsModule",
      "LaunchPadModule",
      "LocalCustomerManagementModule",
      "MaintenanceModule",
      "MarketingModule",
      "MobilityModule",
      "MyInfoModule",
      "NamirialSigningModule",
      "OIDCModule",
      "PayGatePaymentModule",
      "PorscheIdModule",
      "PorscheMasterDataModule",
      "PorschePaymentModule",
      "PorscheRetainModule",
      "PromoCodeModule",
      "SalesControlBoardModule",
      "SalesOfferModule",
      "SimpleVehicleManagementModule",
      "StandardApplicationModule",
      "TradeInModule",
      "TtbPaymentModule",
      "UserlikeChatbotModule",
      "VehicleDataWithPorscheCodeIntegrationModule",
      "VisitAppointmentModule",
      "WebsiteModule",
      "WhatsappLiveChatModule"
    ],
    "OptionSettings": [
      "ComboOptionSettings",
      "DropdownOptionSettings",
      "MultiSelectOptionSettings",
      "SingleSelectOptionSettings"
    ],
    "PackageType": [
      "PackageTypeWithDescription",
      "PackageTypeWithPrice"
    ],
    "PaymentSetting": [
      "AdyenPaymentSetting",
      "FiservPaymentSetting",
      "PayGatePaymentSetting",
      "PorschePaymentSetting",
      "TtbPaymentSetting"
    ],
    "PromoType": [
      "DiscountPromoType",
      "GiftPromoType"
    ],
    "SalesOfferFeatureConfigurationCore": [
      "DepositSalesOffer",
      "FinanceSalesOffer",
      "InsuranceSalesOffer",
      "MainDetailsSalesOffer",
      "TradeInSalesOffer",
      "VSASalesOffer",
      "VehicleSalesOffer"
    ],
    "SmsSettings": [
      "SystemSmsSettings",
      "TwilioSmsSettings"
    ],
    "StockInventory": [
      "ConfiguratorStockInventory",
      "MobilityStockInventory"
    ],
    "SystemMessage": [
      "MaintenanceUpdate",
      "MessageNotice",
      "UserSessionRevoked"
    ],
    "TermSettings": [
      "DeferredPrincipalTermSettings",
      "GenericPrincipalTermSettings"
    ],
    "TradeIn": [],
    "UploadFileInterface": [
      "ApplicationDocument",
      "SalesOfferDocument",
      "UploadedFile",
      "UploadedFileWithPreview"
    ],
    "Vehicle": [
      "FinderVehicle",
      "LocalMake",
      "LocalModel",
      "LocalVariant"
    ],
    "WebPageBlock": [
      "ColumnWebPageBlock",
      "CustomWebPageBlock",
      "ImageWebPageBlock",
      "TextCarouselWebPageBlock",
      "TextImageWebPageBlock"
    ]
  }
};
      export default result;
    