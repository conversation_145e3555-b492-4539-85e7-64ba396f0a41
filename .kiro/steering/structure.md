# Project Structure

## Root Level Organization
```
├── src/                    # Application source code
├── schema/                 # GraphQL schema definitions
├── devtools/               # Build tools and development utilities
├── cypress/                # End-to-end testing
├── docs/                   # Project documentation
├── public/                 # Static assets
├── charts/                 # Helm deployment charts
└── terraform/              # Infrastructure as code
```

## Source Code Structure (`src/`)

### Frontend (`src/app/`)
- **components/**: Reusable React components
- **pages/**: Route-specific page components
- **layouts/**: Page layout components
- **routers/**: Client-side routing configuration
- **themes/**: Styling and theme definitions
- **utilities/**: Frontend utility functions
- **constants/**: Frontend constants and enums
- **types/**: Frontend-specific TypeScript types

### Backend (`src/server/`)
- **schema/**: GraphQL resolvers and type definitions
- **database/**: Database models and connections
- **services/**: Business logic services
- **integrations/**: External API integrations
- **queues/**: Background job processing
- **emails/**: Email template and sending logic
- **permissions/**: Authorization and access control
- **utils/**: Server utility functions

### Shared (`src/shared/`)
- **utils/**: Utilities used by both frontend and backend
- **constants.ts**: Application-wide constants
- **permissions.ts**: Shared permission definitions

### Testing (`src/__tests__/`)
- **unit/**: Unit tests
- **api/**: API integration tests
- **helpers/**: Test utilities and helpers
- **fixtures/**: Test data and mock objects

## GraphQL Schema (`schema/`)
Organized by domain/feature:
- Each domain has its own folder (e.g., `applications/`, `vehicles/`, `dealers/`)
- Contains `Query.graphql`, `Mutation.graphql`, and type definitions
- Shared types in `types/` and `common/`
- Enums and inputs organized in respective subfolders

## Development Tools (`devtools/`)
- **commands/**: CLI commands for build, dev, etc.
- **backend/**: Backend build configuration
- **frontend/**: Frontend build configuration
- **core/**: Shared build utilities

## Key Configuration Files
- **package.json**: Dependencies and scripts
- **tsconfig.json**: TypeScript configuration
- **.eslintrc.js**: Linting rules (Airbnb + TypeScript)
- **jest.config.ts**: Testing configuration
- **docker-compose.yml**: Local development services
- **.graphqlrc.json**: GraphQL tooling configuration

## Naming Conventions
- **Files**: camelCase for TypeScript/JavaScript, kebab-case for configs
- **Components**: PascalCase React components
- **Directories**: camelCase for code, kebab-case for configs
- **GraphQL**: PascalCase for types, camelCase for fields
- **Database**: camelCase collections and fields

## TypeScript Rules
- Strict Typing: Use TypeScript's strict mode as enforced in tsconfig.json.
- No any Type: Never use the any type. Always identify and use existing types, interfaces, or type aliases from the codebase (e.g., src/app/types/, src/shared/, or generated GraphQL types in schema/). If no suitable type exists, create a specific type or interface, or use alternative approaches like generics, unknown, or type assertions with validation.
- Type Imports: Use import type for type-only imports to optimize compilation.
- Type Safety: Ensure all functions, components, and variables have explicit types or interfaces.

## Import Organization
- External libraries first
- Internal imports grouped by: shared, server, app
- Relative imports last
- Assets (CSS, images) imported last