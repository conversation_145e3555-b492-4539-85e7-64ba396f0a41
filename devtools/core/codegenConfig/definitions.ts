import { CodegenConfig } from '@graphql-codegen/cli';
import { TypeScriptPluginConfig } from '@graphql-codegen/typescript';
import { TypeScriptResolversPluginConfig } from '@graphql-codegen/typescript-resolvers';

type ConfigOutput = CodegenConfig['generates'][number];

const pluginConfig: TypeScriptPluginConfig & TypeScriptResolversPluginConfig = {
    contextType: '../context#Context',
    rootValueType: '../context#RootDocument',
    noSchemaStitching: true,
    skipTypename: true,
    typesPrefix: 'GraphQL',
    mappers: {
        // Routers
        Router: '../../database#Router',
        PathScript: '../../database#PathScript',

        // Layouts
        Layout: '../../database#Layout',
        BasicLayout: '../../database#BasicLayout',
        BasicProLayout: '../../database#BasicProLayout',
        PorscheV3Layout: '../../database#PorscheV3Layout',

        // Menu items
        MenuItem: '../../database#MenuItem',
        MenuEndpointItem: '../../database#MenuEndpointItem',
        MenuCustomPathItem: '../../database#MenuCustomPathItem',
        MenuLogoutActionItem: '../../database#MenuLogoutActionItem',

        // Endpoints
        Endpoint: '../../database#Endpoint',
        DummyWelcomePageEndpoint: '../../database#DummyWelcomePageEndpoint',
        DummyPrivatePageEndpoint: '../../database#DummyPrivatePageEndpoint',
        StandardApplicationEntrypoint: '../../database#StandardApplicationEntrypoint',
        ApplicationListEndpoint: '../../database#ApplicationListEndpoint',
        EventApplicationEntrypoint: '../../database#EventApplicationEntrypoint',
        LaunchPadApplicationEntrypoint: '../../database#LaunchPadApplicationEntrypoint',
        ConfiguratorApplicationEntrypoint: '../../database#ConfiguratorApplicationEntrypoint',
        CustomerListEndpoint: '../../database#CustomerListEndpoint',
        MobilityApplicationEntrypoint: '../../database#MobilityApplicationEntrypoint',
        WebPageEndpoint: '../../database#WebPageEndpoint',
        StandardApplicationPublicAccessEntrypoint: '../../database#StandardApplicationPublicAccessEntrypoint',
        FinderApplicationPublicAccessEntrypoint: '../../database#FinderApplicationPublicAccessEntrypoint',
        FinderApplicationEntrypoint: '../../database#FinderApplicationEntrypoint',

        // Modules
        Module: '../../database#Module',
        ConsentsAndDeclarationsModule: '../../database#ConsentsAndDeclarationsModule',
        SimpleVehicleManagementModule: '../../database#SimpleVehicleManagementModule',
        LocalCustomerManagementModule: '../../database#LocalCustomerManagementModule',
        BankModule: '../../database#BankModule',
        BasicSigningModule: '../../database#BasicSigningModule',
        NamirialSigningModule: '../../database#NamirialSigningModule',
        StandardApplicationModule: '../../database#StandardApplicationModule',
        EventApplicationModule: '../../database#EventApplicationModule',
        AdyenPaymentModule: '../../database#AdyenPaymentModule',
        PorschePaymentModule: '../../database#PorschePaymentModule',
        FiservPaymentModule: '../../database#FiservPaymentModule',
        PayGatePaymentModule: '../../database#PayGatePaymentModule',
        TtbPaymentModule: '../../database#TtbPaymentModule',
        MyInfoModule: '../../database#MyInfoModule',
        ConfiguratorModule: '../../database#ConfiguratorModule',
        WhatsappLiveChatModule: '../../database#WhatsappLiveChatModule',
        UserlikeChatbotModule: '../../database#UserlikeChatbotModule',
        PromoCodeModule: '../../database#PromoCodeModule',
        MaintenanceModule: '../../database#MaintenanceModule',
        WebsiteModule: '../../database#WebsiteModule',
        MobilityModule: '../../database#MobilityModule',
        MobilityEmailScenarioContent: '../../database#MobilityEmailScenarioContent',
        LabelsModule: '../../database#LabelsModule',
        FinderVehicleManagementModule: '../../database#FinderVehicleManagementModule',
        FinderApplicationPublicModule: '../../database#FinderApplicationPublicModule',
        FinderApplicationPrivateModule: '../../database#FinderApplicationPrivateModule',
        AutoplayModule: '../../database#AutoplayModule',
        CtsModule: '../../database#CtsModule',
        PorscheMasterDataModule: '../../database#PorscheMasterDataModule',
        PorscheMasterDataSetting: '../../database#PorscheMasterDataSetting',
        AppointmentModule: '../../database#AppointmentModule',
        InsuranceModule: '../../database#InsuranceModule',
        GiftVoucherModule: '../../database#GiftVoucherModule',
        TradeInModule: '../../database#TradeInModule',
        CapModule: '../../database#CapModule',
        PorscheIdModule: '../../database#PorscheIdModule',
        PorscheRetainModule: '../../database#PorscheRetainModule',
        DocusignModule: '../../database#DocusignModule',
        LaunchPadModule: '../../database#LaunchPadModule',
        VisitAppointmentModule: '../../database#VisitAppointmentModule',
        MarketingModule: '../../database#MarketingModule',
        SalesOfferModule: '../../database#SalesOfferModule',
        SalesControlBoardModule: '../../database#SalesControlBoardModule',
        VehicleDataWithPorscheCodeIntegrationModule: '../../database#VehicleDataWithPorscheCodeIntegrationModule',

        // Company
        Company: '../../database#Company',
        CompanyOptions: '../../database#Company',
        EdmEmailSocialMedia: '../../database#EdmEmailSocialMedia',
        SMTPEmailSettings: '../../database#SMTPEmailSettings',
        TwilioSmsSettings: '../../database#TwilioSmsSettings',

        // Consents and Declarations
        ConsentsAndDeclarations: '../../database#ConsentsAndDeclarations',
        TextConsentsAndDeclarations: '../../database#TextConsentsAndDeclarations',
        CheckboxConsentsAndDeclarations: '../../database#CheckboxConsentsAndDeclarations',
        MarketingConsentsAndDeclarations: '../../database#MarketingConsentsAndDeclarations',
        ApplicationAgreement: './typings#ApplicationAgreement',
        TextApplicationAgreement: './typings#TextApplicationAgreement',
        CheckboxApplicationAgreement: './typings#CheckboxApplicationAgreement',
        MarketingApplicationAgreement: './typings#MarketingApplicationAgreement',
        Condition: '../../database#Condition',
        LogicCondition: '../../database#LogicCondition',
        ApplicationModuleCondition: '../../database#ApplicationModuleCondition',
        BankCondition: '../../database#BankCondition',
        DealerCondition: '../../database#DealerCondition',
        InsurerCondition: '../../database#InsurerCondition',
        ContextualCondition: '../../database#ContextualCondition',
        GiftVoucherCondition: '../../database#GiftVoucherCondition',

        // Users
        User: '../../database#User',
        UserSession: '../../database#UserSession',
        Role: '../../database#Role',
        Permission: '../../database#Permission',
        UserGroup: '../../database#UserGroup',

        // System
        SystemMessage: '../../utils/systemMessage#SystemMessage',
        LanguagePack: '../../database#LanguagePack',
        TranslatedString: '../../database#TranslatedString',

        // Authentication
        AuthenticationResponse: './typings#AuthenticationResponse',

        // Links
        ExternalLink: '../../database#ExternalLink',
        ResetPasswordLink: '../../database#ResetPasswordLink',
        CreateNewUserLink: '../../database#CreateNewUserLink',
        MyInfoCallbackLink: '../../database#MyInfoCallbackLink',
        NamirialSigningLink: '../../database#NamirialSigningLink',
        ConfiguratorApplicationLink: '../../database#ConfiguratorApplicationLink',
        VerifyEmailUpdateLink: '../../database#VerifyEmailUpdateLink',
        ProceedWithCustomerLink: '../../database#ProceedWithCustomerLink',
        StandardApplicationLink: '../../database#StandardApplicationLink',
        EventApplicationLink: '../../database#EventApplicationLink',
        FinderApplicationLink: '../../database#FinderApplicationLink',
        MobilityApplicationAmendmentLink: '../../database#MobilityApplicationAmendmentLink',
        MobilityApplicationCancellationLink: '../../database#MobilityApplicationCancellationLink',
        AdyenRedirectionLink: '../../database#AdyenRedirectionLink',
        PorschePaymentRedirectionLink: '../../database#PorschePaymentRedirectionLink',
        FiservPaymentRedirectionLink: '../../database#FiservPaymentRedirectionLink',
        PayGatePaymentRedirectionLink: '../../database#PayGatePaymentRedirectionLink',
        TtbPaymentRedirectionLink: '../../database#TtbPaymentRedirectionLink',
        CTSFinderRedirectionLink: '../../database#CTSFinderRedirectionLink',
        TestDriveProcessRedirectionLink: '../../database#TestDriveProcessRedirectionLink',
        GiftVoucherAdyenRedirectionLink: '../../database#GiftVoucherAdyenRedirectionLink',
        GiftVoucherPorschePaymentRedirectionLink: '../../database#GiftVoucherPorschePaymentRedirectionLink',
        GiftVoucherFiservPaymentRedirectionLink: '../../database#GiftVoucherFiservPaymentRedirectionLink',
        GiftVoucherPayGatePaymentRedirectionLink: '../../database#GiftVoucherPayGatePaymentRedirectionLink',
        GiftVoucherTtbPaymentRedirectionLink: '../../database#GiftVoucherTtbPaymentRedirectionLink',
        ApplyNewRedirectionLink: '../../database#ApplyNewRedirectionLink',
        PorscheIdCallbackLink: '../../database#PorscheIdCallbackLink',
        SendSalesOfferLink: '../../database#SendSalesOfferLink',
        SalesOfferNamirialSigningLink: '../../database#SalesOfferNamirialSigningLink',

        // Common
        UploadedFile: '../../database#UploadedFile',
        UploadedFileWithPreview: '../../database#UploadedFileWithPreview',

        // Author
        Author: './typings#Author',

        // Bank
        Bank: '../../database#Bank',
        SystemBank: '../../database#SystemBank',
        ExternalBank: '../../database#ExternalBank',
        BankOption: '../../database#SystemBank',

        // Bank Integrations
        BankIntegration: '../../database#BankIntegration',
        EmailBankIntegration: '../../database#EmailBankIntegration',
        HlfBankIntegration: '../../database#HlfBankIntegration',
        HlfBankV2Integration: '../../database#HlfBankV2Integration',
        UobBankIntegration: '../../database#UobBankIntegration',
        DbsBankIntegration: '../../database#DbsBankIntegration',
        MaybankIntegration: '../../database#MaybankIntegration',
        EnbdBankIntegration: '../../database#EnbdBankIntegration',

        // SDM Integration
        Authorization: '../../database#Authorization',

        // Event
        Event: '../../database#Event',
        CalendarEvent: '../../database#CalendarEvent',
        CustomizedField: '../../database#CustomizedField',

        // Versionings
        SimpleVersioning: '../../database#SimpleVersioning',
        AdvancedVersioning: '../../database#AdvancedVersioning',

        // Vehicle
        Vehicle: '../../database#Vehicle',
        LocalVariant: '../../database#LocalVariant',
        LocalModel: '../../database#LocalModel',
        LocalMake: '../../database#LocalMake',

        // Finance Product
        FinanceProduct: '../../database#FinanceProduct',
        LocalHirePurchase: '../../database#LocalFinanceProduct',
        LocalHirePurchaseWithBalloon: '../../database#LocalFinanceProduct',
        LocalHirePurchaseWithBalloonGFV: '../../database#LocalFinanceProduct',
        LocalLease: '../../database#LocalFinanceProduct',
        LocalLeasePurchase: '../../database#LocalFinanceProduct',
        LocalDeferredPrincipal: '../../database#LocalFinanceProduct',
        LocalUcclLeasing: '../../database#LocalFinanceProduct',

        // Remote FP Settings
        WebCalcSetting: '../../database#WebCalcSetting',

        // Customer
        Customer: '../../database#Customer',
        LocalCustomer: '../../database#LocalCustomer',
        CorporateCustomer: '../../database#CorporateCustomer',
        Guarantor: '../../database#Guarantor',

        LocalCustomerField: '../../database#LocalCustomerField',
        LocalCustomerStringField: '../../database#LocalCustomerField',
        LocalCustomerArrayStringField: '../../database#LocalCustomerField',
        LocalCustomerNumberField: '../../database#LocalCustomerField',
        LocalCustomerDateField: '../../database#LocalCustomerField',
        LocalCustomerPhoneField: '../../database#LocalCustomerField',
        LocalCustomerDrivingLicenseField: '../../database#LocalCustomerField',
        LocalCustomerStringDescriptionField: '../../database#LocalCustomerField',
        LocalCustomerUAEIdentitySetField: '../../database#LocalCustomerField',
        LocalCustomerReferenceDetailSetField: '../../database#LocalCustomerField',
        LocalCustomerSalaryTransferredBankSetField: '../../database#LocalCustomerField',

        KYCPreset: '../../database#KYCPreset',

        // Applications
        Application: '../../database#Application',
        ApplicationStageDetails: './typings#ApplicationStageDetails',
        StandardApplication: '../../database#StandardApplication',
        EventApplication: '../../database#EventApplication',
        ConfiguratorApplication: '../../database#ConfiguratorApplication',
        MobilityApplication: '../../database#MobilityApplication',
        MobilityBookingLocation: '../../database#MobilityBookingLocation',
        MobilityBookingLocationPickup: '../../database#MobilityBookingLocationPickup',
        MobilityBookingLocationHome: '../../database#MobilityBookingLocationHome',
        FinderApplication: '../../database#FinderApplication',
        LaunchpadApplication: '../../database#LaunchpadApplication',
        SalesOfferApplication: '../../database#SalesOfferApplication',
        CalculatorResult: './typings#CalculatorResult',
        AdyenPaymentSetting: '../../database#AdyenPaymentSetting',
        PorschePaymentSetting: '../../database#PorschePaymentSetting',
        FiservPaymentSetting: '../../database#FiservPaymentSetting',
        PayGatePaymentSetting: '../../database#PayGatePaymentSetting',
        TtbPaymentSetting: '../../database#TtbPaymentSetting',
        AuditTrail: '../../database#AuditTrail',
        MyInfoSetting: '../../database#MyInfoSetting',
        NamirialSetting: '../../database#NamirialSetting',
        DealerVehicles: '../../database#DealerVehicles',
        DealerFinanceProducts: '../../database#DealerFinanceProducts',
        Dealer: '../../database#Dealer',
        ImportDealerResponse: './typings#ImportDealerResponse',
        ListDealerOption: '../../database#Dealer',
        DealerSocialMedia: '../../database#DealerSocialMedia',
        Configurator: '../../database#Configurator',
        ModelConfigurator: '../../database#ModelConfigurator',
        VariantConfigurator: '../../database#VariantConfigurator',
        ColorAndTrimSettings: '../../database#ColorAndTrimSettings',
        OptionSettings: '../../database#OptionSettings',
        PackageType: '../../database#PackageType',
        ColorBlock: '../../database#ColorBlock',
        TrimBlock: '../../database#TrimBlock',
        PackageBlock: '../../database#PackageBlock',
        OptionsBlock: '../../database#OptionsBlock',
        PackageSettings: '../../database#PackageSettings',
        AdditionalDetails: '../../database#AdditionalDetail',
        Block: '../../database#Block',
        Matrix: '../../database#Matrix',
        SingleSelectOptionSettings: '../../database#SingleSelectOptionSettings',
        SingleSelectOption: '../../database#SingleSelectOption',
        MultiSelectOptionSettings: '../../database#MultiSelectOptionSettings',
        ComboOptionSettings: '../../database#ComboOptionSettings',
        DropdownOptionSettings: '../../database#DropdownOptionSettings',
        Option: '../../database#Option',
        ComboOption: '../../database#ComboOption',
        ApplicationConfiguratorBlock: '../../database#ApplicationConfiguratorBlock',
        ApplicationConfiguratorColorSetting: '../../database#ApplicationConfiguratorColorSetting',
        ApplicationConfiguratorOptionSetting: '../../database#ApplicationConfiguratorOptionSetting',
        ApplicationConfiguratorPackageSetting: '../../database#ApplicationConfiguratorPackageSetting',
        ApplicationConfiguratorTrimSetting: '../../database#ApplicationConfiguratorTrimSetting',
        ApplicationOptionSettings: '../../database#ApplicationOptionSettings',
        ApplicationComboSettings: '../../database#ApplicationComboSettings',
        ApplicationComboOption: '../../database#ApplicationComboOption',
        DealershipSetting: '../../database#DealershipSetting',
        DealershipPublicSalesPerson: '../../database#DealershipPublicSalesPerson',
        DealershipMyInfoSetting: '../../database#DealershipMyInfoSetting',
        DealershipPaymentSetting: '../../database#DealershipPaymentSetting',
        WhatsappLiveChatSetting: '../../database#WhatsappLiveChatSetting',
        UserlikeChatbotSetting: '../../database#UserlikeChatbotSetting',
        PromoCode: '../../database#PromoCode',
        GiftPromoType: '../../database#GiftPromoType',
        DiscountPromoType: '../../database#DiscountPromoType',
        Inventory: '../../database#Inventory',
        ConfiguratorInventory: '../../database#ConfiguratorInventory',
        MobilityInventory: '../../database#MobilityInventory',
        StockInventory: '../../database#StockInventory',
        ConfiguratorStockInventory: '../../database#ConfiguratorStockInventory',
        MobilityStockInventory: '../../database#MobilityStockInventory',
        ImportInventoryResponse: './typings#ImportInventoryResponse',
        MobilitySnapshot: '../../database#MobilitySnapshot',
        TradeInSetting: '../../database#TradeInSetting',
        TradeIn: '../../database#TradeIn',
        VehicleDataWithPorscheCodeIntegrationSetting: '../../database#VehicleDataWithPorscheCodeIntegrationSetting',

        // Lead
        Lead: '../../database#Lead',
        ConfiguratorLead: '../../database#ConfiguratorLead',
        EventLead: '../../database#EventLead',
        FinderLead: '../../database#FinderLead',
        LaunchpadLead: '../../database#LaunchpadLead',
        StandardLead: '../../database#StandardLead',
        MobilityLead: '../../database#MobilityLead',

        // Mobility
        Mobility: '../../database#Mobility',
        MobilityAddon: '../../database#MobilityAddon',
        MobilityAdditionalInfo: '../../database#MobilityAdditionalInfo',
        MobilityDetail: '../../database#MobilityDetail',
        MobilityOption: '../../database#MobilityOption',
        MobilityLocation: '../../database#MobilityLocation',
        MobilityHomeDelivery: '../../database#MobilityHomeDelivery',

        // Labels
        Labels: '../../database#Labels',
        ModuleVariant: '../../database#ModuleVariant',

        // Banner
        Banner: '../../database#Banner',

        // Mobility - WebPage
        WebPage: '../../database#WebPage',
        TextImageWebPageBlock: '../../database#TextImageWebPageBlock',
        CustomWebPageBlock: '../../database#CustomWebPageBlock',
        ImageWebPageBlock: '../../database#ImageWebPageBlock',
        ColumnWebPageBlock: '../../database#ColumnWebPageBlock',
        TextCarouselWebPageBlock: '../../database#TextCarouselWebPageBlock',
        ImageDescription: '../../database#ImageDescription',
        ColumnBlock: '../../database#ColumnBlock',

        // Finder Vehicle
        FinderVehicle: '../../database#FinderVehicle',
        FinderVehicleSetting: '../../database#FinderVehicleSetting',
        FinderVehicleManagementSetting: '../../database/#FinderVehicleManagementSetting',

        // Autoplay
        AutoplaySetting: '../../database#AutoplaySetting',

        // Insurer
        Insurer: '../../database#Insurer',

        // Insurance Product
        InsuranceProduct: '../../database#InsuranceProduct',
        Eazy: '../../database#InsuranceProduct',
        ErgoLookupTable: '../../database#InsuranceProduct',

        // CTS Setting
        CtsSetting: '../../database#CtsSetting',

        // Gift Voucher ( Gift codes )
        GiftVoucher: '../../database#GiftVoucher',
        GiftVoucherJourney: '../../database#GiftVoucherJourney',

        // stages
        FinancingStage: '../../database#FinancingStage',
        ReservationStage: '../../database#ReservationStage',
        LeadStage: '../../database#LeadStage',
        MobilityStage: '../../database#MobilityStage',
        AppointmentStage: '../../database#AppointmentStage',
        InsuranceStage: '../../database#InsuranceStage',
        VisitAppointmentStage: '../../database#VisitAppointmentStage',

        // C@P Metadata
        CapMetadata: '../../database#CapMetadata',
        CapVehicleMakeMetadata: '../../database#CapVehicleMakeMetadata',
        CapVehicleModelMetadata: '../../database#CapVehicleModelMetadata',

        // Docusign
        DocusignSetting: '../../database#DocusignSetting',

        // OIDC
        OIDCModule: '../../database#OIDCModule',
        OIDCClient: '../../database#OIDCClient',

        // SalesOffer
        SalesOffer: '../../database#SalesOffer',
    },
    enumValues: './enums',
    scalars: {
        DateTime: 'Date',
        ObjectID: 'mongodb#ObjectId',
        Upload: '../context#FileUploadPromise',
    },
};

const config: ConfigOutput = {
    plugins: [
        {
            add: {
                content: '/* eslint-disable */',
            },
        },
        'typescript',
        'typescript-resolvers',
    ],
    config: pluginConfig,
};

export default config;
