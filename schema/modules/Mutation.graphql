extend type Mutation {
    """
    Create simple vehicle management module
    """
    createSimpleVehicleManagementModule(companyId: ObjectID!, settings: SimpleVehicleManagementSettings!): Module!

    """
    Update simple vehicle management module
    """
    updateSimpleVehicleManagementModule(moduleId: ObjectID!, settings: SimpleVehicleManagementUpdateSettings!): Module

    """
    Create consent and declarations module
    """
    createConsentsAndDeclarationsModule(companyId: ObjectID!, settings: ConsentsAndDeclarationsModuleSettings!): Module!

    """
    Update consent and declarations module
    """
    updateConsentsAndDeclarationsModule(moduleId: ObjectID!, settings: ConsentsAndDeclarationsModuleSettings!): Module

    """
    Create local customer management module
    """
    createLocalCustomerManagement(companyId: ObjectID!, settings: LocalCustomerManagementSettings!): Module!

    """
    Update local customer management module
    """
    updateLocalCustomerManagement(moduleId: ObjectID!, settings: LocalCustomerManagementSettings!): Module

    """
    Update Local Customer Management Module KYC Fields
    """
    updateLocalCustomerManagementKYCFields(
        moduleId: ObjectID!
        kycFields: [LocalCustomerManagementModuleKycFieldSettings!]!
        extraSettings: LocalCustomerManagementKYCFieldsExtraConfigSettings!
    ): Module

    """
    Create bank module
    """
    createBankModule(companyId: ObjectID!, settings: BankModuleSettings!): Module!

    """
    Update bank module
    """
    updateBankModule(moduleId: ObjectID!, settings: BankModuleSettings!): Module

    """
    Create insurance module
    """
    createInsuranceModule(companyId: ObjectID!, settings: InsuranceModuleSettings!): Module!

    """
    Update insurance module
    """
    updateInsuranceModule(moduleId: ObjectID!, settings: InsuranceModuleSettings!): Module

    """
    Create standard application module
    """
    createStandardApplicationModule(companyId: ObjectID!, settings: StandardApplicationModuleInitialSettings!): Module!

    """
    Update standard application vehicle assignments
    """
    updateStandardApplicationVehicleAssignments(moduleId: ObjectID!, dealerVehicles: [DealerVehiclesPayload!]!): Module

    """
    Update Standard Application Module Email content
    """
    updateStandardApplicationModuleEmailContent(
        moduleId: ObjectID!
        settings: StandardApplicationModuleEmailContentsInput!
    ): Module

    """
    Upload Standard Application Module Asset
    """
    uploadStandardApplicationModuleAsset(
        moduleId: ObjectID!
        upload: Upload!
        type: StandardApplicationModuleAsset!
        dealerId: ObjectID
    ): Module

    """
    Delete Standard Application Module Asset
    """
    deleteStandardApplicationModuleAsset(
        moduleId: ObjectID!
        type: StandardApplicationModuleAsset!
        dealerId: ObjectID
    ): Module

    """
    Update application module settings by Dealer
    """
    updateApplicationDealershipAssignmentsByDealer(
        moduleId: ObjectID!
        dealerId: ObjectID
        dealerVehicles: DealerVehiclesPayload
        dealerFinanceProducts: DealerFinanceProductsPayload
        dealerAssignee: DealershipSettingOverridesInput
        dealerPriceDisclaimer: DealerDisclaimersOverridesConfiguratorInput
        dealerFinancingDisclaimer: DealerDisclaimersOverridesConfiguratorInput
        dealerRentalDisclaimer: DealerTranslatedStringSettingOverridesInput
        dealerRentalRequirement: DealerTranslatedStringSettingOverridesInput
        dealerBookingCode: DealerBookingCodeSettingOverrideInput
        dealerMarketType: DealerApplicationMarketTypeInput
        dealerReservationInstructions: DealerTranslatedStringSettingOverridesInput
        dealerTermsTitle: DealerTranslatedStringSettingOverridesInput
        dealerTermsText: DealerTranslatedStringSettingOverridesInput
        dealerInsuranceProducts: DealerInsuranceProductsPayload
        # sales offer setting
        vsaTerms: DealerTranslatedStringSettingOverridesInput
        vsaSigningInstructions: DealerTranslatedStringSettingOverridesInput
        specificationTerms: DealerTranslatedStringSettingOverridesInput
        coeBiddingTerms: DealerTranslatedStringSettingOverridesInput
    ): Module

    """
    Update standard application finance product assignments
    """
    updateStandardApplicationFinanceProductAssignments(
        moduleId: ObjectID!
        dealerFinanceProducts: [DealerFinanceProductsPayload!]!
    ): Module

    """
    Create event application module
    """
    createEventApplicationModule(companyId: ObjectID!, settings: EventApplicationModuleInitialSettings!): Module!

    """
    Update event application vehicle assignments
    """
    updateEventApplicationVehicleAssignments(moduleId: ObjectID!, dealerVehicles: [DealerVehiclesPayload!]!): Module

    """
    Update event application finance product assignments
    """
    updateEventApplicationFinanceProductAssignments(
        moduleId: ObjectID!
        dealerFinanceProducts: [DealerFinanceProductsPayload!]!
    ): Module

    """
    Update event application module email contents
    """
    updateEventApplicationModuleEmailContents(
        moduleId: ObjectID!
        settings: EventApplicationModuleEmailContentsSetting!
    ): Module

    """
    Update event application module Main Details
    """
    updateEventApplicationModuleMainDetails(
        moduleId: ObjectID!
        settings: EventApplicationModuleUpdateSettings!
    ): Module

    """
    Upload event application module Assets
    """
    uploadEventApplicationModuleAsset(
        id: ObjectID!
        upload: Upload!
        type: EventModuleAsset!
        emailContentUpdateType: EmailContentUpdateType!
        dealerId: ObjectID
        scenarios: [ApplicationScenario!]!
    ): Module

    """
    Delete event application module Assets
    """
    deleteEventApplicationModuleAsset(
        id: ObjectID!
        type: EventModuleAsset!
        emailContentUpdateType: EmailContentUpdateType!
        dealerId: ObjectID
        scenarios: [ApplicationScenario!]!
    ): Module

    """
    Create basic signing module
    """
    createBasicSigningModule(companyId: ObjectID!, settings: BasicSigningModuleSettings!): Module!

    """
    Update basic signing module
    """
    updateBasicSigningModule(moduleId: ObjectID!, settings: BasicSigningModuleSettings!): Module

    """
    Create Namirial Signing module
    """
    createNamirialSigningModule(
        companyId: ObjectID!
        settings: NamirialSigningModuleSettings!
        secrets: NamirialSettingsInput
    ): Module!

    """
    Update Namirial Signing module
    """
    updateNamirialSigningModule(moduleId: ObjectID!, settings: NamirialSigningModuleSettings!): Module

    """
    Update Namirial Signing Settings / Secrets
    """
    updateNamirialSigningSettings(moduleId: ObjectID!, settings: NamirialSettingsInput!): Module!

    """
    Create Adyen payment module
    """
    createAdyenPaymentModule(companyId: ObjectID!, settings: AdyenPaymentModuleSettings!): Module!

    """
    Update Adyen payment module
    """
    updateAdyenPaymentModule(moduleId: ObjectID!, settings: AdyenPaymentModuleSettings!): Module

    """
    Create Porsche payment module
    """
    createPorschePaymentModule(companyId: ObjectID!, settings: PorschePaymentModuleSettings!): Module!

    """
    Update Porsche payment module
    """
    updatePorschePaymentModule(moduleId: ObjectID!, settings: PorschePaymentModuleSettings!): Module

    """
    Create Fiserv payment module
    """
    createFiservPaymentModule(companyId: ObjectID!, settings: FiservPaymentModuleSettings!): Module!

    """
    Update Fiserv payment module
    """
    updateFiservPaymentModule(moduleId: ObjectID!, settings: FiservPaymentModuleSettings!): Module

    """
    Create PayGate payment module
    """
    createPayGatePaymentModule(companyId: ObjectID!, settings: PayGatePaymentModuleSettings!): Module!

    """
    Update PayGate payment module
    """
    updatePayGatePaymentModule(moduleId: ObjectID!, settings: PayGatePaymentModuleSettings!): Module

    """
    Create TTB payment module
    """
    createTtbPaymentModule(companyId: ObjectID!, settings: TtbPaymentModuleSettings!): Module!

    """
    Update TTB payment module
    """
    updateTtbPaymentModule(moduleId: ObjectID!, settings: TtbPaymentModuleSettings!): Module

    """
    Create MyInfo Module
    """
    createMyInfoModule(companyId: ObjectID!, settings: MyInfoModuleSettings!): Module!

    """
    Update MyInfo Module
    """
    updateMyInfoModule(moduleId: ObjectID!, settings: MyInfoModuleSettings!): Module

    """
    Create Configurator Module
    """
    createConfiguratorModule(companyId: ObjectID!, settings: ConfiguratorModuleInitialSettings!): Module!

    """
    Update Configurator Module
    """
    updateConfiguratorModule(moduleId: ObjectID!, settings: ConfiguratorModuleUpdateSettings!): Module

    """
    Update Configurator Module Email Contents
    """
    updateConfiguratorModuleEmailContents(
        moduleId: ObjectID!
        settings: ConfiguratorModuleEmailContentsSetting!
    ): Module

    """
    Upload Configurator Module Assets
    """
    uploadConfiguratorModuleAsset(
        moduleId: ObjectID!
        upload: Upload!
        type: ConfiguratorModuleAsset!
        emailContentUpdateType: EmailContentUpdateType!
        dealerId: ObjectID
    ): Module

    """
    Delete Configurator Module Assets
    """
    deleteConfiguratorModuleAsset(
        moduleId: ObjectID!
        type: ConfiguratorModuleAsset!
        emailContentUpdateType: EmailContentUpdateType!
        dealerId: ObjectID
    ): Module

    """
    Create Whatsapp Live Chat Module
    """
    createWhatsappLiveChatModule(companyId: ObjectID!, settings: WhatsappLiveChatModuleInput!): Module!

    """
    Update Whatsapp Live Chat Module
    """
    updateWhatsappLiveChatModule(moduleId: ObjectID!, settings: WhatsappLiveChatModuleInput!): Module!

    """
    Create Userlike Chatbot Module
    """
    createUserlikeChatbotModule(companyId: ObjectID!, settings: UserlikeChatbotModuleInput!): Module!

    """
    Update Userlike Chatbot Module
    """
    updateUserlikeChatbotModule(moduleId: ObjectID!, settings: UserlikeChatbotModuleInput!): Module!

    """
    Delete module
    """
    deleteModule(moduleId: ObjectID!): Boolean!

    """
    Update Dealership management for standard application vehicle, and FP assignment
    """
    updateStandardApplicationDealership(
        dealerId: ObjectID!
        applicationVehicles: [StandardApplicationVehiclePayload!]!
        applicationFinanceProducts: [StandardApplicationFinanceProductPayload!]!
    ): Module

    """
    Update Standard Application Module Main Details
    """
    updateStandardApplicationModuleMainDetails(
        moduleId: ObjectID!
        settings: StandardApplicationModuleUpdateSettings!
    ): Module

    """
    Create promo code module
    """
    createPromoCodeModule(companyId: ObjectID!, settings: PromoCodeModuleSettings!): Module!

    """
    Update promo code module
    """
    updatePromoCodeModule(moduleId: ObjectID!, settings: PromoCodeModuleSettings!): Module

    """
    Create Maintenance Module
    """
    createMaintenanceModule(companyId: ObjectID!, settings: MaintenanceModuleSettings!): Module!

    """
    Update Maintenance Module
    """
    updateMaintenanceModule(moduleId: ObjectID!, settings: MaintenanceModuleSettings!): Module

    """
    Update Maintenance Module Details
    """
    updateMaintenanceModuleDetails(moduleId: ObjectID!, settings: MaintenanceDetailsSettings!): Module

    """
    Upload MaintenanceModule Asset
    """
    uploadMaintenanceModuleAsset(moduleId: ObjectID!, upload: Upload!, type: ModuleAsset!): Module

    """
    Delete MaintenanceModule Asset
    """
    deleteMaintenanceModuleAsset(moduleId: ObjectID!, type: ModuleAsset!): Module

    """
    Create Labels Module
    """
    createLabelsModule(companyId: ObjectID!, settings: LabelsModuleSettings!): Module!

    """
    Update Labels Module
    """
    updateLabelsModule(moduleId: ObjectID!, settings: LabelsModuleSettings!): Module

    """
    Create Website Module
    """
    createWebsiteModule(companyId: ObjectID!, settings: CreateWebsiteModulePayload!): Module!

    """
    Update Website Module
    """
    updateWebsiteModule(moduleId: ObjectID!, settings: UpdateWebsiteModulePayload!): Module!

    """
    Create mobility module
    """
    createMobilityModule(companyId: ObjectID!, settings: MobilityModuleInitialSettings!): Module!

    """
    Update mobility module
    """
    updateMobilityModule(moduleId: ObjectID!, settings: MobilityModuleUpdateSettings!): Module

    """
    Update mobility module email content
    """
    updateMobilityEmailContent(
        moduleId: ObjectID
        emailContents: [MobilityEmailScenarioContentInput!]!
        emailContentUpdateType: EmailContentUpdateType!
        dealerId: ObjectID
    ): Module

    """
    Upload mobility module email asset
    """
    uploadMobilityModuleEmailAsset(
        emailContentId: ObjectID!
        moduleId: ObjectID!
        upload: Upload!
        mobilityRecipient: MobilityRecipient!
        mobilityAsset: MobilityAsset!
        assetType: MobilityEmailAssetType!
        emailContentUpdateType: EmailContentUpdateType!
        dealerId: ObjectID
    ): Module

    """
    delete mobility module email asset
    """
    deleteMobilityModuleEmailAsset(
        emailContentId: ObjectID!
        moduleId: ObjectID!
        mobilityRecipient: MobilityRecipient!
        mobilityAsset: MobilityAsset!
        assetType: MobilityEmailAssetType!
        emailContentUpdateType: EmailContentUpdateType!
        dealerId: ObjectID
    ): Module

    """
    Create Finder Vehicle Management module
    """
    createFinderVehicleManagementModule(companyId: ObjectID!, settings: FinderVehicleManagementModuleSetting!): Module!

    """
    Update finder vehicle management module
    """
    updateFinderVehicleManagementModule(moduleId: ObjectID!, settings: FinderVehicleManagementModuleSetting!): Module

    """
    Create Autoplay module
    """
    createAutoplayModule(companyId: ObjectID!, settings: AutoplayModulePayload!): Module!

    """
    Update Autoplay module
    """
    updateAutoplayModule(moduleId: ObjectID!, settings: AutoplayModulePayload!): Module!

    """
    Create Autoplay module setting
    """
    createAutoplayModuleSetting(moduleId: ObjectID!, settings: AutoplaySettingPayload!): Module!

    """
    Update Autoplay module setting
    """
    updateAutoplayModuleSetting(moduleId: ObjectID!, settingId: ObjectID, settings: AutoplaySettingPayload!): Module!

    """
    Delete Autoplay module setting
    """
    deleteAutoplayModuleSetting(moduleId: ObjectID!, settingId: ObjectID!): Module!

    """
    Create Finder Application Public Module
    """
    createFinderApplicationPublicModule(
        companyId: ObjectID!
        settings: FinderApplicationModuleInitialSettings!
    ): Module!

    """
    Update Finder Application Public Module
    """
    updateFinderApplicationPublicModule(moduleId: ObjectID!, settings: FinderApplicationModuleUpdateSettings!): Module

    """
    Create Finder Application Private Module
    """
    createFinderApplicationPrivateModule(
        companyId: ObjectID!
        settings: FinderApplicationPrivateModuleInitialSettings!
    ): Module!

    """
    Update Finder Application Private Module
    """
    updateFinderApplicationPrivateModule(
        moduleId: ObjectID!
        settings: FinderApplicationPrivateModuleUpdateSettings!
    ): Module

    """
    Update Finder Application Public Module Email Contents
    """
    updateFinderApplicationPublicModuleEmailContents(
        moduleId: ObjectID!
        settings: FinderApplicationModuleEmailContentsSetting!
    ): Module

    """
    Update Finder Application Private Module Email Contents
    """
    updateFinderApplicationPrivateModuleEmailContents(
        moduleId: ObjectID!
        settings: FinderApplicationModuleEmailContentsSetting!
    ): Module

    """
    Upload Finder Application Module Assets
    """
    uploadFinderApplicationModuleAsset(
        moduleId: ObjectID!
        upload: Upload!
        type: FinderApplicationModuleAsset!
        emailContentUpdateType: EmailContentUpdateType!
        dealerId: ObjectID
        scenarios: [ApplicationScenario!]!
    ): Module

    """
    Delete Finder Application Module Assets
    """
    deleteFinderApplicationModuleAsset(
        moduleId: ObjectID!
        type: FinderApplicationModuleAsset!
        emailContentUpdateType: EmailContentUpdateType!
        dealerId: ObjectID
        scenarios: [ApplicationScenario!]!
    ): Module

    """
    Create CTS Module
    """
    createCtsModule(companyId: ObjectID!, settings: CtsModuleInitialSettings!): Module!

    """
    Update CTS Module
    """
    updateCtsModule(moduleId: ObjectID!, settings: CtsModuleUpdateSettings!): Module

    """
    Create CTS Module setting
    """
    createCtsModuleSetting(moduleId: ObjectID!, settings: CtsSettingInitialInput!): Module!

    """
    Update CTS Module setting
    """
    updateCtsModuleSetting(moduleId: ObjectID!, settingId: ObjectID!, settings: CtsSettingUpdateInput!): Module!

    """
    Delete CTS Module setting
    """
    deleteCtsModuleSetting(moduleId: ObjectID!, settingId: ObjectID!): Module!

    """
    Update CTS module FP assignments
    """
    updateCtsFinanceProducts(moduleId: ObjectID!, settingId: ObjectID!, financeProductSuiteIds: [ObjectID!]!): Module

    """
    Update CTS module Insurance Product assignments
    """
    updateCtsInsuranceProducts(
        moduleId: ObjectID!
        settingId: ObjectID!
        insuranceProductSuiteIds: [ObjectID!]!
    ): Module

    """
    Create Porsche MasterData Module
    """
    createPorscheMasterDataModule(companyId: ObjectID!, settings: PorscheMasterDataModuleSettings!): Module!

    """
    Update Porsche MasterData Module
    """
    updatePorscheMasterDataModule(moduleId: ObjectID!, settings: PorscheMasterDataModuleSettings!): Module

    """
    Create Appointment Module
    """
    createAppointmentModule(companyId: ObjectID!, settings: AppointmentModuleInitialSettings!): Module!

    """
    Update Appointment Module
    """
    updateAppointmentModule(moduleId: ObjectID!, settings: AppointmentModuleInitialSettings!): Module

    """
    Update Appointment Module Email content
    """
    updateAppointmentModuleEmailContent(moduleId: ObjectID!, settings: AppointmentModuleEmailContentsInput!): Module

    """
    Upload Appointment Module Asset
    """
    uploadAppointmentModuleAsset(moduleId: ObjectID!, upload: Upload!, type: AppointmentModuleAsset!): Module

    """
    Delete Appointment Module Asset
    """
    deleteAppointmentModuleAsset(moduleId: ObjectID!, type: AppointmentModuleAsset!): Module

    """
    Create Gift Voucher Module
    """
    createGiftVoucherModule(companyId: ObjectID!, settings: GiftVoucherModuleInitialSettings!): Module!

    """
    Update Gift Voucher Module
    """
    updateGiftVoucherModule(moduleId: ObjectID!, settings: GiftVoucherModuleUpdateSettings!): Module

    """
    Update Gift Voucher Module Email content
    """
    updateGiftVoucherModuleEmailContent(moduleId: ObjectID!, settings: GiftVoucherModuleEmailContentsInput!): Module

    """
    Upload Gift Voucher Module Asset
    """
    uploadGiftVoucherModuleAsset(moduleId: ObjectID!, upload: Upload!, type: GiftVoucherModuleAsset!): Module

    """
    Delete Gift Voucher Module Asset
    """
    deleteGiftVoucherModuleAsset(moduleId: ObjectID!, type: GiftVoucherModuleAsset!): Module

    """
    Create WebCalc Setting
    """
    createWebCalcSetting(moduleId: ObjectID!, setting: WebCalcSettingPayload!): WebCalcSetting

    """
    Update WebCalc Setting
    """
    updateWebCalcSetting(moduleId: ObjectID!, setting: WebCalcSettingPayload!): WebCalcSetting

    """
    Create Website social media
    """
    createWebsiteSocialMedia(moduleId: ObjectID!, socialMediaInput: EdmEmailSocialMediaPayload!): EdmEmailSocialMedia!

    """
    Update Website social media
    """
    updateWebsiteSocialMedia(socialMediaId: ObjectID!, socialMediaInput: EdmEmailSocialMediaPayload!): Module

    """
    Delete Website social media
    """
    deleteWebsiteSocialMedia(socialMediaId: ObjectID!): Module

    """
    Upload Website social media asset
    """
    uploadWebsiteSocialMediaAsset(socialMediaId: ObjectID!, upload: Upload!): Module

    """
    Delete Website email social media asset
    """
    deleteWebsiteSocialMediaAsset(socialMediaId: ObjectID!): Module

    """
    Create Trade In Module
    """
    createTradeInModule(companyId: ObjectID!, settings: TradeInModuleSettings!): Module!

    """
    Update Trade In Module
    """
    updateTradeInModule(moduleId: ObjectID!, settings: TradeInModuleSettings!): Module

    """
    Create C@P Module
    """
    createCapModule(companyId: ObjectID!, settings: CapModuleSettings!): Module!

    """
    Update C@P Module
    """
    updateCapModule(moduleId: ObjectID!, settings: CapModuleSettings!): Module

    """
    Create Porsche ID Module
    """
    createPorscheIdModule(companyId: ObjectID!, settings: PorscheIdModuleSettings!): Module!

    """
    Update Porsche ID Module
    """
    updatePorscheIdModule(moduleId: ObjectID!, settings: PorscheIdModuleSettings!): Module

    """
    Create Porsche Retain Module
    """
    createPorscheRetainModule(companyId: ObjectID!, settings: PorscheRetainModuleSettings!): Module!

    """
    Update Porsche Retain Module
    """
    updatePorscheRetainModule(moduleId: ObjectID!, settings: PorscheRetainModuleSettings!): Module

    """
    Create Docusign Module
    """
    createDocusignModule(
        companyId: ObjectID!
        settings: DocusignModuleInput!
        docusignSetting: DocusignSettingInput!
    ): Module!

    """
    Update Docusign Module
    """
    updateDocusignModule(
        moduleId: ObjectID!
        settings: DocusignModuleInput!
        docusignSetting: DocusignSettingInput!
    ): Module

    """
    Create Launch Pad Module
    """
    createLaunchPadModule(companyId: ObjectID!, settings: LaunchPadModuleInitialSettings!): Module!

    """
    Update Launch Pad Module
    """
    updateLaunchPadModule(moduleId: ObjectID!, settings: LaunchPadModuleUpdateSettings!): Module

    """
    Create Visit Appointment Module
    """
    createVisitAppointmentModule(companyId: ObjectID!, settings: VisitAppointmentModuleInitialSettings!): Module!

    """
    Update Visit Appointment Module
    """
    updateVisitAppointmentModule(moduleId: ObjectID!, settings: VisitAppointmentModuleInitialSettings!): Module

    """
    Update Visit Appointment Module Email content
    """
    updateVisitAppointmentModuleEmailContent(
        moduleId: ObjectID!
        settings: VisitAppointmentModuleEmailContentsInput!
    ): Module

    """
    Upload Visit Appointment Module Asset
    """
    uploadVisitAppointmentModuleAsset(moduleId: ObjectID!, upload: Upload!, type: VisitAppointmentModuleAsset!): Module

    """
    Delete Visit Appointment Module Asset
    """
    deleteVisitAppointmentModuleAsset(moduleId: ObjectID!, type: VisitAppointmentModuleAsset!): Module

    """
    Update LaunchPad Module vehicle assignments
    """
    updateLaunchPadModuleVehicleAssignments(
        moduleId: ObjectID!
        dealerId: ObjectID!
        dealerVehicles: [DealerVehiclesPayload!]!
    ): Module

    """
    Create Marketing Module
    """
    createMarketingModule(companyId: ObjectID!, settings: MarketingModuleInput!): Module!

    """
    Update Marketing Module
    """
    updateMarketingModule(moduleId: ObjectID!, settings: MarketingModuleInput!): Module

    """
    Create Sales Offer Module
    """
    createSalesOfferModule(companyId: ObjectID!, settings: SalesOfferModuleInitialSettings!): Module!

    """
    Update Sales Offer Module
    """
    updateSalesOfferModule(moduleId: ObjectID!, settings: SalesOfferModuleUpdateSettings!): Module

    """
    Create Vehicle Data With Porsche Code Integration Module
    """
    createVehicleDataWithPorscheCodeIntegrationModule(
        companyId: ObjectID!
        setting: VehicleDataWithPorscheCodeIntegrationSettingInput!
    ): Module!

    """
    Update Vehicle Data With Porsche Code Integration Module
    """
    updateVehicleDataWithPorscheCodeIntegrationModule(
        moduleId: ObjectID!
        setting: VehicleDataWithPorscheCodeIntegrationSettingInput!
    ): Module!

    """
    Update Sales Offer Module Email Content
    """
    updateSalesOfferModuleEmailContent(
        moduleId: ObjectID!
        settings: SalesOfferModuleDealerSpecificEmailContentInput!
    ): Module

    """
    Create Sales Control Board Module
    """
    createSalesControlBoardModule(companyId: ObjectID!, displayName: String!): Module!

    """
    Update Sales Control Board Module
    """
    updateSalesControlBoardModule(moduleId: ObjectID!, displayName: String!): Module

    """
    Update Sales Control Board Module by Dealer
    """
    updateSalesControlBoardModuleByDealer(
        moduleId: ObjectID!
        dealerId: ObjectID!
        testDriveMonthlyTarget: Int!
        orderIntakesMonthlyTarget: Int!
        retailsMonthlyTarget: Int!
        financeCommissionMonthlyTarget: Float!
        insuranceCommissionMonthlyTarget: Float!
        salesConsultantsAssignments: [ObjectID!]!
    ): Module
}
