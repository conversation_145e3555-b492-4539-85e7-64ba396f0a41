input ApplicationQuotationOptionSetting {
    amountIncludingVat: Float!
    description: String
    isVatIncluded: Boolean!
}

input EnbdApplicationQuotationSettings {
    applicantName: String!
    commissionNumber: String!
    engineNumber: String!
    chassisNumber: String!
    exteriorColor: String!
    options: [ApplicationQuotationOptionSetting!]!
    downPaymentTo: ApplicationQuotationDownPaymentTarget
    companyName: String!
    financeManagerName: String!
    vatRate: Float
}

input ApplicationQuotationSettings {
    source: ApplicationQuotationSource!
    enbd: EnbdApplicationQuotationSettings
}
