input TradeInVehiclePayload {
    source: LocalCustomerFieldSource!
    vehicleSource: CurrentVehicleSource
    ownership: Boolean
    make: String
    model: String
    equipmentLine: CurrentVehicleEquipmentLine
    modelYear: Int
    purchaseYear: Int
    engineType: EngineType
    registrationNumber: String
    mileage: Int
    yearOfManufacture: Int
    price: Float
    isSelected: Boolean!
    firstRegistrationDate: DateTime
    roadTaxExpiryDate: DateTime
    engineCapacity: Int
    propellant: String
    primaryColour: String
    secondaryColour: String
    status: String
    scheme: String
    coeCategory: String
    coeExpiryDate: DateTime
    quotaPremium: Float
    openMarketValue: Float
    noOfTransfers: Int
    vehicleContractEnd: DateTime
    potentialReplacement: DateTime
    vin: String

    transmission: String
    arf: Int
    chassisNumber: String
    engineNumber: String
    vehicleHandoverDate: DateTime

    ownerIdType: LTAPreOwnerIdType
    ownerId: String

    exteriorColor: String
    interiorColor: String
    parfEligible: Boolean
    softTopColor: String
    previousAccidentDamage: Boolean
    originalEngine: Boolean
    originalCondition: Boolean
    vehicleFreeFromThirdPartyClaims: Boolean
    deposit: Float
    remarks: String
}
