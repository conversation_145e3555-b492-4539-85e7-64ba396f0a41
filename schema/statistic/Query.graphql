extend type Query {
    """
    Get statistic for submitted applications or leads
    """
    getSubmittedApplicationPerDay(filter: StatisticFilterPayload): StatisticMonthlyResult

    """
    Get statistic for popular variant applications or leads
    """
    getPopularVariant(filter: StatisticFilterPayload): StatisticMonthlyResult

    """
    Get statistic for popular event applications or leads
    """
    getPopularEvent(filter: StatisticFilterPayload): StatisticMonthlyResult

    """
    Get statistic for deposit payment per day
    """
    getDepositPaymentPerDay(filter: StatisticFilterPayload): StatisticMonthlyResult

    """
    Get statistic for salesperson with most applications / leads
    """
    getTopSalesPersons(filter: StatisticFilterPayload, sortParam: String): StatisticMonthlyResult

    """
    Get statistic for application counter
    """
    getApplicationCounter(filter: StatisticFilterPayload): StatisticCounterResult

    """
    Get statistic for instant approval rate
    """
    getDbsInstantApprovalRate(filter: InstantApprovalStatisticFilterPayload): StatisticInstantApprovalResult

    """
    Get Marketing Dashboard result
    """
    getMarketingDashboard(filter: MarketingDashboardFilterPayload): MarketingDashboard

    """
    Marketing Dashboard Filters
    """
    getMarketingDashboardFilterOption(companyId: ObjectID!, period: PeriodPayload): MarketingDashboardFilterDropdown!

    """
    get MonthOfImportOption of sales control board
    """
    getMonthOfImportOptions(dealerId: ObjectID!): [MonthOfImportOption!]!

    """
    get sales control board filter options
    """
    getSalesControlBoardFilterOptions(dealerId: ObjectID!): SalesControlBoardFilterOptions!

    """
    get sales control board dashboard data
    """
    getSalesControlBoard(filter: SalesControlBoardFilterPayload!): Boolean!
    
}
