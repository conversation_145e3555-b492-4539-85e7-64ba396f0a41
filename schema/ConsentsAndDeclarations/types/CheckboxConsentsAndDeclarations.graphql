type CheckboxConsentsAndDeclarations implements ConsentsAndDeclarations {
    """
    UID
    """
    id: ObjectID!

    """
    C&D type
    """
    type: ConsentsAndDeclarationsType!

    """
    Module ID
    """
    moduleId: ObjectID!

    """
    Module
    """
    module: Module!

    """
    Display Name
    """
    displayName: String!

    """
    Is deleted
    """
    isDeleted: Boolean!

    """
    Versioning
    """
    versioning: AdvancedVersioning!

    """
    Title
    """
    title: TranslatedString

    """
    Description
    """
    description: TranslatedString

    """
    Order Number
    """
    orderNumber: Int!

    """
    Mandatory
    """
    isMandatory: Boolean!

    """
    Is active
    """
    isActive: Boolean!

    """
    Has legal markup
    """
    hasLegalMarkup: Boolean

    """
    Legal Markup
    """
    legalMarkup: TranslatedString

    """
    Purpose
    """
    purpose: [ConsentsAndDeclarationsPurpose!]!

    """
    Data field
    """
    dataField: DataField!

    """
    Conditions
    """
    conditions: [Condition!]

    """
    Feature Purpose
    """
    featurePurpose: ConsentFeaturePurpose!

    """
    Permissions
    """
    permissions: [String]!

    """
    Legal Text Position
    """
    legalTextPosition: LegalTextPosition

    """
    Parent ID (optional, pointing to the C&D group)
    """
    parentId: ObjectID
}
