type GroupConsentsAndDeclarations implements ConsentsAndDeclarations {
    """
    UID
    """
    id: ObjectID!

    """
    C&D type
    """
    type: ConsentsAndDeclarationsType!

    """
    Module ID
    """
    moduleId: ObjectID!

    """
    Module
    """
    module: Module!

    """
    Display Name
    """
    displayName: String!

    """
    Is deleted
    """
    isDeleted: Boolean!

    """
    Versioning
    """
    versioning: AdvancedVersioning!

    """
    Title
    """
    title: TranslatedString

    """
    Description
    """
    description: TranslatedString

    """
    Order Number
    """
    orderNumber: Int!

    """
    Is active
    """
    isActive: Boolean!

    """
    Purpose
    """
    purpose: [ConsentsAndDeclarationsPurpose!]!

    """
    Data field
    """
    dataField: DataField!

    """
    Conditions
    """
    conditions: [Condition!]

    """
    Feature Purpose
    """
    featurePurpose: ConsentFeaturePurpose!

    """
    Permissions
    """
    permissions: [String]!

    """
    Parent ID (optional, pointing to the C&D group)
    """
    parentId: ObjectID

    """
    Child consents and declarations
    """
    children: [ConsentsAndDeclarations!]!
}