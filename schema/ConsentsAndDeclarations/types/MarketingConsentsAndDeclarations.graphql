type MarketingPlatform {
    mail: <PERSON><PERSON><PERSON>!
    email: <PERSON>olean!
    phone: <PERSON>olean!
    sms: <PERSON>olean!
    fax: <PERSON><PERSON>an!
}

type MarketingConsentsAndDeclarations implements ConsentsAndDeclarations {
    """
    UID
    """
    id: ObjectID!

    """
    C&D type
    """
    type: ConsentsAndDeclarationsType!

    """
    Module ID
    """
    moduleId: ObjectID!

    """
    Module
    """
    module: Module!

    """
    Display Name
    """
    displayName: String!

    """
    Is deleted
    """
    isDeleted: Boolean!

    """
    Versioning
    """
    versioning: AdvancedVersioning!

    """
    Title
    """
    title: TranslatedString

    """
    Description
    """
    description: TranslatedString

    """
    Order Number
    """
    orderNumber: Int!

    """
    Mandatory
    """
    isMandatory: Boolean!

    """
    Is active
    """
    isActive: Boolean!

    """
    Purpose
    """
    purpose: [ConsentsAndDeclarationsPurpose!]!

    """
    Platform
    """
    platform: MarketingPlatform!

    """
    Data field
    """
    dataField: DataField!

    """
    Conditions
    """
    conditions: [Condition!]

    """
    Default Checked for channels
    """
    defaultChecked: Boolean!

    """
    Feature Purpose
    """
    featurePurpose: ConsentFeaturePurpose!

    """
    Permissions
    """
    permissions: [String]!

    """
    Parent ID (optional, pointing to the C&D group)
    """
    parentId: ObjectID
}
