extend type Mutation {
    """
    Create a new text consent and declaration
    """
    createTextConsentsAndDeclarations(
        moduleId: ObjectID!
        settings: TextConsentsAndDeclarationsSettings!
        conditionSettings: [ConditionSettings!]
        eventId: ObjectID
    ): ConsentsAndDeclarations!

    """
    Create a new checkbox consent and declaration
    """
    createCheckboxConsentsAndDeclarations(
        moduleId: ObjectID!
        settings: CheckboxConsentsAndDeclarationsSettings!
        conditionSettings: [ConditionSettings!]
        eventId: ObjectID
    ): ConsentsAndDeclarations!

    """
    Create a new marketing consent and declaration
    """
    createMarketingConsentsAndDeclarations(
        moduleId: ObjectID!
        settings: MarketingConsentsAndDeclarationsSettings!
        conditionSettings: [ConditionSettings!]
        eventId: ObjectID
    ): ConsentsAndDeclarations!

    """
    Update a text consent and declaration
    """
    updateTextConsentsAndDeclarations(
        suiteId: ObjectID!
        settings: TextConsentsAndDeclarationsSettings!
        conditionSettings: [ConditionSettings!]
    ): ConsentsAndDeclarations

    """
    Update a checkbox consent and declaration
    """
    updateCheckboxConsentsAndDeclarations(
        suiteId: ObjectID!
        settings: CheckboxConsentsAndDeclarationsSettings!
        conditionSettings: [ConditionSettings!]
    ): ConsentsAndDeclarations

    """
    Update a marketing consent and declaration
    """
    updateMarketingConsentsAndDeclarations(
        suiteId: ObjectID!
        settings: MarketingConsentsAndDeclarationsSettings!
        conditionSettings: [ConditionSettings!]
    ): ConsentsAndDeclarations

    """
    Create a new group consent and declaration with children
    """
    createGroupConsentsAndDeclarations(
        moduleId: ObjectID!
        settings: GroupConsentsAndDeclarationsWithChildrenInput!
        conditionSettings: [ConditionSettings!]
        eventId: ObjectID
    ): ConsentsAndDeclarations!

    """
    Update a group consent and declaration with children
    """
    updateGroupConsentsAndDeclarations(
        suiteId: ObjectID!
        settings: GroupConsentsAndDeclarationsWithChildrenInput!
    ): ConsentsAndDeclarations

    """
    Delete a consent and declaration
    """
    deleteConsentsAndDeclarations(suiteId: ObjectID!): Boolean!

    """
    Update Consent Order List
    """
    updateConsentOrderList(featurePurpose: ConsentFeaturePurposeInput!, consentIds: [ObjectID!]!): Boolean!
}
