input GroupConsentsAndDeclarationsSettings {
    """
    Display Name
    """
    displayName: String!

    """
    Title
    """
    title: TranslatedStringInput

    """
    Description
    """
    description: TranslatedStringInput

    """
    Order Number
    """
    orderNumber: Int!

    """
    Is active
    """
    isActive: Boolean!

    """
    Data field
    """
    dataField: DataField!

    """
    Purpose
    """
    purpose: [ConsentsAndDeclarationsPurpose!]!
}

input GroupConsentsAndDeclarationsWithChildrenInput {
    """
    Group settings
    """
    groupSettings: GroupConsentsAndDeclarationsSettings!

    """
    Child consents to create/update
    """
    children: [ConsentsAndDeclarationsChildInput!]!
}

input ConsentsAndDeclarationsChildInput {
    """
    Child consent ID (for updates, null for new consents)
    """
    id: ObjectID

    """
    Type of child consent
    """
    type: ConsentsAndDeclarationsType!

    """
    Settings for text consent
    """
    textSettings: TextConsentsAndDeclarationsSettings

    """
    Settings for checkbox consent
    """
    checkboxSettings: CheckboxConsentsAndDeclarationsSettings

    """
    Settings for marketing consent
    """
    marketingSettings: MarketingConsentsAndDeclarationsSettings

    """
    Condition settings
    """
    conditionSettings: [ConditionSettings!]
}