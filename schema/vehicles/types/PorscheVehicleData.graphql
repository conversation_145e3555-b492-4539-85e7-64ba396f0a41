type PorscheVehicleData {
    orderCodeType: String!
    exteriorColor: PorscheVehicleDataFeature
    interiorColor: PorscheVehicleDataFeature
    wheels: PorscheVehicleDataFeature
    """
    The list of all available options for the vehicle.
    """
    specialEquipments: [PorscheVehicleDataFeature!]!

    images: [PorscheVehicleImages!]!

    """
    Others, unidentifiable options that are not part of the list of specifications on the Porsche Vehicle Data
    """
    others: PorscheVehicleDataFeature

    basePrice: Int!
    basePriceString: String!
    totalPrice: Int!
    totalPriceString: String!

    name: String!

    vehicleId: String!
}

type PorscheVehicleDataFeature {
    key: String
    featureName: String!
    featurePriceString: String
    featurePriceNumber: Int
    featureOptionCode: String!
    featureOptionType: String!
}

type TranslatedText {
    translations: [Translation!]!
}

type Translation {
    languageTag: String!
    translation: String!
}

type PorscheVehicleImages {
    url: String!
    key: String!
}
