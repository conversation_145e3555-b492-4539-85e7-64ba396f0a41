extend type Mutation {
    createDealer(companyId: ObjectID!, dealerInput: DealerInput!): Dealer!

    updateDealer(id: ObjectID!, dealerInput: DealerInput!): Dealer

    deleteDealer(id: ObjectID!): Boolean!

    """
    Import Dealers from Excel file
    """
    importDealers(companyId: ObjectID!, upload: Upload!): ImportDealerResponse!

    createDealerSocialMedia(dealerId: ObjectID!, dealerSocialMediaInput: DealerSocialMediaInput!): DealerSocialMedia!

    updateDealerSocialMedia(
        dealerSocialMediaId: ObjectID!
        dealerSocialMediaInput: DealerSocialMediaInput!
    ): DealerSocialMedia

    deleteDealerSocialMedia(dealerSocialMediaId: ObjectID!): Boolean!

    uploadDealerSocialMediaAsset(dealerSocialMediaId: ObjectID!, upload: Upload!, type: DealerAsset!): Dealer

    deleteDealerSocialMediaAsset(dealerSocialMediaId: ObjectID!, type: DealerAsset!): Dealer
}
