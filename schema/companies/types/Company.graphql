type Company {
    """
    UID
    """
    id: ObjectID!

    """
    Company display name
    """
    displayName: String!

    """
    Company name
    """
    companyName: TranslatedString!

    """
    Company legal name
    """
    legalName: TranslatedString!

    """
    Country code
    """
    countryCode: String!

    """
    Market Code
    """
    marketCode: String

    """
    COE
    """
    coe: Float!

    """
    PPSR Fee
    """
    ppsr: Float!

    """
    Dealer Establishment Fee
    """
    estFee: Float!

    """
    Currency (symbol)
    """
    currency: String!

    """
    Time zone
    """
    timeZone: String!

    """
    Modules
    """
    modules: [Module!]!

    """
    Company phone number
    """
    phone: Phone

    """
    Company email address
    """
    email: String!

    """
    Company address
    """
    address: String

    """
    Company description
    """
    description: TranslatedString

    """
    Company primary color
    """
    color: String!

    """
    Company copyright
    """
    copyright: TranslatedString!

    """
    Company logo
    """
    logo: UploadedFileWithPreview

    """
    Company logo for non white background
    """
    logoNonWhiteBackground: UploadedFileWithPreview

    """
    Company mobile logo
    """
    mobileLogo: UploadedFileWithPreview

    """
    Company favicon
    """
    favicon: UploadedFileWithPreview

    """
    Company font
    """
    font: UploadedFile

    """
    Company font bold
    """
    fontBold: UploadedFile

    """
    Company rounding settings
    """
    roundings: Roundings!

    """
    Company calculation rounding (e.g. round to 10/100/1000)
    """
    calculationRounding: CalculationRounding!

    """
    Company VAT rate setting
    """
    vatRateSettings: VATRateSettings

    """
    Company email settings
    """
    emailSettings: EmailSettings!

    """
    Company sms settings
    """
    smsSettings: SmsSettings!

    """
    Active state
    """
    isActive: Boolean!

    """
    Deletion state
    """
    isDeleted: Boolean!

    """
    Languages
    """
    languages: [LanguagePack!]!

    """
    Permissions
    """
    permissions: [String]!

    """
    Session Timeout
    """
    sessionTimeout: Int!

    """
    Password configuration
    """
    passwordConfiguration: PasswordConfiguration!

    """
    Payment Settings
    """
    paymentSettings: [PaymentSetting!]!

    """
    Live chat settings
    """
    liveChatSettings: [LiveChatSetting!]!

    """
    Autoplay settings
    """
    autoplaySettings: [AutoplaySetting!]!

    """
    Multi Factor Authentication Settings
    """
    mfaSettings: MFASettings

    """
    Available dealers
    """
    availableDealers: [Dealer!]

    """
    Company theme
    """
    theme: CompanyTheme!

    """
    Active maintenance
    """
    activeMaintenanceModule: MaintenanceModule

    """
    Company users
    """
    users: [User!]!

    """
    Company Roles
    """
    roles: [Role!]!

    """
    Company User Groups
    """
    userGroups: [UserGroup!]!

    """
    Versioning
    """
    versioning: SimpleVersioning!

    """
    EDM Email Footer
    """
    edmEmailFooter: EdmEmailFooter!

    """
    Mask settings
    """
    mask: MaskSettings!

    """
    Data Purge Enabled
    """
    isDataPurgeEnabled: Boolean!

    """
    Data Purge After ( in years )
    """
    dataPurgeAfter: Int

    availableModules: AvailableModules!

    enableContentRefinement: Boolean!

    """
    Is Instant Approval Stats Enabled
    """
    isInstantApprovalStatsEnabled: Boolean!

    """
    Allow Limit Dealer Feature
    """
    allowLimitDealerFeature: Boolean!

    """
    Address Autofill
    """
    addressAutofill: Boolean

    """
    Flag to indicate if calendar invites should be sent
    for appointments
    """
    shouldSendCalendarInvite: Boolean!

    """
    Find Nearby Dealer
    """
    findNearbyDealer: Boolean
}
