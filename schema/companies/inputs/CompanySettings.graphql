input CompanySettings {
    displayName: String!
    companyName: TranslatedStringInput!
    legalName: TranslatedStringInput!
    countryCode: String!
    marketCode: String
    timeZone: String!
    currency: String!
    phone: PhonePayload
    email: String!
    address: String
    description: TranslatedStringInput
    coe: Float!
    ppsr: Float!
    # Dealer establishment fee
    estFee: Float!
    color: String!
    copyright: TranslatedStringInput!
    roundings: RoundingsPayload!
    calculationRounding: CalculationRounding!
    vatRateSettings: VATRateSettingsInput
    emailSettings: EmailSettingsPayload!
    smsSettings: SmsSettingsPayload!
    languages: [ObjectID!]!
    sessionTimeout: Int!
    passwordConfiguration: PasswordConfiguration!
    isActive: Boolean!
    mfaSettings: MFASettingsPayload
    theme: CompanyTheme!
    edmEmailFooter: EdmEmailFooterPayload!
    dataMask: MaskSettingsPayload!
    isDataPurgeEnabled: Boolean!
    dataPurgeAfter: Int
    enableContentRefinement: Boolean!
    isInstantApprovalStatsEnabled: Boolean!
    allowLimitDealerFeature: Boolean!
    addressAutofill: Boolean
    shouldSendCalendarInvite: Boolean!
    findNearbyDealer: <PERSON><PERSON>an
}
