type MobilityLead implements Lead {
    """
    Lead ID
    """
    id: ObjectID!

    """
    Identifier
    """
    identifier: String!

    """
    Customer ID
    """
    customerId: ObjectID!

    """
    Customer
    """
    customer: Customer!

    """
    Assignee ID
    """
    assigneeId: ObjectID

    """
    Assignee
    """
    assignee: User

    """
    assignees this lead can be assigned to
    """
    availableAssignees: [User!]!

    """
    Dealer ID
    """
    dealerId: ObjectID!

    """
    Dealer
    """
    dealer: Dealer!

    """
    ModuleId
    """
    moduleId: ObjectID!

    """
    Module
    """
    module: Module!

    """
    Status
    """
    status: LeadStatus!

    """
    Applications or Requests
    """
    applications: [Application!]!

    """
    Trade In Vehicle on Contact/Lead
    """
    tradeInVehicle: [TradeInVehicle!]!

    """
    C\@P Values
    """
    capValues: CapValuesOnApplication

    """
    Versioning
    """
    versioning: AdvancedVersioning!

    """
    Merged to lead suite id
    """
    mergedToLeadSuiteId: ObjectID

    """
    Merged to lead identifier
    """
    mergedToLeadIdentifier: String

    """
    Boolean indicating if the lead is a contact
    """
    isLead: Boolean

    """
    Flag indicating whether Search for Customer has been performed for this Contact/Lead
    This is used in the Qualify workflow to determine which steps to show
    """
    isCustomerSearchPerformed: Boolean

    """
    KYC fields for the customer
    """
    customerKYC: [KYCField!]!

    """
    Agreements for the customer
    """
    customerAgreements: [ApplicationAgreement!]!

    permissions: [String]!

    """
    Vehicle ID
    """
    vehicleId: ObjectID!

    """
    Vehicle
    """
    vehicle: Vehicle

    campaignValues: ApplicationCampaignValues

    """
    Lead Document
    """
    documents: [ApplicationDocument!]!

    """
    Router ID
    """
    routerId: ObjectID

    """
    Endpoint
    """
    endpoint: Endpoint

    """
    Router
    """
    router: Router

    """
    Language ID to determine what language user chose in journey
    """
    languageId: ObjectID

    """
    Origin Sales Consultant
    """
    originSalesConsultantId: ObjectID
    originSalesConsultant: User

    """
    Selected Variant vehicle condition
    """
    vehicleCondition: FinderVehicleCondition

    """
    Selected Variant Purchase Intention
    """
    purchaseIntention: DateTime

    purposeOfVisit: PurposeOfVisit

    intentType: IntentType

    salesOffer: SalesOffer

    """
    Company ID
    """
    companyId: ObjectID!

    """
    Company
    """
    company: Company!

    """
    Lead Module
    """
    leadModule: LaunchPadModule
}
