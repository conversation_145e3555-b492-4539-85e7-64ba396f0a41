type MainDetailsSalesOffer implements SalesOfferFeatureConfigurationCore {
    kind: SalesOfferFeatureKind!
    status: SalesOfferFeatureStatus!

    """
    is Feature Enabled
    """
    isEnabled: Boolean!

    """
    The most recent update once the feature is created
    """
    lastUpdatedAt: DateTime!

    optionsSubsidy: Float
    estimatedDeliveryDate: DateTime
    coeAmount: Float
    numberConsecutiveBids: Int
    remarks: String
    coeCategory: CoeCategory
    documents: [SalesOfferDocument!]!

    tradeInPreOwned: Float
}
