extend type Mutation {
    createSalesOffer(porscheCode: String!, leadSuiteId: ObjectID!, salesOfferModuleId: ObjectID!): SalesOffer

    updateMainDetailsSalesOffer(id: ObjectID!, mainDetails: MainDetailsSalesOfferInput!): SalesOffer!

    updateVehicleSalesOffer(id: ObjectID!, vehicle: VehicleSalesOfferInput!, leadSuiteId: ObjectID!): SalesOffer!

    updateFinanceSalesOffer(id: ObjectID!, finance: FinanceSalesOfferInput!): SalesOffer!

    updateDepositSalesOffer(id: ObjectID!, deposit: DepositSalesOfferInput!): SalesOffer!

    updateInsuranceSalesOffer(id: ObjectID!, insurance: InsuranceSalesOfferInput!): SalesOffer!

    updateTradeInSalesOffer(
        id: ObjectID!
        endpointId: ObjectID!
        languageId: ObjectID!
        tradeIn: TradeInSalesOfferInput!
    ): Lead!

    sendSalesOffer(
        id: ObjectID!
        endpointId: ObjectID!
        languageId: ObjectID!
        salesManagerId: ObjectID
        featureKinds: [SalesOfferFeatureKind!]!
        customer: [LocalCustomerFieldSettings!]!
        consents: [ApplicantAgreementSettings!]!
    ): Lead!

    initialSalesOfferSigning(token: String!): String!
    initialSalesOfferPayment(token: String!): SalesOfferJourney!

    uploadVehicleSalesOfferSpecificationDocument(id: ObjectID!, file: Upload!): SalesOffer!

    downloadSpecificationDocument(id: ObjectID!): String!

    uploadSalesOfferDocument(salesOfferId: ObjectID!, file: Upload!): SalesOffer!
    
    deleteSalesOfferDocument(salesOfferId: ObjectID!, fileId: ObjectID!): SalesOffer!
    downloadSalesOfferDocument(salesOfferId: ObjectID!, fileId: ObjectID!, kind: SalesOfferDocumentKind!): String!
    shareSalesOfferDocument(params: ShareSalesOfferDocumentInput!): SalesOffer!
}
