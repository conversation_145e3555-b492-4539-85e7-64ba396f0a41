input UserFilteringRule {
    """
    Filter by email
    """
    email: String

    """
    Filter by display name
    """
    displayName: String

    """
    Filter by multiple company IDs
    """
    companyIds: [ObjectID!]

    """
    Filter by mobile number
    """
    mobile: String

    """
    Filter by role
    """
    roles: String

    """
    Filter by last signed in
    """
    lastSignedIn: [Timeframe!]

    """
    Filter by User Group
    """
    userGroups: String

    """
    Filter by active
    """
    actives: [Boolean!]

    """
    Filter by included Unassigned  Company or not
    """
    includesUnassignedCompany: Boolean
}
