{"title": "Module specifications", "subTitle": "", "implementations": {"ConsentsAndDeclarationsModule": {"className": "Consents & Declarations", "description": "V5 SAFE App Consents & Declarations", "group": "V5 SAFE App Core"}, "SimpleVehicleManagementModule": {"className": "Vehicle Management", "description": "V5 SAFE App Vehicle Management", "group": "V5 SAFE App Core"}, "LocalCustomerManagementModule": {"className": "Customer Management", "description": "V5 SAFE App Customer Management", "group": "V5 SAFE App Core"}, "BankModule": {"className": "Financing", "description": "Financing & Bank Management", "group": "V5 SAFE App Core"}, "BasicSigningModule": {"className": "Sign with OTP", "description": "OTP Verification via SMS or Email", "group": "Signing"}, "NamirialSigningModule": {"className": "eSign with Nam<PERSON><PERSON>", "description": "Digital Signing with <PERSON><PERSON><PERSON>", "group": "Signing"}, "DocusignModule": {"className": "eSign with Docusign", "description": "Digital Signing with Docusign", "group": "Signing"}, "StandardApplicationModule": {"className": "Showroom Sales", "description": "V5 SAFE App Showroom Sales Channel for New Cars", "group": "Applications"}, "EventApplicationModule": {"className": "Lead C@Pture Forms", "description": "V5 SAFE App Lead C@Pture Forms Channel", "group": "Applications"}, "AdyenPaymentModule": {"className": "<PERSON><PERSON><PERSON>", "description": "Adyen Payment Gateway", "group": "Payment Gateway"}, "PorschePaymentModule": {"className": "Porsche Payment", "description": "Porsche Payment Widget", "group": "Payment Gateway"}, "FiservPaymentModule": {"className": "Fiserv", "description": "Fiserv Payment Gateway", "group": "Payment Gateway"}, "PayGatePaymentModule": {"className": "PayGate", "description": "PayGate Payment Gateway", "group": "Payment Gateway"}, "TtbPaymentModule": {"className": "TTB", "description": "TTB Payment Gateway", "group": "Payment Gateway"}, "MyInfoModule": {"className": "Myinfo", "description": "Myinfo", "group": "Others"}, "ConfiguratorModule": {"className": "Configurators", "description": "V5 SAFE App Configurator Channel", "group": "Applications"}, "FinderApplicationModule": {"className": "<PERSON> Finder - Public", "description": "Porsche Finder to V5 SAFE App Offer Page", "group": "Applications"}, "FinderApplicationPrivateModule": {"className": "Porsche Finder - Private", "description": "Porsche Finder Private Page", "group": "Applications"}, "WhatsappLiveChatModule": {"className": "WhatsApp", "description": "WhatsApp Live Chat", "group": "Live Chat"}, "UserlikeChatbotModule": {"className": "Userlike", "description": "<PERSON><PERSON><PERSON>", "group": "Live Chat"}, "PromoCodeModule": {"className": "Promo Code", "description": "V5 SAFE App Promo Code Management", "group": "V5 SAFE App Core"}, "GiftVoucherModule": {"className": "Gift Codes", "description": "V5 SAFE App Gift Codes Management", "group": "Applications"}, "MainenanceModule": {"className": "Maintenance", "description": "Basic Management for Mainenance Module", "group": "V5 SAFE App Core"}, "SdmModule": {"className": "SDM", "description": "SDM", "group": "Others"}, "WebsiteModule": {"className": "Web Page Builder", "description": "V5 SAFE App Web Page Management", "group": "V5 SAFE App Core"}, "MobilityModule": {"className": "Mobility Rental", "description": "V5 SAFE App Mobility Rental Channel", "group": "Applications"}, "BmwModule": {"className": "External system to V5 SAFE App", "description": "External system to V5 SAFE App, e.g. SDM to V5 SAFE App BMW", "group": "Others"}, "LabelsModule": {"className": "Vehicle Labels", "description": "V5 SAFE App Vehicle Label Management", "group": "V5 SAFE App Core"}, "MaintenanceModule": {"className": "Maintenance Page", "description": "V5 SAFE App Maintenance Page Management", "group": "V5 SAFE App Core"}, "AppointmentModule": {"className": "Appointment - Test Drive", "description": "V5 SAFE App Appointment Management for Test Drive", "group": "V5 SAFE App Core"}, "VisitAppointmentModule": {"className": "Appointment - Showroom Visit", "description": "V5 SAFE App Appointment Management for Showroom Visit", "group": "V5 SAFE App Core"}, "FinderVehicleManagementModule": {"className": "Porsche Finder Vehicles", "description": "Porsche Finder Vehicle Management", "group": "Others"}, "AutoplayModule": {"className": "Autoplay Integration", "description": "Autoplay Integration", "group": "Others"}, "CtsModule": {"className": "Porsche CTS", "description": "Porsche CTS Global Calculator", "group": "Others"}, "AudiCalculationModule": {"className": "Audi Calculation", "description": "Audi Calculation API for Website", "group": "Others"}, "InsuranceModule": {"className": "Insurance", "description": "Insurance & Insurer Management", "group": "V5 SAFE App Core"}, "PorscheMasterDataModule": {"className": "Porsche MasterData", "description": "Porsche Vehicle MasterData", "group": "Others"}, "AudiFinancingModule": {"className": "Audi Financing", "description": "Audi Financing", "group": "Others"}, "TradeInModule": {"className": "APV Trade-in", "description": "Trade-in Management", "group": "Others"}, "AudiSalesForceModule": {"className": "Audi SalesForce", "description": "Audi SalesForce", "group": "Others"}, "CapModule": {"className": "Porsche C@P", "description": "Porsche C@P Integration", "group": "Others"}, "PorscheIdModule": {"className": "Porsche ID", "description": "Porsche ID Integration", "group": "Others"}, "PorscheRetainModule": {"className": "Porsche Retain", "description": "Retain Integration", "group": "Others"}, "LaunchPadModule": {"className": "Launchpad", "description": "V5 SAFE App Launchpad", "group": "Applications"}, "OIDCModule": {"className": "OIDC IdP", "description": "Identity Provider for OpenID Connect", "group": "Others"}, "MarketingModule": {"className": "Marketing Dashboard", "description": "Marketing reports and performance insights", "group": "V5 SAFE App Core"}, "SalesControlBoardModule": {"className": "Sales Control Board", "description": "Sales target achievement report", "group": "V5 SAFE App Core"}, "VehicleDataWithPorscheCodeIntegrationModule": {"className": "Porsche Vehicle Data Integration", "description": "Vehicle Data with Porsche Code Integration", "group": "Others"}, "SalesOfferModule": {"className": "Sales Offer", "description": "Sales Offer", "group": "Applications"}}, "addModule": {"title": "Create Mo<PERSON>le", "subTitle": "", "companySelection": {"title": "Company"}, "classSelection": {"title": "Module Class", "infoMessage": "Module class selection for company {{companyName}}", "cancelButton": "Go back to company selection"}, "configuration": {"title": "Configuration", "infoMessage": "Module configuration for module of type {{type}}", "submitButton": "Complete", "cancelButton": "Go back to class selection", "messages": {"submitting": "Creating module..", "success": "<PERSON><PERSON>le created"}}}, "deleteModal": {"title": "Delete module", "content": "Do you confirm the module deletion ? This action cannot be reverted.", "cancelButton": "Cancel", "submitButton": "Confirm & Delete", "messages": {"submitting": "Deleting module..", "success": "<PERSON><PERSON>le deleted"}}, "alerts": {"moduleImmutableDependencies": {"title": "Warning", "content": "The dependencies may not be changed after creation, the following fields are immutable."}}, "explanations": {"scenarios": {"Payment": {"label": "Payment", "help": "Payment is mandatory"}, "Financing": {"label": "Financing", "help": "Financing is mandatory"}, "Insurance": {"label": "Insurance", "help": "Insurance is mandatory"}, "LeadCapture": {"label": "Lead C@Pture", "help": ""}, "Booking": {"label": "Mobility Booking", "help": ""}, "Appointment": {"label": "Appointment - Test Drive", "help": "Appointment is mandatory"}, "VisitAppointment": {"label": "Appointment - Showroom Visit", "help": "Appointment - Showroom Visit is mandatory"}}, "counterHelp": "Counter prefix may include variables to be swapped with value based on the date", "counterVariables": {"month": "Month digits (1-12)", "year": "Four-digit year (ex. 2022)", "twoDigitYear": "Two-digit year (ex. 22)", "paddedMonth": "Month digits with padded zero (01-12)", "quarter": "Quarter digits (1-4)"}, "applicationMarkets": {"default": "Default (No country/market specific)", "singapore": "Singapore (COE)", "newZealand": "New Zealand (PPSR & Est. Fee)"}, "counterPadding": "Number of leading zeros applied on the incremented index", "counterMethods": {"global": {"label": "Global", "help": "Counter is never reset"}, "yearly": {"label": "Yearly", "help": "Counter is reset at the beginning of each year"}, "monthly": {"label": "Monthly", "help": "Counter is reset at the beginning of each month"}, "quarterly": {"label": "Quarterly", "help": "Counter is reset at the beginning of each quarter"}}, "templates": {"AFCTemplate": {"label": "AFC Template"}, "BMWTemplate": {"label": "BMW Template"}}, "displayPreferences": {"hidden": "<PERSON>de", "alwaysShown": "Show always", "shownWhenMoreThanOne": "Show when more than 1"}, "bankDisplayInSharePDF": {"hidden": "<PERSON>de", "followCalculator": "Follow Calculator"}, "preferenceValue": {"yes": "Yes", "no": "No", "optional": "Optional"}, "finderVehicleCondition": {"all": "All", "new": "New", "preowned": "Pre-Owned", "porscheApproved": "Porsche Approved Pre-Owned"}, "financingPreference": {"mandatory": "Financial Application - Mandatory", "optional": "Financial Application - Optional", "request": "Request"}}, "suffix": {"inWorkingDays": "in working days"}, "fields": {"displayName": {"label": "Display Name"}, "link": {"label": "Link"}, "applicationMarket": {"label": "Market"}, "applicationScenario": {"label": "<PERSON><PERSON><PERSON>"}, "isOcrEnabled": {"label": "OCR"}, "agreementsModuleId": {"label": "Consent module"}, "bankModuleId": {"label": "Financing module"}, "insuranceModuleId": {"label": "Insurance module"}, "customerModuleId": {"label": "Customer module"}, "visitAppointmentModuleId": {"label": "Appointment Module - Showroom Visit"}, "vehicleModuleId": {"label": "Vehicle module"}, "signingModuleId": {"label": "Signing module"}, "displayAppointmentDatepicker": {"label": "Display Appointment Datepicker"}, "displayVisitAppointmentDatepicker": {"label": "Display Appointment for Showroom Visit Datepicker"}, "appointmentModuleId": {"label": "Appointment module - Test Drive"}, "giftVoucherModule": {"label": "Gift Codes"}, "paymentModuleId": {"label": "Payment module / Payment Settings"}, "autoplayModuleId": {"label": "Autoplay Module / Autoplay Settings"}, "capModuleId": {"label": "Porsche C@P Module"}, "isSearchCapCustomerOptional": {"label": "Optional to Search Customer"}, "isCapEnabled": {"label": "Enable C@P Module"}, "prequalification": {"label": "Pre-Qualification", "tooltip": "Set as Yes for Qualify function. Set as No to skip Qualify step and directly send Leads to C@P upon form submission."}, "capLeadOrigin": {"label": "C@P Lead Origin"}, "capLeadSource": {"label": "C@P Lead Source"}, "capCampaignId": {"label": "C@P Campaign ID"}, "applicationCounterMethod": {"label": "Financing - Method"}, "applicationCounterPadding": {"label": "Financing - Padding"}, "applicationCounterPrefix": {"label": "Financing - Prefix"}, "leadCounterMethod": {"label": "Lead - Method"}, "leadCounterPadding": {"label": "Lead - Padding"}, "leadCounterPrefix": {"label": "Lead - Prefix"}, "reservationCounterMethod": {"label": "Reservation - Method"}, "reservationCounterPadding": {"label": "Reservation - Padding"}, "reservationCounterPrefix": {"label": "Reservation - Prefix"}, "eventCounterMethod": {"label": "Event - Method"}, "eventCounterPadding": {"label": "Event - Padding"}, "eventCounterPrefix": {"label": "Event - Prefix"}, "appointmentCounterMethod": {"label": "Appointment - Method"}, "appointmentCounterPadding": {"label": "Appointment - Padding"}, "appointmentCounterPrefix": {"label": "Appointment - Prefix"}, "insuranceCounterMethod": {"label": "Insurance - Method"}, "insuranceCounterPadding": {"label": "Insurance - Padding"}, "insuranceCounterPrefix": {"label": "Insurance - Prefix"}, "vsaCounterMethod": {"label": "VSA - Method"}, "vsaCounterPadding": {"label": "VSA - Padding"}, "vsaCounterPrefix": {"label": "VSA - Prefix"}, "amount": {"label": "Payment Amount"}, "currency": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "merchantAccount": {"label": "Merchant Account ID"}, "apiKey": {"label": "API Key"}, "clientKey": {"label": "Client Key"}, "originKey": {"label": "Origin Key"}, "environment": {"label": "Environment"}, "hmacKeys": {"label": "HMAC Key"}, "username": {"label": "Notification Username"}, "password": {"label": "Notification Password"}, "myInfoSetting": {"label": "Myinfo"}, "endpoint": {"label": "End Point - Authority"}, "apiToken": {"label": "API Token"}, "coe": {"label": "COE", "coeEditable": {"label": "COE Editable"}}, "ppsr": {"label": "PPSR", "ppsrEditable": {"label": "PPSR Editable"}}, "estFee": {"label": "Dealer <PERSON><PERSON><PERSON>", "estFeeEditable": {"label": "Dealer <PERSON><PERSON><PERSON> Fee Editable"}}, "bankEstFee": {"label": "{{bankLegalName}} Est. Fee"}, "bankEstFeeEditable": {"label": "Bank Est. Fee Editable"}, "nzFeesViewable": {"label": "Display NZ Fees"}, "priceDisclaimer": {"label": "Price Disclaimer", "tooltipSG": "Please use {{coeprice}} to insert COE value as defined by the COE field.", "tooltipNZ": "Please use {{ppsr}} and {{estFee}} to insert PPSR or Est. Fee value as defined by the PPSR field and Est. Fee field."}, "preownedPriceDisclaimer": {"label": "Preowned Price Disclaimer"}, "depositAmount": {"label": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>"}, "testDrive": {"label": "Test Drive"}, "tradeIn": {"label": "Trade-in"}, "isTradeInAmountVisible": {"label": "Trade-in Amount Input"}, "assignee": {"label": "Assignee"}, "liveChatSetting": {"label": "LiveChat"}, "promoCodeModule": {"label": "Promo Code"}, "priceDisclaimerTitle": {"label": "Price Disclaimer"}, "reservationInstructions": {"label": "Reservation Instruction"}, "financingDisclaimerTitle": {"label": "Financing Disclaimer"}, "termsTitle": {"label": "T&C Title"}, "termsText": {"label": "T&C Text"}, "sdm": {"label": "SDM"}, "baseUrl": {"label": "Base URL"}, "publicCert": {"label": "Public Certificate"}, "privateKey": {"label": "Private Key"}, "inventoryEnabled": {"label": "Inventory Enabled"}, "importExportTemplate": {"label": "Import/Export Template"}, "isFinancingOptional": {"label": "Finance Application is Optional"}, "financingPreference": {"label": "Financing In CI - Request / Application"}, "isInsuranceOptional": {"label": "Insurance Application is Optional"}, "showResetKYCButton": {"label": "Show Reset Button in KYC page "}, "showFromValueOnVehicleDetails": {"label": "From Value on Vehicle Details"}, "bankDisplayPreference": {"label": "Bank in Calculator"}, "bankDisplayInSharePdf": {"label": "Bank in Share PDF"}, "insurerDisplayPreference": {"label": "Insurer in Calculator"}, "showRemoteFlowButtonInKYCPage": {"label": "Proceed with Customer's Device (Private Access)"}, "skipForDeposit": {"label": "<PERSON><PERSON>"}, "clientId": {"label": "Client ID"}, "clientSecret": {"label": "Client Secret"}, "grantType": {"label": "Grant Type"}, "authority": {"label": "Certificate Authority"}, "bookingsCounterMethod": {"label": "Booking - Method"}, "bookingsCounterPadding": {"label": "Booking - Padding"}, "bookingsCounterPrefix": {"label": "Booking - Prefix"}, "bmw": {"label": "BMW"}, "bankId": {"label": "Bank"}, "dealer": {"label": "Dealer"}, "externalUrl": {"label": "External URL"}, "allowLTA": {"label": "LTA"}, "secretKey": {"label": "Secret Key"}, "loginEndpoint": {"label": "Login Endpoint"}, "apiEndpoint": {"label": "API Endpoint"}, "finderUrl": {"label": "Finder URL"}, "unavailableDayofWeek": {"label": "Unavailable Day of Week"}, "finderVehicleCondition": {"label": "Finder Vehicle Condition"}, "porscheApprovedInfo": {"label": "Info of Porsche Approved Pre-Owned"}, "paymentSetting": {"label": "Payment"}, "insurer": {"label": "Insurer"}, "bank": {"label": "Bank"}, "showFinanceCalculator": {"label": "Show Finance Calculator"}, "showInsuranceCalculator": {"label": "Show Insurance Calculator"}, "useBankDisclaimers": {"label": "Use Bank's Disclaimers"}, "useInsurerDisclaimers": {"label": "Use Insurer’s Disclaimers"}, "ctsSettingId": {"label": "Setting ID"}, "audiCalculationSettingId": {"label": "Setting ID"}, "hasDealerOptions": {"label": "Dealer Options"}, "includeDealerOptionsForFinancing": {"label": "Include Dealer Option for Financing"}, "showVisitModelPageButton": {"label": "Visit Model Page"}, "modelPageUrl": {"label": "Model Page URL"}, "reservationPeriod": {"label": "Reservation period"}, "porscheMasterDataModule": {"label": "Porsche MasterData Module"}, "audiFinancingModule": {"label": "Audi Financing Module"}, "finderModules": {"label": "Finder <PERSON>"}, "isActive": {"label": "Active"}, "ggId": {"label": "Sales Area ID"}, "ppnUrl": {"label": "PPN URL"}, "ppnClientId": {"label": "PPN Client ID"}, "ppnClientSecret": {"label": "PPN Client Secret"}, "ppnResource": {"label": "PPN Resource"}, "pccdUrlPrefix": {"label": "PCCD URL Prefix"}, "pccdClientId": {"label": "PCCD Client ID"}, "pccdClientSecret": {"label": "PCCD Client Secret"}, "pccdToken": {"label": "PCCD Token"}, "tradeInAppBaseUrl": {"label": "Trade-in App Base URL"}, "tradeInAppClientId": {"label": "Trade-in App Client ID"}, "tradeInAppClientSecret": {"label": "Trade-in App Client Secret"}, "tradeInModuleId": {"label": "Trade-in Module"}, "isPorscheIdLoginMandatory": {"label": "Porsche ID Login is Mandatory"}, "isCustomerDataRetreivalByPorscheId": {"label": "Customer Data Retrieval by Porsche ID"}, "authBasePath": {"label": "Authenticate Base Path"}, "authClientId": {"label": "Authenticate Client ID"}, "authClientSecret": {"label": "Authenticate Client Secret"}, "basePath": {"label": "Base Path"}, "porscheClientXId": {"label": "Porsche Client X ID"}, "porscheClientXSecret": {"label": "Porsche Client X Secret"}, "vehicleDataWithPorscheCodeIntegrationSettingId": {"label": "Porsche Vehicle Integration Setting ID"}, "coeBiddingTerms": {"label": "COE Bidding Terms"}, "specificationTerms": {"label": "Specification Terms"}, "vsaSigningInstructions": {"label": "VSA Signing Instructions"}, "vsaTerms": {"label": "VSA Terms"}}, "selectFields": {"options": {"none": "None"}}, "sections": {"mainDetails": "Main Details", "sdmModule": {"salesForce": "Salesforce"}, "dependencies": "Dependencies", "idGenerators": "ID Generators"}, "message": {"moduleInUse": {"label": "Unable to delete, module has CTS Settings"}, "settingInUse": {"label": "Unable to delete, CTS Setting attached in some applications"}}, "errors": {"counterSetting": {"invalidYearlyPrefix": "{YYYY} <strong><underline>or</underline></strong> {YY} must be in Prefix", "invalidMonthlyPrefix": "{YYYY} or {YY} <strong><underline>and</underline></strong> {MM} <strong><underline>or</underline></strong> {M} must be in Prefix", "invalidQuarterlyPrefix": "{YYYY} or {YY} <strong><underline>and</underline></strong> either {Q} <strong><underline>or</underline></strong> {MM} <strong><underline>or</underline></strong> {M} must be in Prefix"}}}