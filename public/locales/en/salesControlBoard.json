{"title": "Sales Control Board", "actions": {"filter": "Filter", "reset": "Reset Filters", "applyFilters": "Apply Filters", "import": "Import", "export": "Export"}, "modal": {"title": "Import Data", "error": {"title": "Partial Import Completed", "description": "The file {{ fileName }} was partially imported. Some rows were skipped because they contain missing or incomplete information.", "subDescription": "Please fix the issues and re-import the affected rows."}, "description": "Please select the Data Type and upload the indicated file.", "fields": {"dataType": {"label": "Data Type"}, "reportingPeriod": {"label": "Reporting Period"}}, "buttons": {"submit": "Submit", "cancel": "Cancel", "understood": "Understood"}, "file": {"placeholder": "Select file or drop file here", "button": "Select File", "allowExtension": "Accepted format: CSV."}}, "noCompanySelected": "Please select Company to view Sales Control Board.", "noDealerSelected": "Please select Dealer to view Sales Control Board.", "noDataWarning": "There is no data imported for type {{ types }} on {{ month }}.", "performanceOverview": {"titleForManager": "Performance Overview - {{ selected<PERSON>onth }}", "titleForConsultant": "My Performance Overview - {{ selected<PERSON>onth }}"}, "progressToGoal": {"title": "Progress to Goal - {{ <PERSON><PERSON><PERSON><PERSON> }}"}, "performance": {"titleForManager": "Sales Consultant Performance - {{ selectedMonth }}", "titleForConsultant": "My Performance - {{ <PERSON><PERSON><PERSON><PERSON> }}"}, "weekSalesFunnel": {"title": "Week {{ count }} Sales Funnel: {{ dateRange }}"}, "filter": {"title": "Filter", "fields": {"monthOfImport": {"label": "Month of Import"}, "reportingView": {"label": "Reporting View"}, "salesConsultant": {"label": "Sales Consultant"}, "vehicleModel": {"label": "Vehicle Model"}, "reportingViewOptions": {"month": "Month", "ytd": "YTD"}}}, "messages": {"importing": "Importing data...", "importSuccess": "Data imported successfully."}}