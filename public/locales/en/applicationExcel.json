{"consent": {"personalData": "Personal Data Consent", "mail": "Mail Consent", "phone": "Phone Consent", "email": "<PERSON><PERSON>", "sms": "SMS Consent", "fax": "Fax Consent"}, "appointment": {"timezoneWithOffset": "Appointment Date and Time {{timezone}}", "timezone": "Appointment Date and Time"}, "payment": {"promoCode": "Promo Code", "promoCodeAmount": "Promo Code Amount", "giftCode": "Gift Code", "giftCodeAmount": "Gift Code Amount", "paymentAmount": "Payment Amount", "paymentStatus": "Payment Status", "paymentDateWithTimeZone": "Payment Date {{timeZone}}", "paymentDate": "Payment Date", "transactionId": "Transaction ID", "paymentMethod": "Payment Method"}, "main": {"mobilityCreatedDateWithTimeZone": "Date of Booking {{timeZone}}", "mobilityCreatedDate": "Date of Booking", "mobilityUpdatedAtWithTimeZone": "Last Activity {{timeZone}}", "mobilityUpdatedAt": "Last Activity", "status": "Status", "mobilityStartDateWithTimeZone": "Start Date {{timeZone}}", "mobilityStartDate": "Start Date", "mobilityEndDateWithTimeZone": "End Date {{timeZone}}", "mobilityEndDate": "End Date", "location": "Location", "dealer": "Dealer", "createdBy": "Created By", "assignee": "Assignee", "salesperson": "Salesperson", "salespersonRefCode": "Salesperson Ref Code", "module": "<PERSON><PERSON><PERSON>", "source": "Source", "otherApplicationReference": "Other Applications Reference", "dateSubmissionResubmissionWithTimeZone": "Date of Submission/Resubmission {{timeZone}}", "dateSubmissionResubmission": "Date of Submission/Resubmission", "dateOfApprovalWithTimeZone": "Date of Approved/Declined {{timeZone}}", "dateOfApproval": "Date of Approved/Declined", "durationApprovalDecline": "Duration of Submitted to Approved/Declined", "eventDisplayName": "Lead Capture Form Label", "leadCaptureFormName": "Lead Capture Form Name", "appointmentDateTime": "Appointment Date and Time", "market": "Market", "lastModified": "Last Modified"}, "vehicle": {"make": "Make", "model": "Model", "subModel": "subModel", "vehicleName": "Vehicle Name", "vehicleId": "Vehicle ID", "price": "Price", "testDrive": "Test Drive", "chassisNo": "Chassis No.", "engineNo": "Engine No."}, "bmw": {"customerId": "Customer ID", "fullName": "Full Name", "nric": "NRIC", "mobile": "Mobile", "email": "Email", "dob": "DOB", "accessories": "Accessories", "condition": "Condition", "omv": "OMV"}, "sdm": {"customerId": "Customer ID", "fullName": "Full Name", "nric": "NRIC", "mobile": "Mobile", "email": "Email", "dob": "DOB", "accessories": "Accessories", "condition": "Condition"}, "financing": {"bank": "Bank", "affinBankAFC": "Affin Bank Auto Finance Centre", "financeProduct": "Financial Product", "interestRate": "Interest Rate", "terms": "Terms", "loanCurrency": "Loan ({{currency}})", "loan": "Loan", "approvalRemark": "Approval Remark"}, "insurance": {"insuranceCompany": "Insurance Company", "insurancePremiumCurrency": "Insurance Premium ({{currency}})", "insurancePremium": "Insurance Premium", "insuranceProduct": "Insurance Product"}, "cap": {"businessPartnerId": "C@P BP ID", "leadId": "C@P Lead ID"}, "campaignValues": {"utmCampaign": "UTM Campaign", "utmMedium": "UTM Medium", "utmSource": "UTM Source", "capCampaignId": "C@P Campaign ID", "capCampaignIdDescription": "C@P Campaign ID Description", "capLeadSource": "C@P Lead Source", "utmUrl": "UTM Campaign URL", "capLeadOrigin": "C@P Lead Origin", "mediaCampaignId": "UTM Campaign ID", "campaignCountryRegion": "UTM Campaign Country Region", "campaignModel": "Campaign Model", "campaignSubTopic": "Campaign Sub Topic", "campaignLanguage": "Campaign Language", "utmContent": "UTM Content", "campaignAudience": "Campaign Audience", "campaignType": "Campaign Type", "campaignAddonParameter": "UTM Additional Parameter", "campaignPoNumber": "UTM PO Number"}}