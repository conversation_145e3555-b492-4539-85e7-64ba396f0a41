{"addTitle": "Company", "title": "Company - {{name}}", "cardTitles": {"companySetup": "Main Details", "companyDetails": "Company Details", "countryDetails": "Country Details", "emailSettings": "<PERSON><PERSON>s", "smsSettings": "SMS Settings", "emailFooter": "<PERSON><PERSON>", "customerDataMasking": "Customer Data Masking"}, "actions": {"create": "Create", "update": "Update", "delete": "Delete", "addSocialMediaIcons": "Add Social Media Icons"}, "fields": {"name": {"label": "Reference Name"}, "companyName": {"label": "Company Name"}, "legalName": {"label": "Legal Name"}, "country": {"label": "Country"}, "active": {"label": "Active"}, "dataPurgeEnabled": {"label": "Data Purge"}, "dataPurgeAfter": {"label": "All Data to be Purged After"}, "timeZone": {"label": "Time Zone"}, "phone": {"label": "Phone"}, "email": {"label": "Email"}, "enableContentRefinement": {"label": "Rewrite with AI"}, "address": {"label": "Address"}, "description": {"label": "Company Description"}, "color": {"label": "Theme: Highlight Colour"}, "theme": {"label": "Theme (UI Library)"}, "copyright": {"label": "Copyright", "tooltip": "Use {{currentYear}} to display current year."}, "logo": {"label": "Logo for White Background"}, "logoNonWhiteBackground": {"label": "Logo for Non-White Background"}, "mobileLogo": {"label": "Logo in Mobile View"}, "favicon": {"label": "Favicon"}, "font": {"label": "Company Web Font (Regular)"}, "fontBold": {"label": "Company Web Font (Bold)"}, "countryCode": {"label": "Country"}, "marketCode": {"label": "Market"}, "languages": {"label": "Language", "tooltip": "System language in English will be used if this field is left empty."}, "currency": {"label": "Currency Symbol"}, "roundingAbsoluteAmount": {"label": "Rounding for Absolute Amount and Calculation"}, "roundingPercentage": {"label": "Rounding for %"}, "calculationRounding": {"label": "Rounding for Calculation", "tooltip": "Choosing \"NONE\" will ignore this setting, and the rounding defined in Rounding for Absolute Amount and Calculation will apply instead. If any other option is selected, it will override the rounding in that setting, and apply only to calculated values: Downpayment (ROUNDUP), Loan Amount (ROUNDDOWN), and Monthly Payment (ROUNDUP)."}, "VATRateSetting": {"label": "VAT Rate", "tooltip": "Default VAT Rate is displayed here. Additional VAT with specific Start Date can be added."}, "emailProvider": {"label": "SMTP Provider"}, "emailHost": {"label": "SMTP Host"}, "emailPort": {"label": "SMTP Port No"}, "emailSsl": {"label": "SSL enabled"}, "emailAuthenticationRequired": {"label": "Requires Authentication"}, "emailUsername": {"label": "Username"}, "emailPassword": {"label": "Password"}, "emailFrom": {"label": "From Email ID"}, "emailCertificate": {"label": "Certificate"}, "sessionTimeout": {"label": "Session Timeout"}, "passwordConfiguration": {"label": "Password Format for Attachment Sent to Customer"}, "smsProvider": {"label": "SMS Provider"}, "twilioAccount": {"label": "<PERSON><PERSON><PERSON>"}, "twilioToken": {"label": "<PERSON><PERSON><PERSON>"}, "twilioSender": {"label": "<PERSON><PERSON><PERSON>"}, "mfa": {"label": "2FA"}, "emailFooter": {"footerText": {"label": "Footer Text"}, "disclaimerText": {"label": "Disclaimer Text"}, "privacyPolicyUrl": {"label": "Privacy Policy URL"}, "legalNoticeUrl": {"label": "Legal Notice URL"}, "arrowIcon": {"label": "Arrow Icon"}, "emailIcon": {"label": "Email Icon"}, "phoneIcon": {"label": "Phone Icon"}, "copyRight": {"label": "Copyright"}}, "maskDirection": {"label": "Mask Direction"}, "maskCount": {"label": "Mask Count"}, "isInstantApprovalStatsEnabled": {"label": "Instant Approval Stats"}, "allowLimitDealerFeature": {"label": "Allow Limit Dealer Features"}, "addressAutofill": {"label": "Address Autocomplete", "tooltip": "Suggests address results when the user pre-fills an address."}, "findNearbyDealer": {"label": "Find Nearby Dealer", "tooltip": "Suggests the nearest dealer based on the searched Address/Postal Code in the Lead C@Pture Form."}, "shouldSendCalendarInvite": {"label": "Send Calendar Invite"}}, "suffixes": {"dataPurgeAfter": "years", "sessionTimeout": "min"}, "messages": {"createSubmitting": "Creating company..", "createSuccessful": "Company created", "deleteSubmitting": "Requesting company deletion..", "deleteSuccessful": "Company deleted", "updateSubmitting": "Updating company..", "updateSuccessful": "Company updated"}, "deleteModal": {"title": "Deletion", "content": "Deletion of Company will also delete all Applications, Contents and Settings related to it. Do you want to proceed?", "okText": "Confirm & Delete", "cancelText": "Cancel", "confirmationLabel": "Please type '{{CONFIRMATION_TEXT}}' in the text box below to confirm the deletion of the company.", "confirmationRequired": "Confirmation is required"}, "emailFooter": {"socialMediaIcons": "Social Media Icons"}}