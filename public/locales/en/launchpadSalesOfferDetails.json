{"title": "Sales Offer", "tabs": {"mainDetails": "Main Details", "vehicle": "Vehicle", "tradeIn": "Trade-in", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "financing": "Financing", "insurance": "Insurance", "summary": "Summary", "documents": "Documents", "logs": "Logs"}, "options": {"deposit": {"depositMethod": {"online": "Online", "offline": "Offline"}}, "mainDetails": {"coeCategory": {"b": "B", "e": "E"}}}, "sections": {"mainDetails": {"title": "Main Details", "fields": {"allPrice": {"label": "Vehicle Price + Locally Fitted Options"}, "tradeInPreOwned": {"label": "Trade-in Pre-Owned (Net Outstanding)", "required": true}, "optionsSubsidy": {"label": "Options Subsidy", "required": true}, "estimatedDeliveryDate": {"label": "Estimated Delivery Date", "required": true}, "coe": {"label": "COE Amount", "required": true}, "consecutiveBidsNo": {"label": "No. of Consecutive Bids", "required": true}, "remarks": {"label": "Remarks", "placeholder": "Enter your remarks"}, "coeCategory": {"label": "COE Category"}, "vsaSerialNumber": {"label": "VSA Serial No."}}}, "summaryDetails": {"salesOfferTitle": "Sales Offer", "preOfferTitle": "Pre-Offer", "selectAll": "Select All", "actions": {"send": "Send"}, "category": {"vsa": "Vehicle Sales Agreement", "vehicle": "Specifications Agreement", "coe": "COE Bidding Agreement", "deposit": "Deposit Payment", "finance": "Financial Application", "insurance": "Insurance Application"}, "status": {"Updated": "Updated", "PendingManager": "Pending Manager", "PendingCustomer": "Pending Customer", "Signed": "Signed", "PaymentCompleted": "Payment Completed", "Expired": "Expired"}, "kyc": {"salesManagerTitle": "Select Manager to Sign VSA", "salesManager": "Sales Manager"}}, "documents": {"systemDocumentsTitle": "System Documents", "otherDocumentsTitle": "Other Documents", "uploadFile": "Upload File", "salesOfferDisplayName": "Sales Offer - {{date}}", "shareModal": {"title": "Share Document", "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email"}, "send": "Send"}, "deleteConfirmModal": {"title": "Delete Document", "content": "Do you want to delete {{filename}}?", "okText": "Confirm"}}, "vehicle": {"title": "Vehicle Details", "fields": {"commissionNo": {"label": "Commission No."}, "chassisNo": {"label": "Chassis No."}, "engineMotorNo": {"label": "Engine/Motor No."}, "porscheCode": {"label": "Porsche Code"}, "specificationDocument": {"label": "Specifications Document", "placeholder": "Upload specifications document"}}, "vehiclePrice": "Vehicle Price", "retrieve": "Retrieve"}, "locallyFittedOptions": {"title": "Locally Fitted Options", "addMore": {"button": "Add More", "modal": {"title": "Add Locally Fitted Option", "fields": {"itemDescription": {"label": "Item Description", "placeholder": "Enter your remarks", "required": true}, "price": {"label": "Price", "required": true}}, "button": "Add"}}}, "insuranceDetails": {"title": "Insurance Details", "insurance": "Insurance", "fields": {"offerWithInsurance": {"label": "Offer with Insurance"}}}, "financingDetails": {"title": "Financing Details", "financing": "Financing", "fields": {"offerWithFinancing": {"label": "Offer with Financing"}}}, "tradeinDetails": {"title": "Trade-in Details", "tradeIn": "Trade-in", "fields": {"offerWithTradeIn": {"label": "Offer with Trade-in"}}, "actions": {"sendRequest": "Send Request"}}, "depositDetails": {"title": "De<PERSON>sit Details", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "fields": {"depositMethod": {"label": "Method of Payment"}, "depositAmount": {"label": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>"}}}}, "messages": {"updatingTradein": "Trade in updating..", "updateTradeInSuccessful": "Trade in updated", "updatingSalesOfferMainDetails": "Main Details updating..", "updateSalesOfferMainDetailsSuccessful": "Main Details updated", "updatingVehicleSalesOffer": "Vehicle Details updating..", "updateVehicleSalesOfferSuccessful": "Vehicle Details updated", "retrievingPorscheCode": "Retrieving Porsche Code..", "retrievedPorscheCodeSuccessful": "Porsche Code retrieved", "updatingFinanceSalesOffer": "Financing Details updating..", "updateFinanceSalesOfferSuccessful": "Financing Details updated", "updatingDepositSalesOffer": "Deposit Details updating..", "updateDepositSalesOfferSuccessful": "Deposit Details updated", "updatingInsuranceSalesOffer": "Insurance Details updating..", "updateInsuranceSalesOfferSuccessful": "Insurance Details updated", "updatingTradeInSalesOffer": "Trade In Details updating..", "updateTradeInSalesOfferSuccessful": "Trade In Details updated", "sendingSalesOffer": "Sales offer sending..", "sentSalesOfferSuccessful": "Sales offer sent", "sendingPreOffer": "Pre-offer sending..", "sentPreOfferSuccessful": "Pre-offer sent", "uploadSpecificationDocument": "Uploading specifications document..", "uploadSpecificationDocumentSuccessful": "Specifications document uploaded", "porscheCodeMismatch": "Porsche Code is for a different variant. Create new Lead and Sales Offer for new variant.", "uploadDocument": "Uploading document..", "uploadDocumentSuccessful": "{{filename}} has been uploaded.", "deleteDocument": "Deleting document..", "deleteDocumentSuccessful": "{{filename}} has been deleted.", "downloadDocument": "Downloading document..", "downloadDocumentSuccessful": "{{filename}} has been downloaded.", "shareDocument": "Sharing document..", "shareDocumentSuccessful": "{{filename}} has been shared."}}