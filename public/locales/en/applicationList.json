{"title": "Financings", "titles": {"appointment": "Appointments"}, "actions": {"download": "Download", "create": "Create"}, "columns": {"company": "Company", "applicationDate": "Financing Date", "insuranceDate": "Insurance Date", "leadDate": "Lead Date", "reservationDate": "Reservation Date", "bookingSubmitted": "Booking Submitted", "appointmentDate": "Appointment Date", "tradeInDate": "Date", "bookingDate": "Booking Date", "id": "APP ID", "bookingId": "Mobility Booking ID", "assignedTo": "Assigned to", "customer": "Customer", "variant": "<PERSON><PERSON><PERSON>", "bankName": "Bank", "financialProductName": "Financial Product", "loan": "Loan", "status": "Status", "lastUpdated": "Last Activity", "vehicle": "<PERSON><PERSON><PERSON>", "transactionId": "Transaction Id", "module": "<PERSON><PERSON><PERSON>", "vin": "VIN", "totalAmountPaid": "Paid", "location": "Pickup Location", "startDateTime": "Start Date/Time", "endDateTime": "End Date/Time", "insuranceCompany": "Insurance Company", "insurancePremium": "Insurance Premium {{currency}}", "dealer": "Dealer", "finderVin": "Finder VIN", "capStatus": "C@P Status", "leadGenFormName": "LCF Name", "leadGenFormCampaignId": "Campaign ID", "creationDate": "Creation Date", "followUpDate": "Follow-Up Date"}, "status": {"Activated": "Activated", "ApplicantDetailsPendingUpdate": "Applicant Details Pending Update", "ApplicantDetailsReceived": "Applicant Details Received", "GuarantorDetailsPendingUpdate": "Guarantor Details Pending Update", "GuarantorDetailsReceived": "Guarantor Details Received", "ApplicationReceived": "Application Received", "AppointmentMade": "Appointment Made", "Approved": "Approved", "BankReviewInProgress": "Bank Review In Progress", "Cancelled": "Cancelled", "CancellationdFailed": "Cancellation Failed", "CheckIn": "On-<PERSON>re", "AppointmentCheckIn": "Check In", "Completed": "Completed", "ConnectionFailed": "Connection Failed", "Declined": "Declined", "Drafted": "Drafted", "Lead": "Lead", "NewLead": "New", "OTPCompleted": "OTP Completed", "OTPPending": "OTP Pending", "PaymentCompleted": "Payment Completed", "PaymentFailed": "Payment Failed", "PaymentPending": "Pending Payment", "PaymentRefunded": "Payment Refunded", "PaymentTimeout": "Payment Timeout", "PaymentReceived": "Payment Received", "PaymentReceivedOffline": "Payment Received Offline", "PendingCustomerConfirmation": "Pending Customer's Confirmation", "PendingDisbursement": "Pending Disbursement", "ResubmissionToBankFailed": "Resubmission To Bank Failed", "ResubmittedToBank": "Resubmitted To Bank", "ResubmittedToSystem": "Resubmitted To System", "Shared": "Shared", "SigningCompleted": "Signing Completed", "SigningCreationFailed": "Signing Creation Failed", "SigningInitiated": "Signing Initiated", "SigningPending": "Signing Pending", "SigningRejected": "Signing Rejected", "SigningTimeout": "Signing Timeout", "SubmissionToBankFailed": "Submission To Bank Failed", "SubmissionFailed": "Submission Failed", "SubmittedToBank": "Submitted To Bank", "SubmittedToSystem": "Pending Reservation", "SubmittedWithError": "Submitted to C@P with Errors", "UnableToCancel": "Unable to Cancel", "UnableToConnect": "Unable to Connect", "UnabletoMakePayment": "Unable to Make Payment", "UnableToSubmit": "Unable to Submit", "SubmittedToInsuranceCompany": "Submitted To Insurance Company", "SubmissionToInsuranceCompanyFailed": "Submission To Insurance Company Failed", "InsuranceCompanyReviewInProgress": "Insurance Company Review In Progress", "NewAppointment": "New", "NewMobility": "New", "Contacted": "Contacted", "GuarantorSigningCompleted": "Guarantor Signing Completed", "GuarantorSigningInitiated": "Guarantor Signing Initiated", "GuarantorSigningPending": "Guarantor Signing Pending", "GuarantorSigningRejected": "Guarantor Signing Rejected", "AgreementConcluded": "Agreement Concluded", "FinancingRequest": "Financing Request", "BookingConfirmed": "Booking Confirmed", "TestDriveStarted": "Started", "TestDriveCompleted": "Completed", "TestDriveSigningCompleted": "Test Drive Signing Completed", "TestDriveSigningPending": "Test Drive Signing Pending", "TestDriveSigningInitiated": "Signing Initiated", "TestDriveSigningRejected": "Test Drive Signing Rejected", "TestDriveDetailsPendingUpdate": "Test Drive Pending Update", "TestDriveDetailsReceived": "Test Drive Details Received", "InsuranceCancelled": "Cancelled", "InsuranceDeclined": "Declined", "InsuranceApproved": "Approved", "Qualified": "Qualified", "Unqualified": "Unqualified", "PendingQualify": "Pending Qualify", "InProcess": "In Process", "Lost": "Lost", "New": "New", "TradeInPending": "Pending", "TradeInQuoted": "Quoted", "SubmittingToCap": "Submitting To C@P", "FollowUpPlanned": "Planned", "FollowUpConfirmed": "Confirmed", "FollowUpCancelled": "Cancelled"}, "leadStatus": {"Completed": "Completed", "Drafted": "Drafted", "SubmittedToCap": "Submitted to C@P", "Lost": "Lost", "PendingQualify": "Pending Qualify", "Shared": "Shared", "SubmissionFailed": "Submission to C@P Failed", "SubmittedWithError": "Submitted to C@P with Errors", "Unqualified": "Unqualified", "SubmittingToCap": "Submitting To C@P", "Contacted": "Contacted", "Merged": "<PERSON>rged"}, "capStatus": {"PendingQualify": "Pending Qualify", "Submitted": "Submitted to C@P", "Qualified": "Qualified", "SubmittedWithError": "Submitted to C@P with Errors", "SubmissionFailed": "Submission to C@P Failed", "Unqualified": "Unqualified", "SubmittingToCap": "Submitting To C@P"}, "noData": {"message": "There is no application setup in the system yet, do you want to create one ?", "action": "Create an application"}, "ciCard": {"assigned": "Assigned", "variant": "<PERSON><PERSON><PERSON>", "loan": "Loan"}, "downloadModal": {"title": "Download Finance Applications", "description": "Please select the module and the time frame of applications to download."}, "download": {"initiated": "Download initiated. The file will be delivered to your email shortly.", "noRecordToDownload": "There is no record to download.", "downloadNotCompleted": "Download not completed (Code: 400). Please try again or contact support for assistance."}, "values": {"noInsurancePremium": "N.A."}, "approvalDate": {"approvalDay_one": "{{count}} day", "approvalDay_other": "{{count}} days", "approvalDay_zero": "", "approvalHour_one": "{{count}} hour", "approvalHour_other": "{{count}} hours", "approvalHour_zero": "", "label": "$t(applicationList:approvalDate.approvalDay, {\"count\": {{days}} }) $t(applicationList:approvalDate.approvalHour, {\"count\": {{hours}} }) "}, "tabs": {"visitAppointment": "Showroom Visit", "appointment": "Test Drive"}}