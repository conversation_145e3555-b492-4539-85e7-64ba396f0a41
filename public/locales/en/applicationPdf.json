{"title": "Application Agreement PDF", "signature": "Applicant Signature", "guarantorSignature": "Guarantor Signature", "sections": {"applicationDetails": {"title": "Application Details", "fields": {"applicationId": {"label": "Application Id"}, "dealer": {"label": "Dealer"}, "channel": {"label": "Channel"}, "createIn": {"label": "Date Created"}, "createBy": {"label": "Created By"}, "creatorMobile": {"label": "Salesperson Mobile"}, "creatorReferenceCode": {"label": "Salesperson Reference ID"}}}, "testDriveVehicleDetails": {"title": "Vehicle Details", "fields": {"model": {"label": "Model"}, "variant": {"label": "<PERSON><PERSON><PERSON>"}, "startMileage": {"label": "Mileage Start"}, "registrationNumber": {"label": "Registration Number"}, "timeOut": {"label": "Time out {{offset}}"}}}, "tradeInVehicleDetails": {"title": "Current Vehicle", "fields": {"source": {"label": "Vehicle Source"}, "ownership": {"label": "Ownership"}, "make": {"label": "Make"}, "model": {"label": "Model"}, "equipmentLine": {"label": "Equipment Line"}, "modelYear": {"label": "Model Year"}, "purchaseYear": {"label": "Purchase Year"}, "engineType": {"label": "Engine Type"}, "registrationNumber": {"label": "Registration Number"}, "mileage": {"label": "Mileage (km)"}, "firstRegistrationDate": {"label": "Registration Date"}, "yearOfManufacture": {"label": "Year of Manufacture"}, "roadTaxExpiryDate": {"label": "Road Tax Expiry Date"}, "engineCapacity": {"label": "Engine Capacity"}, "propellant": {"label": "Propellant"}, "primaryColour": {"label": "Primary Colour"}, "secondaryColour": {"label": "Secondary Colour"}, "status": {"label": "Vehicle Status"}, "scheme": {"label": "Vehicle Scheme"}, "vehicleContractEnd": {"label": "Vehicle Contract End (YYYY/MM)"}, "potentialReplacement": {"label": "Potential Replacement"}, "vin": {"label": "Porsche Vehicle Identifier"}}}, "additionalInformation": {"title": "Additional Information"}, "appointmentDetails": {"title": "Appointment Details", "fields": {"appointmentId": {"label": "Appointment ID"}, "appointmentDate": {"label": "Appointment Date"}, "appointmentTime": {"label": "Appointment Time {{offset}}"}, "assignee": {"label": "Assignee"}}}, "mobilityMainDetails": {"title": "Main Details", "fields": {"applicationId": {"label": "Booking ID"}, "createIn": {"label": "Date Created {{offset}}"}, "variantName": {"label": "Variant Name"}, "rentalPeriodStart": {"label": "Start Date/Time"}, "rentalPeriodEnd": {"label": "End Date/Time"}, "location": {"label": "Pickup Location"}}}, "attachmentDetails": {"applicant": {"title": "Individual/Corporate Documents", "mobilityTitle": "Customer Documents"}, "guarantor": {"title": "Guarantor Documents"}}, "paymentDetails": {"title": "Payment Details", "fields": {"amount": {"label": "Payment Amount"}, "date": {"label": "Payment Date"}, "transactionId": {"label": "Transaction ID"}, "status": {"label": "Status"}, "method": {"label": "Payment Method"}}}, "applicantDetails": {"title": "Individual/Corporate Details", "mobilityTitle": "Customer Details", "guarantorTitle": "Guarantor Details", "subTitle": {"customerDetails": "Individual/Corporate Details", "mobilityCustomerDetails": "Customer Details", "guarantorDetails": "Guarantor Details", "addressDetails": "Address Details", "drivingLicense": "Driving License", "employmentDetails": "Employment Details", "referenceDetails": "Reference Details", "identityDetails": "Identity Details", "correspondenceAddress": "Correspondence Address", "corporateInformation": "Corporate Information", "others": "Others", "residentialAddress": "Residential Address"}, "fields": {"title": {"label": "Title"}, "nonBinaryTitle": {"label": "Title"}, "salutation": {"label": "Salutation"}, "salutationBmw": {"label": "Salutation-BMW"}, "lastNameFront": {"label": "Last Name (front)"}, "firstName": {"label": "First Name"}, "lastName": {"label": "Last Name"}, "lastNameJapan": {"label": "Last Name (Japan)"}, "firstNameJapan": {"label": "First Name (Japan)"}, "fullName": {"label": "Full Name"}, "birthday": {"label": "Date of Birth"}, "email": {"label": "Email"}, "phone": {"label": "Mobile No."}, "citizenship": {"label": "Citizenship"}, "nationality": {"label": "Nationality"}, "identityNumber": {"label": "Identity No."}, "passport": {"label": "Passport"}, "country": {"label": "Country"}, "postalCode": {"label": "Postal Code"}, "address": {"label": "Address"}, "unitNumber": {"label": "Unit Number"}, "id": {"label": "Customer ID"}, "nric": {"label": "NRIC"}, "mobile": {"label": "Mobile"}, "drivingLicence": {"label": "Driving Licence"}, "validity": {"label": "Validity"}, "expireDate": {"label": "Expire Date"}, "licenseClass": {"label": "Class"}, "telephone": {"label": "Telephone Number"}, "residentialStatusVwfs": {"label": "Residential Status"}, "otherResidentialStatusVwfs": {"label": "Others"}, "issueDate": {"label": "Issue Date"}, "district": {"label": "District"}, "road": {"label": "Road"}, "region": {"label": "Region"}, "city": {"label": "City"}, "timeOfAddress": {"label": "Time Of Address"}, "timeOfEmployment": {"label": "Time Of Employment"}, "correspondenceCity": {"label": "Correspondence City"}, "correspondenceDistrict": {"label": "Correspondence District"}, "correspondenceAddress": {"label": "Correspondence Address"}, "jobTitle": {"label": "Job Title"}, "otherJobTitle": {"label": "Others"}, "jobTitleTh": {"label": "Occupation"}, "otherJobTitleTh": {"label": "Others"}, "occupation": {"label": "Occupation"}, "employmentStatus": {"label": "Employment Status"}, "companyName": {"label": "Company Name"}, "companyPhoneticName": {"label": "Phonetic for Company Name"}, "companyCity": {"label": "Company City"}, "companyDistrict": {"label": "Company District"}, "companyAddress": {"label": "Company Address"}, "companyPhone": {"label": "Company Phone"}, "companyPhoneExtension": {"label": "Company Phone Extension"}, "businessTitle": {"label": "Business Title"}, "relationshipWithApplicant": {"label": "Relationship with Applicant"}, "otherRelationshipWithApplicant": {"label": "Others"}, "monthlyIncome": {"label": "Monthly Income"}, "otherIncome": {"label": "Other Income"}, "corporateName": {"label": "Corporate Name"}, "corporateIdentityNumber": {"label": "Corporate Identity Number"}, "corporateRegistrationDate": {"label": "Corporate Registration Date"}, "corporateIndustryCategory": {"label": "Corporate Industry Category"}, "corporateRegisteredCapital": {"label": "Corporate Registered Capital"}, "corporateAnnualRevenue": {"label": "Corporate Annual Revenue"}, "corporateNumberOfEmployee": {"label": "Corporate Number of Employee"}, "corporatePhone": {"label": "Corporate Phone"}, "noClaimDiscount": {"label": "No Claim Discount"}, "driverLicensePassDate": {"label": "Driver License Pass Date"}, "race": {"label": "Race"}, "gender": {"label": "Gender"}, "nonBinaryGender": {"label": "Gender"}, "maritalStatus": {"label": "Marital Status"}, "residentialStatus": {"label": "Residential Status"}, "education": {"label": "Education"}, "residenceType": {"label": "Residence Type"}, "addressType": {"label": "Address Type"}, "emirate": {"label": "Emirate"}, "incomeType": {"label": "Income Type"}, "dateOfJoining": {"label": "Date Of Joining"}, "preferredFirstPaymentDate": {"label": "Preferred First Payment Date"}, "referenceDetail": {"name": {"label": "Reference Name"}, "relationship": {"label": "Reference Relationship"}, "contactNumber": {"label": "Reference Contact Number"}}, "salaryTransferredBank": {"enabled": {"label": "Salary Transferred to Bank"}, "bankName": {"label": "Bank Name"}, "bankAccountNumber": {"label": "Bank Account Number"}}, "uaeIdSet": {"passportNumber": {"label": "Passport Number"}, "passportExpiryDate": {"label": "Passport Expiry"}, "emiratesIdNumber": {"label": "Emirates ID Number"}, "emiratesIdExpiryDate": {"label": "Emirates ID Expiry"}, "drivingLicenseNumber": {"label": "Driving License Number"}, "drivingLicenseExpiryDate": {"label": "Driving License Expiry"}, "uaeResidenceSince": {"label": "UAE Residence Since"}, "uaeVisaNumber": {"label": "UAE Visa Number"}, "uaeVisaIssueDate": {"label": "UAE Visa Issue Date"}, "uaeVisaExpiryDate": {"label": "UAE Visa Expiry Date"}}, "purchaseIntention": {"label": "How soon do you intend to purchase a car?"}, "comments": {"label": "Comments"}}}, "marketingPlatforms": {"email": "Email", "fax": "Fax", "mail": "Mail", "phone": "Phone", "sms": "SMS"}, "financingDetails": {"title": "Financing Details", "fields": {"carPrice": {"label": "Car Price"}, "coe": {"label": "COE"}, "ppsr": {"label": "PPSR"}, "estFee": {"label": "Dealer Establishment Fee"}, "bankEstFee": {"label": "{{bankLegalName}} Establishment Fee"}, "paymentMode": {"label": "Mode of Payment"}, "financialInstitution": {"label": "Financial Institution"}, "financeProduct": {"label": "Finance Product"}, "deposit": {"label": "Security Deposit"}, "tenure": {"label": "Tenure"}, "monthlyPayment": {"label": "Monthly Payment"}, "tradeInAmount": {"label": "Expected Trade-In Amount"}, "cashAfterTradeIn": {"label": "Remaining Cash Payment"}, "commentsToBank": {"label": "Comments to Bank"}, "bank": {"label": "Bank"}, "interestRate": {"label": "Interest Rate"}, "downPayment": {"label": "Downpayment"}, "downPaymentWithAdditional": {"label": "Downpayment"}, "loanAmount": {"label": "<PERSON><PERSON>"}, "promoCode": {"label": "Promo Code"}, "interestRateValue": {"flat": "{{value}}% p.a. flat"}, "tenureValue": {"term": "{{value}} Months"}, "licensePlateFee": {"label": "License Plate Fee"}, "commission": {"label": "Commission"}, "commissionValue": {"commission": "{{value}}%"}, "monthlyPaymentFixedRate": {"label": "Monthly Payment (Fixed Interest Rate)"}, "licenseAndFuelTax": {"label": "License and Fuel Tax"}, "displacement": {"label": "Displacement"}, "displacementValue": {"displacement": "{{value}}cc"}, "insuranceFee": {"label": "Insurance Fee"}, "taxLoss": {"label": "Tax Loss"}, "marginOfFinance": {"label": "Margin Of Finance"}, "hirePurchase": {"label": "HP Amount"}, "assuredResaleValue": {"label": "Assured Resale Value"}, "estimatedSurplusBalloon": {"label": "Estimated Surplus After Assured Resale Value"}, "mileage": {"label": "Mileage"}, "residualValue": {"label": "Residual Value"}, "balloonPayment": {"label": "Balloon Payment"}, "totalAmountPayable": {"label": "Total Amount Payable"}, "totalInterestPayable": {"label": "Total Interest Payable"}, "dealerOptions": {"label": "Dealer Options"}, "discount": {"label": "Discount"}, "discountDescription": {"label": "Discount Description"}, "affinAutoFinanceCentre": {"label": "Affin Bank Auto Finance Centre"}}, "paymentMethod": {"advance": "Advance", "arrears": "Arrears"}, "interestRate": {"flat": "{{value}}% p.a. flat", "effective": "{{value}}%"}}, "insurancingDetails": {"title": "Insurance Details", "fields": {"carPrice": {"label": "Car Price"}, "totalPrice": {"label": "Total Price"}, "commentsToInsurer": {"label": "Comments to Insurer"}, "insuranceProduct": {"label": "Insurance Product"}, "insurer": {"label": "Insurance Company"}, "dateOfRegistration": {"label": "Original Registration Date"}, "insurancePremium": {"label": "Insurance Premium"}, "coe": {"label": "COE"}, "ppsr": {"label": "PPSR"}, "estFee": {"label": "Dealer Establishment Fee"}, "bankEstFee": {"label": "{{bankLegalName}} Establishment Fee"}, "sumInsured": {"label": "Sum Insured"}}}, "quotationDetails": {"title": "Quotation Details", "subTitles": {"vehicle": "Vehicle"}, "fields": {"date": {"label": "Date"}, "model": {"label": "Porsche Model"}, "applicantName": {"label": "Full Name on Quotation"}, "commissionNumber": {"label": "Commission No."}, "engineNumber": {"label": "Engine No."}, "chassisNumber": {"label": "Chassis No."}, "exteriorColor": {"label": "Exterior Color"}, "companyName": {"label": "Company Name"}, "financeManager": {"label": "Finance Manager"}, "yearOfManufacture": {"label": "Year of Manufacture"}, "carPriceExcludeVat": {"label": "Car price (Exc. VAT)"}, "promoCode": {"label": "Less: {{ promoCode }}"}, "netCarPriceExcludeVat": {"label": "Net car price (Exc. VAT)"}, "accessoryExcludeVat": {"label": "{{label}} (Exc. VAT)"}, "accessoryIncludeVat": {"label": "{{label}} Inc. of VAT"}, "netVat": {"label": "Net VAT @{{vat}}%"}, "totalPriceIncludeVat": {"label": "Total Price Inc. of VAT"}, "downpaymentTo": {"label": "Downpayment to {{ target }}"}, "downpayment": {"label": "Downpayment"}, "balanceToBePaid": {"label": "Balance to be paid"}, "signature": {"label": "F&I Manager"}, "discount": {"label": "Less {{description}}"}}}, "shareHeaderInfo": {"customer": "Customer Name:", "salesConsultant": "Sales Consultant:", "date": "Date {{offset}}:"}, "optionDetails": {"title": "Options"}}, "insurance": {"dateOfBirthDisclaimer": "The date of birth updated here does not match the one provided in insurance premium calculator. The insurance premium provided is based on the previous date of birth provided, {{oldDateOfBirth}}. Your actual insurance premium will be reviewed and may be different.", "noValue": "N.A.^", "hint": "^Our insurance team will provide a quotation on your Porsche insurance."}, "suffix": {"mileage": "Mileage", "mileageUnit": "km"}}