{"title": "Activity Log", "addComment": "Add Comment", "fields": {"comment": {"label": "Comment"}}, "table": {"columns": {"title": "Date/Time (UTC{{offset}})", "activity": "Activity"}}, "application": {"applicationDrafted": "Draft is saved by {{ author }}", "applicationAmendments": "{{ updates }} by {{ author }}", "applicationSubmittedToSystem": "Application Submitted", "applicationResubmittedToSystem": "Application Resubmitted", "applicationFinancingRequested": "Financing Requested", "applicationCustomerAgreedOnCnd": "Consent Checked by {{ author }}", "applicationRemarksSubmitted": "Remarks: {{remarks}}", "applicationKYCReceived": "Applicant's Information Received by System", "applicationPaymentPending": "Payment Pending", "applicationPaymentCompleted": "Payment Completed", "applicationPaymentFailed": "Payment Failed", "applicationPaymentRefunded": "Payment Refunded", "applicationPaymentSkipped": "Payment is skipped by {{ author }}", "applicationPaymentCapturing": "Payment Submitted", "applicationSubmittedToBank": {"withoutReference": "Submitted to {{ bank }}", "withReference": "Submitted to {{ bank }}, reference {{ reference }}"}, "applicationResubmittedToBank": {"withoutReference": "Resubmitted to {{ bank }}", "withReference": "Resubmitted  to {{ bank }}, reference {{ reference }}"}, "applicationSubmissionToBankFailed": "Submission Failed to {{ bank }}, reason: {{ reason }}", "applicationResubmissionToBankFailed": "Resubmission Failed to {{ bank }}, reason: {{ reason }}", "applicationApproved": {"withAmount": "Approved by {{ author }} - {{ amount }}", "withoutAmount": "Approved by {{ author }}", "withDetails": "Approved by {{ author }}, amount: {{ amount }}, tenure: {{ tenure }} months, interest rate: {{ interestRate }} %", "withExtraDetails": "Approved by {{ author }}, amount: {{ amount }}, tenure: {{ tenure }} months, interest rate: {{ interestRate }} %, effective interest rate: {{ effectiveInterestRate }} %"}, "applicationCancelled": {"withoutReference": "Cancelled by {{ author }}", "withReference": "Cancelled by {{ author }}, reference {{ reference }}"}, "applicationCompleted": "Completed by {{ author }}", "applicationDeclined": "Declined by {{ author }}", "bankCustomerInfoReceived": {"withSource": "Applicant's Information Received({{ source }}) by {{ bank }}", "withoutSource": "Applicant's Information Received by {{ bank }}", "sources": {"myinfo": "Myinfo", "manual": "Manual", "amend": "Amend"}}, "bankReviewInProgress": {"withSubDescription": "Bank Review In Progress by {{ bank }} ({{ subDescription }})", "withoutSubDescription": "Bank Review In Progress by {{ bank }}"}, "reassignBankAssignee": {"success": "Reassign assignee request to {{ bank }} was successful", "fail": "Reassign assignee request to {{ bank }} failed"}, "applicationApplyForFinanceCreation": "Created from {{stage}} No. {{referenceIdentifier}}", "applicationShared": "Lead Shared by {{ author }}", "requestReleaseLetter": "Requested Release Letter by {{ author }}", "requestDisbursement": "Requested Disbursement by {{ author }}", "requestDisbursementFail": "Requested Disbursement Failed, reason: {{ reason }}", "sdmUpdateStatusFailed": "{{status}} status pushed failed to SDM SF, reason: {{reason}}", "sdmUpdateStatusSuccess": "{{status}} status pushed to SDM SF", "bmwUpdateStatusFailed": "status pushed failed to SDM AFC, reason: {{reason}}", "bmwUpdateStatusSuccess": "status pushed to SDM AFC", "applicationCancellationToBankFailed": "Cancellation Failed, reason: {{ reason }}", "applicationCancellationFailed": "Cancellation Failed, reason: {{ reason }}", "applicationPendingDisbursement": "Pending Disbursement", "applicationExpired": "Expired", "applicationPendingInfoFromCustomer": "Pending info from customer", "applicationEmailSent": "Email sent to {{recipientType}}: {{recipient}} by System", "applicationCommonEmailSent": "Email sent to {{recipientType}} by System", "applicationCheckIn": "Application Checked in by {{ author }}", "applicationAppointmentMade": "Appointment Made by {{ author }}", "applicationAppointmentEndTestDriveReminder": "Email (Reminder to End Test Drive) sent to Sales: {{ recipient }}", "applicationSubmittedToInsuranceCompany": {"withoutReference": "Submitted to Insurance Company {{ insurerName }}", "withReference": "Submitted to Insurance Company {{ insurerName }}, reference {{ reference }}"}, "applicationSubmissionToInsuranceCompanyFailed": "Submission to Insurance Company {{ insurerName }} Failed, reason: {{ reason }}", "insuranceCompanyReviewInProgress": {"withSubDescription": "Insurance Company Review In Progress by {{ bank }} ({{ subDescription }})", "withoutSubDescription": "Insurance Company Review In Progress by {{ bank }}"}, "applicationApplyForInsuranceCreation": "Created from {{stage}} No. {{referenceIdentifier}}", "applicationApplyForReservationCreation": "Created from {{stage}} No. {{referenceIdentifier}}", "applicationSigningTimeout": "Signing Timeout", "bookingSubmitted": "Booking Submitted", "bookingAmended": "Booking Amended", "bookingConfirmationEmailSent": "Booking Confirmation Email sent to {{recipientType}} {{recipient}}", "bookingCancellationEmailSent": "Booking Cancellation Email sent to {{recipientType}} {{recipient}}", "bookingAmendmentEmailSent": "Booking Amendment Email sent to {{recipientType}} {{recipient}}", "reminderEmailSent": "Reminder <PERSON><PERSON> sent to {{recipientType}} {{recipient}}", "applicationContacted": "Contacted by {{author}}", "bookingCompletionEmailSent": "Booking Completion Email sent to {{recipientType}} {{recipient}}", "applicationComment": "Comment added by {{ author }}:\n{{ comment }}", "applicationOTPCompleted": "OTP completed by {{author}}", "applicationOTPCompletedWithIp": "OTP completed by {{author}} (IP address: {{IPAddress}})", "applicationOTPInitiated": "OTP initiated by {{author}}", "applicationSigningCompleted": "Signing completed by {{author}}", "applicationSigningInitiated": "Signing Initiated by {{author}}", "applicationSigningRejected": "Signing rejected by {{author}}", "agreementConcluded": "Agreement concluded by {{author}}", "applicationConfirmedBookingAuditTrail": "Booking confirmed by {{author}}", "applicationTestDriveStartedAuditTrail": "Started test drive by {{author}}", "applicationTestDriveEndedAuditTrail": "Ended test drive by {{author}}", "applicationTestDriveOTPCompleted": "OTP completed by {{author}}", "applicationTestDriveOTPCompletedWithIp": "OTP completed by {{author}} (IP address: {{IPAddress}})", "applicationTestDriveOTPInitiated": "OTP initiated by {{author}}", "applicationTestDriveSigningCompleted": "Signing completed by {{author}}", "applicationTestDriveSigningInitiated": "Signing Initiated by {{author}}", "applicationTestDriveSigningRejected": "Signing rejected by {{author}}", "applicationTestDriveKYCAuditTrail": "Test drive customer information received by System", "applicationTestDriveCnDAuditTrail": "Test drive consent checked by {{author}}", "applicationShowroomVisitCnDAuditTrail": "Showroom Visit consent checked by {{author}}", "applicationProceedWithCustomerDevice": "Proceeded to customer device by {{ author }}", "insuranceApplicationApproved": "Approved by {{ author }}", "insuranceApplicationCancelled": "Cancelled by {{ author }}", "insuranceApplicationDeclined": "Declined by {{ author }}", "capBPIsExist": "Business Partner ID: {{id}} is Found on C@P", "capBPCreated": {"success": "Business Partner is successfully created with Business Partner ID: {{id}}", "failed": "Unable to create Business Partner: {{errorMessage}}"}, "capBPUpdated": {"success": "Business Partner with ID: {{id}} is successfully updated", "failed": "Unable to update Business Partner with ID {{id}}: {{errorMessage}}"}, "capCompetitorVehicleCreated": {"success": "Competitor vehicle for Business Partner ID: {{id}} has successfully submitted", "failed": "Unable to submit competitor vehicle for Business Partner ID {{id}}: {{errorMessage}}"}, "capCompetitorVehicleUpdated": {"success": "Competitor vehicle for Business Partner ID: {{id}} has successfully updated", "failed": "Unable to update competitor vehicle for Business Partner ID {{id}}: {{errorMessage}}"}, "capLeadIsExist": "Lead ID: {{id}} is Found on C@P", "capLeadCampaignNotFound": "Campaign not found", "unableToGetCapCampaignDetails": "Unable to get Campaign Details of {{id}}: {{errorMessage}}", "capLeadCreated": {"success": "Lead is successfully created with Lead ID: {{id}}", "failed": "Unable to create Lead: {{errorMessage}}"}, "capLeadUpdated": {"success": "Lead with ID: {{id}} is successfully updated", "failed": "Unable to update Lead with ID {{id}}: {{errorMessage}}"}, "capActivitySubmitted": {"success": "Activity is successfully submitted with Activity ID: {{id}}", "failed": "Unable to submit Activity: {{errorMessage}}"}, "capActivityEndTestDriveSubmitted": {"success": "Activity is successfully submitted after the test drive ended with Activity ID: {{id}}", "failed": "Unable to submit Activity after the test drive ended: {{errorMessage}}"}, "capActivityPlannedTestDriveSubmitted": {"success": "Activity is successfully submitted after the test drive is confirmed with Activity ID: {{id}}", "failed": "Unable to submit Activity after the test drive confirmed: {{errorMessage}}"}, "capActivityPlannedShowroomVisitSubmitted": {"success": "Activity is successfully submitted after the showroom visit is confirmed with Activity ID: {{id}}", "failed": "Unable to submit Activity after the showroom visit confirmed: {{errorMessage}}"}, "capActivityCompleteShowroomVisitSubmitted": {"success": "Activity is successfully submitted after the showroom visit completed with Activity ID: {{id}}", "failed": "Unable to submit Activity after the showroom visit completed: {{errorMessage}}"}, "capConsentNotFound": "Consent for Partner ID: {{id}} is not found", "capConsentSubmitted": {"success": "Consent for Partner ID: {{id}} is successfully submitted", "failed": "Unable to submit Consent: {{errorMessage}}"}, "capCustomerAttributeCreated": {"success": "Customer attribute is successfully created with ID: {{id}}", "failed": "Unable to create Customer attribute: {{errorMessage}}"}, "capCustomerAttributeUpdated": {"success": "Customer attribute is successfully updated with ID: {{id}}", "failed": "Unable to update Customer attribute: {{errorMessage}}"}, "capCustomerAttributeDeleted": {"success": "Customer attribute is successfully deleted with ID: {{id}}", "failed": "Unable to delete Customer attribute: {{errorMessage}}"}, "capQualified": "Qualified by {{author}}", "capUnqualified": "Unqualified by {{author}}", "capSubmittedWithError": "Submitted to C@P with Errors", "capSubmissionFailed": "Submission to C@P Failed", "capBPSearchFailed": "Unable to search Business Partner: {{errorMessage}}", "capLeadSearchFailed": "Unable to search Lead: {{errorMessage}}", "capCustomerAttributeSearchFailed": "Unable to search Customer attribute: {{errorMessage}}", "defaultCapError": {"authFailed": "Failed to authenticating with PPN", "createActivity": "Catch error when create Activity", "createBusinessPartner": "Catch error when create BP", "updateBusinessPartner": "Catch error when update BP", "searchBusinessPartner": "Catch error when search BP", "businessPartnerDetailsNotFound": "Business Partner details not found", "createCustomerCompetitorVehicle": "Catch error when create customer competitor vehicle", "updateCustomerCompetitorVehicle": "Catch error when update customer competitor vehicle", "createConsent": "Catch error when submit consent", "searchConsent": "Catch error when search consent", "createLead": "Catch error when create lead", "updateLead": "Catch error when update Lead", "searchLead": "Catch error when search Lead", "leadDetailsNotFound": "Lead details not found", "createCustomerAttribute": "Catch error when create customer attribute", "getCustomerAttribute": "Catch error when get customer attribute", "searchCampaign": "Catch error when get campaign details"}, "missingCapValue": {"createActivity": "Required value for submitting the activity is missing", "createBusinessPartner": "Required value for submitting the business partner is missing", "updateBusinessPartner": "Required value for updating the business partner is missing", "searchBusinessPartner": "Required value for getting the business partner is missing", "createCustomerCompetitorVehicle": "Required value for submitting the customer competitor vehicle is missing", "updateCustomerCompetitorVehicle": "Catch error when update customer competitor vehicle", "createConsent": "Required value for submitting the consent is missing", "searchConsent": "Required value for getting the consent is missing", "createLead": "Required value for submitting the lead is missing", "updateLead": "Required value for updating the lead is missing", "searchLead": "Required value for getting the lead is missing", "createCustomerAttribute": "Required value for submitting the customer attribute is missing", "getCustomerAttribute": "Required value for getting the customer attribute is missing", "searchCampaign": "Required value for getting the campaign details is missing"}}, "stock": {"stockUpdated": "Stock updated by {{ author }}: {{ updates }}"}, "formats": {"empty": "empty", "change": "{{ label }} changed from {{ from }} to {{ to }}"}, "author": {"system": "System", "customer": "Customer", "applicant": "Applicant", "guarantor": "Guarant<PERSON>", "bank": "Bank", "insurer": "Insurer", "user": "User", "scUser": "SC/User", "porscheRetain": "Porsche Retain", "salesforce": {"audi": "Audi SF", "sdm": "SDM SF"}}, "inventory": {"inventoryComment": "Comment added by {{ author }}:\n{{ comment }}", "configuratorInventoryCreated": "Inventory created by {{ author }}", "mobilityInventoryCreated": "Inventory created by {{ author }}", "configuratorInventoryUpdated": "Amended inventory {{ field }} from {{ oldValue }} to {{ newValue }} by {{ author }}", "configuratorInventoryUpdated_add": "Added inventory {{ field }} to {{ newValue }} by {{ author }}", "configuratorInventoryUpdated_remove": "Removed inventory {{ field }} from {{ oldValue }} by {{ author }}", "mobilityInventoryUpdated": "Amended inventory {{ field }} from {{ oldValue }} to {{ newValue }} by {{ author }}", "mobilityInventoryUpdated_add": "Added inventory {{ field }} to {{ newValue }} by {{ author }}", "mobilityInventoryUpdated_remove": "Removed inventory {{ field }} from {{ oldValue }} by {{ author }}", "configuratorInventoryDeleted": "Inventory deleted by {{ author }}", "mobilityInventoryDeleted": "Inventory deleted by {{ author }}", "inventoryStockAdded": "Added stocks {{first}} to {{ last }} by {{ author }}", "inventoryStockDeleted": "Deleted stocks {{ first }} to {{ last }} by {{ author }}", "inventoryStockAdded_single": "Added stock {{first}} by {{ author }}", "inventoryStockDeleted_single": "Deleted stock {{ first }} by {{ author }}", "configuratorInventoryStockUpdated": "Amended stock ID {{identifier}} {{ field }} from {{ oldValue }} to {{ newValue }} by {{ author }}", "configuratorInventoryStockUpdated_add": "Added stock ID {{identifier}} {{ field }} to {{ newValue }} by {{ author }}", "configuratorInventoryStockUpdated_remove": "Removed stock ID {{identifier}} {{ field }} from {{ newValue }} by {{ author }}", "mobilityInventoryStockUpdated": "Amended stock ID {{identifier}} {{ field }} from {{ oldValue }} to {{ newValue }} by {{ author }}", "mobilityInventoryStockUpdated_add": "Added stock ID {{identifier}} {{ field }} to {{ newValue }} by {{ author }}", "mobilityInventoryStockUpdated_remove": "Removed stock ID {{identifier}} {{ field }} from {{ oldValue }} by {{ author }}", "stockAmendments": "Amended stock ID {{identifier}} {{ field }} from {{ oldValue }} to {{ newValue }} by {{ author }}", "stockAmendments_add": "Added stock ID {{identifier}} {{ field }} to {{ newValue }} by {{ author }}", "stockAmendments_remove": "Removed stock ID {{identifier}} {{ field }} from {{ oldValue }} by {{ author }}", "inventoryAmendments": "Amended inventory {{ field }} from {{ oldValue }} to {{ newValue }} by {{ author }}", "inventoryAmendments_add": "Added inventory {{ field }} to {{ newValue }} by {{ author }}", "inventoryAmendments_remove": "Removed inventory {{ field }} from {{ oldValue }} by {{ author }}"}, "customer": {"customerAmendmentsWithStage": "{{ updates }} by {{ author }} in {{stage}}", "customerAmendments": "{{ updates }} by {{ author }}", "customerComment": "Comment added by {{ author }}:\n{{ comment }}"}, "tradeIn": {"tradeInDraftRequestReceived": "Trade-in Request Received from Salesforce", "tradeInDraftRequestSent": "Trade-in Request Sent to Trade-in", "tradeInDraftRequestSendingFailed": "Trade-in Request Sent to Trade-in Failed - {{ reason }}", "tradeInDrafted": "Draft Created in Trade-in", "tradeInBidCloseRequestReceived": "<PERSON><PERSON> Closed Received from Trade-in", "tradeInBidCloseRequestSent": "Bid Closed <PERSON><PERSON> to Salesforce", "tradeInBidCloseRequestSendingFailed": "B<PERSON> <PERSON>d <PERSON><PERSON> to Salesforce Failed - {{ reason }}", "tradeInConfirmationRequestReceived": {"awarded": "Awarded <PERSON><PERSON> Received from Salesforce", "notAwarded": "Not Awarded Received from Salesforce"}, "tradeInConfirmationRequestSent": {"awarded": "Awarded <PERSON><PERSON> to Trade-in", "notAwarded": "Not Awarded <PERSON><PERSON> to Trade-in"}, "tradeInConfirmationRequestSendingFailed": {"awarded": "Awarded <PERSON><PERSON> to Trade-in Failed - {{ reason }}", "notAwarded": "Not Awarded Sent to Trade-in Failed - {{ reason }}"}, "tradeInComment": "Comment added by {{ author }}:\n{{ comment }}"}, "leads": {"leadDrafted": "Contact draft saved by {{ author }}", "leadSubmittedToSystem": "Contact submitted", "leadShared": "Lead shared by {{ author }}", "leadQualified": "Qualified by {{ author }}", "leadUnqualified": "Unqualified by {{ author }}", "leadContacted": "Marked as Contacted by {{ author }}", "leadLost": "Marked as Lost by {{ author }}", "leadCompleted": "Completed by {{ author }}", "leadAmended": "{{ updates }} by {{ author }}", "leadSubmittedWithError": "Submitted to C@P with Errors", "leadSubmissionFailed": "Submission to C@P Failed", "leadEmailSent": "Email sent to {{recipientType}}: {{recipient}} by System", "leadCommonEmailSent": "Email sent to {{recipientType}} by System", "leadIntentAndAssign": "Lead intent updated and assigned by {{ author }}", "leadFollowedUp": "Follow-Up is submitted by {{ author }}, scheduled on {{ scheduledDate }}", "leadFollowedUpWithRemarks": "Follow-Up is submitted by {{ author }}, scheduled on {{ scheduledDate }}, remarks: {{remarks}}", "leadFollowUpCancelled": "Follow-Up is cancelled by {{ author }}", "leadFollowUpConfirmed": "Follow-Up is confirmed by {{ author }}", "leadFollowUpUpdated": "Follow-Up is updated by {{ author }}, scheduled on {{ scheduledDate }}", "leadFollowUpUpdatedWithRemarks": "Follow-Up is updated by {{ author }}, scheduled on {{ scheduledDate }}, remarks: {{remarks}}", "leadTestDriveCreated": "Test drive booking created by {{ author }}", "leadShowroomVisitBooked": "Showroom visit booked by {{ author }}", "leadStockAssigned": "Stock assigned by {{ author }}", "leadCustomerAgreedOnCnd": "Consent Checked by {{ author }}", "leadKYCReceived": "Applicant's Information Received by System", "leadCreatedByPorscheRetain": "Lead Created By <PERSON> Retain", "leadUpdatedByPorscheRetain": "Lead Updated By Porsche Retain", "leadMergedWithContactId": "Contact: {{ sourceId }} is merged into Lead: {{ targetId }} by {{ author }}", "leadMergedWithoutContactId": "Lead: {{ id }} is updated by {{ author }}", "updates": {"success": "successfully", "failed": "with errors"}}, "salesOffer": {"porscheCodeRetrieved": "Porsche Code {{ code }} retrieved", "featureSent": "{{ feature }} sent", "documentSigned": "{{ feature }} signed", "documentCompleted": "{{ feature }} completed", "optionAdded": "Option {{ description }} added", "optionDeleted": "Option {{ description }} deleted", "optionUpdated": "Option {{ label }} changed from {{ from }} to {{ to }}", "tabUpdated": "{{ tab }} updated", "tabAmended": "{{ tab }}: {{ updates }}"}}