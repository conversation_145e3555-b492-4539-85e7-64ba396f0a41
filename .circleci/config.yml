version: 2.1

orbs:
  aws-cli: circleci/aws-cli@3.1.4
  aws-eks: circleci/aws-eks@2.2.0
  helm: circleci/helm@1.2.0
  codecov: codecov/codecov@3.2.4

parameters:
  action:
    type: enum
    enum: [ "auto", "validation", "testing" ]
    default: auto

partials:
  prepare-minio: &prepare-minio |
    curl https://dl.min.io/client/mc/release/linux-amd64/mc --output ./mc
    chmod +x ./mc
    ./mc alias set local http://$APP_STORAGE_ENDPOINT:$APP_STORAGE_PORT $APP_STORAGE_ACCESS_KEY $APP_STORAGE_SECRET_KEY
    ./mc mb local/app-private
    ./mc mb local/app-public
    ./mc policy set public local/app-public
  main-env: &main-env
    DOTENV_DISABLE: true
    APP_DB_URI: ***************************************
    APP_SESSION_SECRET: localSecret
    APP_SMTP_PORT: 1025
    APP_STORAGE_ENDPOINT: localhost
    APP_STORAGE_PORT: 9000
    APP_STORAGE_SSL: false
    APP_STORAGE_ACCESS_KEY: 'AKIAIOSFODNN7EXAMPLE'
    APP_STORAGE_SECRET_KEY: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY'
    APP_STORAGE_PRIVATE_BUCKET: app-private
    APP_STORAGE_PUBLIC_BUCKET: app-public
    APP_STORAGE_PUBLIC_ENDPOINT: 'http://localhost:9000/app-public/'
    APP_HTML2PDF_ENDPOINT: 'http://html2pdf:3000/'
    APP_HEALTH_ENABLED: false
  html2pdf-env: &html2pdf-env
    BODY_LIMIT: 5mb
  minio-env: &mino-env
    MINIO_ROOT_USER: 'AKIAIOSFODNN7EXAMPLE'
    MINIO_ROOT_PASSWORD: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY'
  mongo-env: &mongo-env
    MONGO_INITDB_ROOT_USERNAME: root
    MONGO_INITDB_ROOT_PASSWORD: password

commands:
  install-deps:
    description: Install dependencies
    parameters:
      persist-cache:
        type: boolean
        default: false
    steps:
      - restore_cache:
          keys:
            - yarn4-cache-{{ checksum "yarn.lock" }}
            - yarn4-cache
      - run:
          name: Install dependencies
          command: yarn install --immutable
      - when:
          condition: << parameters.persist-cache >>
          steps:
            - save_cache:
                key: yarn4-cache-{{ checksum "yarn.lock" }}
                paths:
                  - ~/project/.yarn

executors:
  node-standalone:
    docker:
      - image: cimg/node:20.19.3
  cypress-standalone:
    docker:
      - image: cypress/browsers:node-20.18.0-chrome-130.0.6723.69-1-ff-131.0.3-edge-130.0.2849.52-1
        environment: *main-env
  cypress:
    docker:
      - image: cypress/browsers:node-20.18.0-chrome-130.0.6723.69-1-ff-131.0.3-edge-130.0.2849.52-1
        environment: *main-env
      - image: mongo:6.0.3-focal
        environment: *mongo-env
      - image: minio/minio:RELEASE.2022-05-26T05-48-41Z
        command: server /data
        environment: *mino-env
      - image: cimg/redis:7.0.8
        command: redis-server --appendonly yes
      - image: appvantage/html2pdf-service:1.7.2
        name: html2pdf
        environment: *html2pdf-env
      - image: mailhog/mailhog:v1.0.1
        name: mailhog
  node-with-dependencies:
    docker:
      - image: cimg/node:20.19.3
        environment: *main-env
      - image: mongo:6.0.3-focal
        environment: *mongo-env
      - image: minio/minio:RELEASE.2021-07-08T01-15-01Z
        command: server /data
        environment: *mino-env
      - image: cimg/redis:7.0.8
        command: redis-server --appendonly yes
      - image: appvantage/html2pdf-service:1.7.2
        name: html2pdf
        environment: *html2pdf-env

jobs:
  validate-code:
    executor: node-standalone
    resource_class: large
    environment:
      <<: *main-env
      CACHE_MODE: "filesystem"
    steps:
      - checkout
      - install-deps:
          persist-cache: true
      - run:
          name: Lint source code
          command: yarn lint --quiet --format junit -o ./junit/js-lint-results.xml
          environment:
            NODE_OPTIONS: "--max-old-space-size=8192"
      - run:
          name: Type checking
          command: yarn tsc
          environment:
            NODE_OPTIONS: "--max-old-space-size=8192"
      - run:
          name: Type checking on cypress
          command: yarn tsc
          working_directory: ~/project/cypress
          environment:
            NODE_OPTIONS: "--max-old-space-size=8192"
      - run:
          name: Lint commit messages
          command: yarn ts-node ./devtools/commands/lint-commits.ts
      - store_test_results:
          path: ~/project/junit/

  build:
    executor: node-standalone
    resource_class: xlarge
    steps:
      - checkout
      - install-deps
      - run:
          name: Build
          command: yarn build
          environment:
            CACHE_MODE: filesystem
            NODE_ENV: production
            NODE_OPTIONS: "--max-old-space-size=8192"
      - run:
          name: Prepare dependencies
          working_directory: ./build
          command: |
            cp -R ../.yarn ../.yarnrc.yml ../yarn.lock .
            yarn up --mode=skip-build
            yarn cache clean
            rm -rf ./node_modules
      - persist_to_workspace:
          root: ~/project
          paths:
            - build
      - store_artifacts:
          path: ~/project/report.html
          destination: report.html

  prerelease:
    executor: node-standalone
    resource_class: large
    environment:
      AWS_DEFAULT_REGION: "ap-southeast-1"
    steps:
      - checkout
      - install-deps
      - setup_remote_docker:
          version: docker23
      - aws-cli/setup:
          role-arn: "arn:aws:iam::059692869964:role/CircleCI_PDFS_PreReleaseJobRole"
          role-session-name: prerelease-$CIRCLE_WORKFLOW_JOB_ID
      - run:
          name: AWS ECR Login
          command:
            aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin 059692869964.dkr.ecr.ap-southeast-1.amazonaws.com
      - aws-eks/update-kubeconfig-with-authenticator:
          install-kubectl: true
          kubectl-version: v1.23.0
          aws-region: ap-southeast-1
          cluster-name: pfs-cluster
      - helm/install-helm-client:
          version: v3.8.2
      - run:
          name: Release & Deploy RC
          command: yarn prerelease publish
          environment:
            NODE_OPTIONS: "--max-old-space-size=8192"

  release:
    executor: node-standalone
    resource_class: medium+
    environment:
      AWS_DEFAULT_REGION: "ap-southeast-1"
    steps:
      - checkout
      - install-deps
      - attach_workspace:
          at: ~/project
      - setup_remote_docker:
          version: docker23
      - aws-cli/setup:
          role-arn: "arn:aws:iam::059692869964:role/CircleCI_PDFS_ReleaseJobRole"
          role-session-name: release-$CIRCLE_WORKFLOW_JOB_ID
      - run:
          name: AWS ECR Login
          command:
            aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin 059692869964.dkr.ecr.ap-southeast-1.amazonaws.com
      - run:
          name: semantic-release
          command: yarn semantic-release
          environment:
            NODE_OPTIONS: "--max-old-space-size=6144"

  deploy:
    executor: node-standalone
    resource_class: medium
    parameters:
      role-arn:
        description: The ARN of the role to assume
        type: string
      namespace:
        description: Namespace the release is in
        type: string
      release-name:
        description: Name of the release
        type: string
      aws-region:
        description: AWS Region
        type: string
        default: ap-southeast-1
      cluster-name:
        description: Name of the cluster
        type: string
    environment:
      AWS_DEFAULT_REGION: << parameters.aws-region >>
    steps:
      - checkout
      - aws-cli/setup:
          role-arn: << parameters.role-arn >>
          role-session-name: deploy-$CIRCLE_WORKFLOW_JOB_ID
      - aws-eks/update-kubeconfig-with-authenticator:
          install-kubectl: true
          kubectl-version: v1.23.0
          aws-region: << parameters.aws-region >>
          cluster-name: << parameters.cluster-name >>
      - helm/install-helm-client:
          version: v3.8.2
      - helm/upgrade-helm-chart:
          chart: ./charts/afc
          reuse-values: true
          namespace: << parameters.namespace >>
          release-name: << parameters.release-name >>
          values-to-override: app.image.tag=${CIRCLE_TAG:1},app.global.publicPath=https://d38gasgcn7qyrv.cloudfront.net/${CIRCLE_TAG:1}
          update-repositories: false

workflows:
  version: 2

  release-staging:
    when:
      matches:
        pattern: "^v*.*.*-next.*$"
        value: << pipeline.git.tag >>
    jobs:
      - deploy:
          name: release staging
          context:
            - arc-pdfs-dev
          namespace: arc-pdfs-dev
          release-name: arc-pdfs-dev
          cluster-name: pfs-cluster
          role-arn: "arn:aws:iam::059692869964:role/CircleCI_PDFS_StagingDeploymentJob"
          # CircleCI does not run workflows for tags unless you explicitly specify tag filters
          filters:
            tags:
              only:
                - /.*/

  validation:
    when:
      equal: [ "validation", << pipeline.parameters.action >> ]
    jobs:
      - validate-code

  main:
    when:
      equals: [ "auto", << pipeline.parameters.action >> ]
    jobs:
      - validate-code
      - release-approval:
          requires:
            - validate-code
          filters:
            branches:
              only:
                - /([0-9])+?.([0-9])+?.x/
                - next
                - latest
          type: approval
      - build:
          requires:
            - release-approval
      - release:
          context:
            - pdfs-releasing
          requires:
            - build
      - prerelease-approval:
          type: approval
          requires:
            - validate-code
          filters:
            branches:
              only:
                - /rc\/([a-zA-Z0-9-])+/
      - prerelease:
          context:
            - pdfs-releasing
          requires:
            - prerelease-approval
